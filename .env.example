# 环境变量配置模板
# 复制此文件为 .env 并填入实际的API密钥

# ========== 执行模式 ==========
# 可选值: "deep_research" 或 "full_analysis"
EXECUTION_MODE=deep_research

# ========== LLM配置 ==========
# 默认LLM提供商: "openai", "openai-compatible", "anthropic", "gemini"
DEFAULT_LLM_PROVIDER=openai

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
# 可选：自定义OpenAI API端点（如Azure OpenAI）
OPENAI_BASE_URL=

# OpenAI兼容API配置（用于本地部署或第三方兼容服务）
OPENAI_COMPATIBLE_BASE_URL=http://localhost:8000/v1
OPENAI_COMPATIBLE_API_KEY=dummy-key
OPENAI_COMPATIBLE_MODEL=gpt-3.5-turbo

# Anthropic API配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Google Gemini API配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-pro

# 多模型代理配置 - 允许不同代理使用不同模型
PLANNER_AGENT_PROVIDER=openai
SYNTHESIZER_AGENT_PROVIDER=gemini
WRITER_AGENT_PROVIDER=anthropic

# 代理特定模型配置 (留空使用默认模型)
PLANNER_AGENT_MODEL=
SYNTHESIZER_AGENT_MODEL=
WRITER_AGENT_MODEL=

# LLM参数 - 全局默认值
DEFAULT_LLM_TEMPERATURE=0.7
DEFAULT_LLM_MAX_TOKENS=8000

# 代理特定参数
PLANNER_TEMPERATURE=0.7
PLANNER_MAX_TOKENS=6000

SYNTHESIZER_TEMPERATURE=0.7
SYNTHESIZER_MAX_TOKENS=12000

WRITER_TEMPERATURE=0.7
WRITER_MAX_TOKENS=16000

# ========== 搜索API配置 ==========
# NASA ADS API (必需)
ADS_API_TOKEN=yyRCiynKlI2TXo3eUoJI0Ojd38J3LWH5dtrbLrp2

# 网络搜索提供商: "tavily" 或 "google"
SEARCH_PROVIDER=tavily

# Tavily Search API (推荐)
TAVILY_API_KEY=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

# Google Custom Search API (备选)
GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_custom_search_engine_id_here

# ========== 搜索参数 ==========
WEB_SEARCH_RESULTS_PER_QUERY=5

# 增强的ADS搜索配置
ADS_PAPERS_PER_SUBQUESTION=100
ADS_TOP_CITED_COUNT=30
ADS_RECENT_PAPERS_COUNT=30

# 论文分析配置
PAPERS_PER_ANALYSIS_BATCH=5
MAX_PAPERS_TO_ANALYZE=50
PAPER_BATCH_SIZE=5

# ========== 请求配置 ==========
REQUEST_TIMEOUT=30
MAX_RETRIES=3
REQUEST_DELAY=1.0

# ========== 日志配置 ==========
LOG_LEVEL=INFO
LOG_FILE=research_assistant.log

# ========== 高级配置 ==========
# 是否启用详细日志
VERBOSE_LOGGING=false

# 是否保存中间结果
SAVE_INTERMEDIATE_RESULTS=true

# 论文去重相似度阈值 (0-1)
PAPER_SIMILARITY_THRESHOLD=0.8

# 网络搜索结果过滤关键词 (逗号分隔)
SEARCH_FILTER_KEYWORDS=

# 是否启用缓存
ENABLE_CACHE=false
CACHE_DIR=cache

# 并发处理配置
MAX_CONCURRENT_REQUESTS=3

# 输出格式配置
OUTPUT_LANGUAGE=zh
INCLUDE_CITATIONS=true
