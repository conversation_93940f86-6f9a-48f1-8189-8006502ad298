# 贡献指南 (Contributing Guide)

感谢您对AI驱动的科研助理项目的关注！我们欢迎各种形式的贡献。

## 目录

- [如何贡献](#如何贡献)
- [开发环境设置](#开发环境设置)
- [代码规范](#代码规范)
- [提交流程](#提交流程)
- [测试要求](#测试要求)
- [文档贡献](#文档贡献)
- [问题报告](#问题报告)

---

## 如何贡献

### 贡献类型

我们欢迎以下类型的贡献：

- 🐛 **Bug修复**: 修复已知问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档改进**: 完善文档和示例
- 🧪 **测试增强**: 添加或改进测试用例
- 🎨 **代码优化**: 性能优化和代码重构
- 🌐 **国际化**: 多语言支持
- 📦 **依赖更新**: 更新第三方依赖

### 贡献流程

1. **Fork项目** 到你的GitHub账户
2. **创建分支** 用于你的修改
3. **进行开发** 并确保代码质量
4. **运行测试** 确保所有测试通过
5. **提交Pull Request** 描述你的修改

---

## 开发环境设置

### 1. 克隆项目

```bash
# Fork项目后克隆你的fork
git clone https://github.com/your-username/AI-Research-Assistant.git
cd AI-Research-Assistant

# 添加上游仓库
git remote add upstream https://github.com/original-repo/AI-Research-Assistant.git
```

### 2. 设置开发环境

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装开发依赖
pip install -r requirements.txt
pip install -e .  # 以开发模式安装项目

# 安装开发工具
pip install black flake8 pytest pytest-cov pre-commit
```

### 3. 配置开发环境

```bash
# 复制环境变量模板
cp .env.example .env.dev

# 编辑开发配置
nano .env.dev
```

开发环境配置示例：
```bash
# 开发模式配置
LOG_LEVEL=DEBUG
VERBOSE_LOGGING=true
EXECUTION_MODE=deep_research

# 测试API密钥（使用测试账户）
ADS_API_TOKEN=your_test_ads_token
OPENAI_API_KEY=your_test_openai_key
```

### 4. 验证环境

```bash
# 运行健康检查
python main.py --health

# 运行测试
python run_tests.py

# 检查代码风格
black --check .
flake8 .
```

---

## 代码规范

### Python代码风格

我们使用以下工具确保代码质量：

- **Black**: 代码格式化
- **Flake8**: 代码检查
- **isort**: 导入排序

### 代码格式化

```bash
# 格式化代码
black .

# 排序导入
isort .

# 检查代码风格
flake8 .
```

### 命名规范

- **文件名**: 使用小写字母和下划线 (`snake_case`)
- **类名**: 使用驼峰命名 (`PascalCase`)
- **函数名**: 使用小写字母和下划线 (`snake_case`)
- **常量**: 使用大写字母和下划线 (`UPPER_CASE`)
- **私有方法**: 以单下划线开头 (`_private_method`)

### 文档字符串

使用Google风格的文档字符串：

```python
def example_function(param1: str, param2: int) -> bool:
    """
    函数的简短描述。
    
    更详细的描述（如果需要）。
    
    Args:
        param1: 参数1的描述
        param2: 参数2的描述
        
    Returns:
        返回值的描述
        
    Raises:
        ValueError: 在什么情况下抛出此异常
    """
    pass
```

### 类型注解

使用类型注解提高代码可读性：

```python
from typing import List, Dict, Optional, Union

def process_papers(papers: List[Paper]) -> Dict[str, Any]:
    """处理论文列表并返回结果字典。"""
    pass
```

---

## 提交流程

### 1. 创建功能分支

```bash
# 确保主分支是最新的
git checkout main
git pull upstream main

# 创建新分支
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/issue-number-description
```

### 2. 进行开发

- 保持提交的原子性（每个提交只做一件事）
- 写清晰的提交信息
- 经常提交，避免大的提交

### 3. 提交信息规范

使用以下格式的提交信息：

```
类型(范围): 简短描述

详细描述（如果需要）

Fixes #issue-number
```

类型包括：
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(agents): 添加论文相似度检测功能

实现了基于标题和摘要的论文相似度检测，
避免重复分析相似的论文。

- 添加相似度计算算法
- 集成到论文处理流程中
- 添加相关配置选项

Fixes #123
```

### 4. 推送和创建PR

```bash
# 推送分支
git push origin feature/your-feature-name

# 在GitHub上创建Pull Request
```

### PR描述模板

```markdown
## 描述
简要描述这个PR的目的和修改内容。

## 修改类型
- [ ] Bug修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 代码重构
- [ ] 测试改进

## 测试
- [ ] 所有现有测试通过
- [ ] 添加了新的测试用例
- [ ] 手动测试通过

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的文档
- [ ] 更新了相关配置
- [ ] 没有引入破坏性变更

## 相关Issue
Fixes #issue-number
```

---

## 测试要求

### 运行测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试
python run_tests.py models
python run_tests.py agents

# 生成覆盖率报告
python run_tests.py --coverage
```

### 编写测试

1. **单元测试**: 测试单个函数或方法
2. **集成测试**: 测试组件间的交互
3. **端到端测试**: 测试完整的工作流程

测试文件命名规范：
- `test_*.py` 用于测试文件
- 测试类以 `Test` 开头
- 测试方法以 `test_` 开头

示例测试：
```python
import pytest
from models import Paper

class TestPaper:
    def test_paper_creation(self):
        """测试论文对象创建"""
        paper = Paper(title="Test Paper")
        assert paper.title == "Test Paper"
    
    def test_paper_year_extraction(self):
        """测试年份提取"""
        paper = Paper(title="Test", publication_date="2023-01-01")
        assert paper.get_year() == 2023
```

### 测试覆盖率

我们要求：
- 新代码的测试覆盖率至少80%
- 关键功能的测试覆盖率至少90%
- 所有公共API都有测试

---

## 文档贡献

### 文档类型

- **README.md**: 项目概述和快速开始
- **DEPLOYMENT.md**: 部署指南
- **API文档**: 代码中的文档字符串
- **用户指南**: 详细的使用说明
- **开发者文档**: 架构和设计文档

### 文档规范

1. **使用Markdown格式**
2. **保持简洁明了**
3. **提供实际示例**
4. **及时更新**

### 文档更新流程

```bash
# 更新文档
git checkout -b docs/update-readme
# 编辑文档文件
git add .
git commit -m "docs: 更新README中的安装说明"
git push origin docs/update-readme
# 创建PR
```

---

## 问题报告

### Bug报告

使用以下模板报告Bug：

```markdown
## Bug描述
清晰简洁地描述Bug。

## 复现步骤
1. 执行 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 期望行为
描述你期望发生什么。

## 实际行为
描述实际发生了什么。

## 环境信息
- 操作系统: [例如 Ubuntu 20.04]
- Python版本: [例如 3.9.0]
- 项目版本: [例如 v1.0.0]

## 附加信息
添加任何其他相关信息，如日志、截图等。
```

### 功能请求

```markdown
## 功能描述
清晰简洁地描述你想要的功能。

## 问题描述
描述这个功能要解决什么问题。

## 建议的解决方案
描述你希望如何实现这个功能。

## 替代方案
描述你考虑过的其他解决方案。

## 附加信息
添加任何其他相关信息。
```

---

## 代码审查

### 审查清单

作为审查者，请检查：

- [ ] 代码功能正确
- [ ] 代码风格符合规范
- [ ] 有适当的测试覆盖
- [ ] 文档已更新
- [ ] 没有安全问题
- [ ] 性能影响可接受

### 审查反馈

- 提供建设性的反馈
- 解释为什么需要修改
- 提供具体的改进建议
- 保持友好和专业的语调

---

## 发布流程

### 版本号规范

我们使用语义化版本控制 (SemVer)：

- `MAJOR.MINOR.PATCH`
- `MAJOR`: 不兼容的API修改
- `MINOR`: 向后兼容的功能性新增
- `PATCH`: 向后兼容的问题修正

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 版本号已更新
- [ ] 更新日志已编写
- [ ] 标签已创建

---

## 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境。请：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 沟通渠道

- **GitHub Issues**: 报告Bug和功能请求
- **GitHub Discussions**: 一般讨论和问答
- **Pull Requests**: 代码审查和讨论

---

## 获得帮助

如果你需要帮助：

1. 查看现有的文档和FAQ
2. 搜索已有的Issues
3. 在GitHub Discussions中提问
4. 创建新的Issue（如果是Bug或功能请求）

感谢你的贡献！🎉
