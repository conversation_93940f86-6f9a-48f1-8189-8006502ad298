# 部署指南 (Deployment Guide)

本文档提供了AI驱动的科研助理在不同环境中的部署指南。

## 目录

- [本地部署](#本地部署)
- [服务器部署](#服务器部署)
- [Docker部署](#docker部署)
- [云平台部署](#云平台部署)
- [性能优化](#性能优化)
- [监控与维护](#监控与维护)

---

## 本地部署

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.8 或更高版本
- **内存**: 至少 4GB RAM（推荐 8GB+）
- **存储**: 至少 2GB 可用空间
- **网络**: 稳定的互联网连接

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository_url>
   cd AI-Research-Assistant
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   # 或
   venv\Scripts\activate     # Windows
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入API密钥
   ```

5. **验证安装**
   ```bash
   python main.py --health
   ```

---

## 服务器部署

### Ubuntu/Debian 服务器

1. **系统更新**
   ```bash
   sudo apt update && sudo apt upgrade -y
   sudo apt install python3 python3-pip python3-venv git -y
   ```

2. **创建专用用户**
   ```bash
   sudo useradd -m -s /bin/bash research-assistant
   sudo su - research-assistant
   ```

3. **部署应用**
   ```bash
   git clone <repository_url>
   cd AI-Research-Assistant
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   ```bash
   cp .env.example .env
   nano .env  # 编辑配置文件
   ```

5. **创建系统服务（可选）**
   ```bash
   sudo nano /etc/systemd/system/research-assistant.service
   ```
   
   服务文件内容：
   ```ini
   [Unit]
   Description=AI Research Assistant
   After=network.target
   
   [Service]
   Type=simple
   User=research-assistant
   WorkingDirectory=/home/<USER>/AI-Research-Assistant
   Environment=PATH=/home/<USER>/AI-Research-Assistant/venv/bin
   ExecStart=/home/<USER>/AI-Research-Assistant/venv/bin/python main.py
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   ```

6. **启动服务**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable research-assistant
   sudo systemctl start research-assistant
   ```

### CentOS/RHEL 服务器

1. **安装依赖**
   ```bash
   sudo yum update -y
   sudo yum install python3 python3-pip git -y
   ```

2. **其余步骤与Ubuntu类似**

---

## Docker部署

### 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建输出目录
RUN mkdir -p outputs

# 设置环境变量
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO

# 暴露端口（如果需要）
# EXPOSE 8000

# 运行应用
CMD ["python", "main.py"]
```

### 创建docker-compose.yml

```yaml
version: '3.8'

services:
  research-assistant:
    build: .
    container_name: ai-research-assistant
    environment:
      - EXECUTION_MODE=deep_research
      - LOG_LEVEL=INFO
    env_file:
      - .env
    volumes:
      - ./outputs:/app/outputs
      - ./logs:/app/logs
    restart: unless-stopped
    stdin_open: true
    tty: true
```

### 部署命令

```bash
# 构建镜像
docker build -t ai-research-assistant .

# 运行容器
docker-compose up -d

# 查看日志
docker-compose logs -f

# 进入容器
docker-compose exec research-assistant bash
```

---

## 云平台部署

### AWS EC2

1. **启动EC2实例**
   - 选择Ubuntu 20.04 LTS AMI
   - 实例类型：t3.medium 或更高
   - 配置安全组允许SSH访问

2. **连接并部署**
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-ip
   # 按照服务器部署步骤进行
   ```

### Google Cloud Platform

1. **创建Compute Engine实例**
   ```bash
   gcloud compute instances create research-assistant \
     --image-family=ubuntu-2004-lts \
     --image-project=ubuntu-os-cloud \
     --machine-type=e2-medium \
     --zone=us-central1-a
   ```

2. **SSH连接并部署**
   ```bash
   gcloud compute ssh research-assistant
   # 按照服务器部署步骤进行
   ```

### Azure Virtual Machine

1. **创建VM**
   ```bash
   az vm create \
     --resource-group myResourceGroup \
     --name research-assistant \
     --image UbuntuLTS \
     --size Standard_B2s \
     --admin-username azureuser \
     --generate-ssh-keys
   ```

---

## 性能优化

### 配置优化

```bash
# .env 文件中的性能配置
MAX_PAPERS_TO_ANALYZE=50
PAPER_BATCH_SIZE=5
MAX_CONCURRENT_REQUESTS=3
REQUEST_DELAY=1.0
ENABLE_CACHE=true
```

### 系统优化

1. **增加文件描述符限制**
   ```bash
   echo "* soft nofile 65536" >> /etc/security/limits.conf
   echo "* hard nofile 65536" >> /etc/security/limits.conf
   ```

2. **优化Python性能**
   ```bash
   # 使用更快的JSON库
   pip install ujson
   
   # 启用Python优化
   export PYTHONOPTIMIZE=1
   ```

### 内存管理

```bash
# 监控内存使用
free -h
htop

# 配置交换空间（如果需要）
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

## 监控与维护

### 日志管理

1. **配置日志轮转**
   ```bash
   sudo nano /etc/logrotate.d/research-assistant
   ```
   
   内容：
   ```
   /home/<USER>/AI-Research-Assistant/*.log {
       daily
       missingok
       rotate 7
       compress
       delaycompress
       notifempty
       copytruncate
   }
   ```

### 健康检查

1. **创建健康检查脚本**
   ```bash
   #!/bin/bash
   # health_check.sh
   
   cd /home/<USER>/AI-Research-Assistant
   source venv/bin/activate
   
   python main.py --health > /tmp/health_check.log 2>&1
   
   if [ $? -eq 0 ]; then
       echo "$(date): Health check passed" >> /var/log/research-assistant-health.log
   else
       echo "$(date): Health check failed" >> /var/log/research-assistant-health.log
       # 发送告警邮件或通知
   fi
   ```

2. **设置定时任务**
   ```bash
   crontab -e
   # 添加：每小时检查一次
   0 * * * * /home/<USER>/health_check.sh
   ```

### 备份策略

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/research-assistant"
APP_DIR="/home/<USER>/AI-Research-Assistant"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置和输出
tar -czf $BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S).tar.gz \
    $APP_DIR/.env \
    $APP_DIR/outputs/ \
    $APP_DIR/*.log

# 保留最近7天的备份
find $BACKUP_DIR -name "backup-*.tar.gz" -mtime +7 -delete
```

### 更新维护

```bash
#!/bin/bash
# update.sh

cd /home/<USER>/AI-Research-Assistant

# 备份当前版本
git stash

# 拉取最新代码
git pull origin main

# 更新依赖
source venv/bin/activate
pip install -r requirements.txt

# 运行测试
python run_tests.py

# 重启服务
sudo systemctl restart research-assistant
```

---

## 安全考虑

### API密钥安全

1. **使用环境变量**
   - 永远不要在代码中硬编码API密钥
   - 使用 `.env` 文件并确保它不被版本控制

2. **文件权限**
   ```bash
   chmod 600 .env
   chown research-assistant:research-assistant .env
   ```

### 网络安全

1. **防火墙配置**
   ```bash
   sudo ufw enable
   sudo ufw allow ssh
   sudo ufw allow from trusted-ip-range
   ```

2. **SSL/TLS**
   - 如果暴露Web接口，使用HTTPS
   - 考虑使用Let's Encrypt证书

### 访问控制

1. **用户权限**
   - 使用专用用户运行应用
   - 限制文件系统访问权限

2. **API限制**
   - 监控API使用量
   - 设置合理的请求频率限制

---

## 故障排除

### 常见部署问题

1. **依赖安装失败**
   ```bash
   # 更新pip
   pip install --upgrade pip
   
   # 清理缓存
   pip cache purge
   
   # 重新安装
   pip install -r requirements.txt --force-reinstall
   ```

2. **权限问题**
   ```bash
   # 检查文件权限
   ls -la
   
   # 修复权限
   chown -R research-assistant:research-assistant /app
   chmod -R 755 /app
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 调整配置
   export MAX_PAPERS_TO_ANALYZE=20
   export PAPER_BATCH_SIZE=3
   ```

### 日志分析

```bash
# 查看应用日志
tail -f research_assistant.log

# 查看系统日志
sudo journalctl -u research-assistant -f

# 搜索错误
grep -i error research_assistant.log
```

---

## 联系支持

如果在部署过程中遇到问题，请：

1. 检查日志文件获取详细错误信息
2. 运行健康检查：`python main.py --health`
3. 查看故障排除部分
4. 提交Issue到项目仓库，包含：
   - 操作系统信息
   - Python版本
   - 错误日志
   - 复现步骤
