# AI研究助理增强功能文档

本文档描述了AI研究助理系统的最新增强功能，包括OpenAI兼容模式支持和AI驱动的关键词生成优化。

## 🚀 主要增强功能

### 1. OpenAI兼容模式支持

系统现在支持OpenAI兼容的API端点，允许使用本地部署的模型服务或第三方兼容服务。

#### 特性
- **自定义API端点**: 支持任何OpenAI兼容的API端点
- **灵活认证**: 支持自定义API密钥或无密钥模式（适用于本地服务）
- **模型选择**: 可配置使用的具体模型名称
- **无缝集成**: 与现有OpenAI客户端完全兼容

#### 配置示例

```bash
# .env文件配置
DEFAULT_LLM_PROVIDER=openai-compatible
OPENAI_COMPATIBLE_BASE_URL=http://localhost:8000/v1
OPENAI_COMPATIBLE_API_KEY=your-api-key
OPENAI_COMPATIBLE_MODEL=llama-2-7b-chat
```

#### 支持的服务
- **本地部署**: Ollama, LocalAI, vLLM等
- **云服务**: Together AI, Anyscale, Replicate等
- **企业服务**: Azure OpenAI (自定义端点)
- **开源项目**: FastChat, Text Generation WebUI等

### 2. 多模型代理配置

不同的AI代理现在可以使用不同的LLM提供商和模型，实现最优的任务分配。

#### 代理特定配置

```bash
# 规划代理使用OpenAI GPT-4
PLANNER_AGENT_PROVIDER=openai
PLANNER_AGENT_MODEL=gpt-4
PLANNER_TEMPERATURE=0.7
PLANNER_MAX_TOKENS=6000

# 综合代理使用Google Gemini
SYNTHESIZER_AGENT_PROVIDER=gemini
SYNTHESIZER_AGENT_MODEL=gemini-1.5-pro
SYNTHESIZER_TEMPERATURE=0.7
SYNTHESIZER_MAX_TOKENS=12000

# 写作代理使用Anthropic Claude
WRITER_AGENT_PROVIDER=anthropic
WRITER_AGENT_MODEL=claude-3-sonnet-20240229
WRITER_TEMPERATURE=0.7
WRITER_MAX_TOKENS=16000
```

#### 优势
- **任务优化**: 不同模型擅长不同任务
- **成本控制**: 可根据任务复杂度选择合适的模型
- **性能提升**: 利用各模型的优势
- **灵活配置**: 可随时调整代理配置

### 3. AI驱动的关键词生成

PlannerAgent现在完全依赖AI生成优化的学术搜索关键词，不再使用简单的文本处理。

#### 改进内容
- **智能分析**: 使用LLM理解研究主题和子问题
- **学术优化**: 生成针对学术数据库优化的关键词
- **多层重试**: 实现智能重试机制确保关键词质量
- **弃用文本处理**: 移除简单的文本分割和停用词过滤

#### 关键词生成流程
1. **主题分析**: AI分析研究主题的核心概念
2. **子问题解构**: 为每个子问题生成特定关键词
3. **学术优化**: 确保关键词适合学术数据库搜索
4. **质量保证**: 多层验证和重试机制
5. **去重整合**: 智能去重和关键词整合

#### 示例输出
```python
# 输入主题: "Deep Learning in Astrophysics"
# AI生成的关键词:
[
    "deep learning astrophysics",
    "neural networks astronomy", 
    "machine learning cosmology",
    "artificial intelligence stellar",
    "computational astrophysics",
    "astronomical data mining",
    "galaxy classification CNN",
    "exoplanet detection ML"
]
```

## 🔧 配置指南

### 基础配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

### OpenAI兼容模式配置

```bash
# 本地Ollama服务
DEFAULT_LLM_PROVIDER=openai-compatible
OPENAI_COMPATIBLE_BASE_URL=http://localhost:11434/v1
OPENAI_COMPATIBLE_API_KEY=dummy-key
OPENAI_COMPATIBLE_MODEL=llama2

# Together AI服务
DEFAULT_LLM_PROVIDER=openai-compatible
OPENAI_COMPATIBLE_BASE_URL=https://api.together.xyz/v1
OPENAI_COMPATIBLE_API_KEY=your-together-api-key
OPENAI_COMPATIBLE_MODEL=meta-llama/Llama-2-7b-chat-hf
```

### 多代理配置示例

```bash
# 高性能配置 - 所有代理使用GPT-4
PLANNER_AGENT_PROVIDER=openai
SYNTHESIZER_AGENT_PROVIDER=openai
WRITER_AGENT_PROVIDER=openai

# 平衡配置 - 混合使用不同模型
PLANNER_AGENT_PROVIDER=openai
SYNTHESIZER_AGENT_PROVIDER=gemini
WRITER_AGENT_PROVIDER=anthropic

# 本地配置 - 使用本地模型
PLANNER_AGENT_PROVIDER=openai-compatible
SYNTHESIZER_AGENT_PROVIDER=openai-compatible
WRITER_AGENT_PROVIDER=openai-compatible
```

## 🧪 测试和验证

### 运行增强功能测试

```bash
# 测试所有增强功能
python test_llm_enhancements.py

# 测试基础功能
python test_enhancements.py

# 健康检查
python main.py --health
```

### 验证关键词生成

```python
from agents import PlannerAgent

planner = PlannerAgent()
query = planner.generate_research_plan("Quantum Computing Applications")

print(f"生成的关键词: {query.keywords}")
print(f"子问题关键词映射: {query.sub_question_keywords}")
```

## 📈 性能优化建议

### 模型选择建议

| 任务类型 | 推荐模型 | 原因 |
|---------|---------|------|
| 规划分解 | GPT-4, Claude-3 | 逻辑推理能力强 |
| 文献分析 | Gemini-1.5-Pro | 长文本处理能力 |
| 报告写作 | Claude-3-Sonnet | 写作质量高 |
| 关键词生成 | GPT-4 | 学术理解能力 |

### 成本优化

```bash
# 成本敏感配置
PLANNER_AGENT_PROVIDER=openai-compatible  # 使用本地模型
SYNTHESIZER_AGENT_PROVIDER=gemini         # 使用Gemini处理长文本
WRITER_AGENT_PROVIDER=openai              # 仅写作使用GPT-4
```

## 🔍 故障排除

### 常见问题

1. **OpenAI兼容端点连接失败**
   - 检查端点URL是否正确
   - 确认服务是否运行
   - 验证API密钥格式

2. **关键词生成质量不佳**
   - 调整temperature参数
   - 增加max_tokens限制
   - 检查提示词模板

3. **多代理配置冲突**
   - 验证所有提供商配置
   - 检查API密钥有效性
   - 确认模型名称正确

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python main.py

# 测试特定组件
python -c "from clients import OpenAICompatibleClient; client = OpenAICompatibleClient(); print(client.test_connection())"
```

## 🚀 下一步计划

- [ ] 支持更多LLM提供商（Cohere, AI21等）
- [ ] 实现动态模型切换
- [ ] 添加模型性能监控
- [ ] 优化关键词生成算法
- [ ] 支持自定义提示词模板

## 📞 支持

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 运行测试脚本验证配置
3. 检查日志文件获取详细错误信息
4. 提交Issue描述具体问题

---

**更新日期**: 2024年7月20日  
**版本**: v2.0.0  
**兼容性**: 向后兼容，支持所有现有配置
