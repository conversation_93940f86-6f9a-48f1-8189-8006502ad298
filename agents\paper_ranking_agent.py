# agents/paper_ranking_agent.py
#
# PaperRankingAgent - 论文排序和筛选代理
# 在PlannerAgent和SynthesizerAgent之间插入，优化学术论文检索和筛选策略

import logging
import json
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from clients.llm_client import LLMClient
from models.response import LLMResponse
from models.paper import Paper
from config import config


class PaperRankingAgent:
    """
    论文排序和筛选代理
    
    功能：
    1. 相关性评估：基于论文标题与子问题的语义相关性
    2. 重要性评估：综合引用数、期刊影响因子、发表时间
    3. 综合排序：结合相关性和重要性得分
    4. 智能筛选：从大量论文中筛选出最相关的前N篇
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """初始化PaperRankingAgent"""
        if llm_client:
            self.llm_client = llm_client
        else:
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("paper_ranking")
        
        self.logger = logging.getLogger(__name__)
        self.agent_config = config.get_agent_config("paper_ranking")
        
        # 排序参数配置
        self.relevance_weight = 0.6  # 相关性权重
        self.importance_weight = 0.4  # 重要性权重
        self.default_top_n = 30  # 默认筛选论文数量
        
        self.logger.info("PaperRankingAgent initialized successfully")
    
    def rank_and_filter_papers(self, sub_question: str, papers: List[Paper], 
                             top_n: int = None) -> Tuple[List[Paper], Dict[str, Any]]:
        """
        对论文进行排序和筛选
        
        Args:
            sub_question: 子问题文本
            papers: 待排序的论文列表
            top_n: 筛选的论文数量，默认为30
            
        Returns:
            Tuple[排序后的前N篇论文, 排序统计信息]
        """
        if not papers:
            self.logger.warning("No papers provided for ranking")
            return [], {"total_papers": 0, "filtered_papers": 0}
        
        if top_n is None:
            top_n = self.default_top_n
        
        self.logger.info(f"Starting paper ranking for sub-question: '{sub_question[:50]}...'")
        self.logger.info(f"Total papers to rank: {len(papers)}, Target top N: {top_n}")
        
        try:
            # 第一步：相关性评估
            papers_with_relevance = self._assess_relevance(sub_question, papers)
            
            # 第二步：重要性评估
            papers_with_scores = self._assess_importance(papers_with_relevance)
            
            # 第三步：综合排序
            ranked_papers = self._calculate_composite_scores(papers_with_scores)
            
            # 第四步：筛选前N篇
            top_papers = ranked_papers[:min(top_n, len(ranked_papers))]
            
            # 生成统计信息
            stats = self._generate_ranking_stats(papers, top_papers, sub_question)
            
            self.logger.info(f"Paper ranking completed. Selected {len(top_papers)} papers from {len(papers)}")
            
            return top_papers, stats
            
        except Exception as e:
            self.logger.error(f"Error in paper ranking: {e}")
            # 返回原始论文的前N篇作为备用
            fallback_papers = papers[:min(top_n, len(papers))]
            fallback_stats = {
                "total_papers": len(papers),
                "filtered_papers": len(fallback_papers),
                "ranking_method": "fallback",
                "error": str(e)
            }
            return fallback_papers, fallback_stats
    
    def _assess_relevance(self, sub_question: str, papers: List[Paper]) -> List[Paper]:
        """评估论文与子问题的相关性"""
        self.logger.info("Assessing paper relevance using AI model")
        
        # 准备论文信息用于AI评估（只使用标题以控制上下文长度）
        paper_titles = []
        for i, paper in enumerate(papers):
            paper_info = {
                "index": i,
                "title": paper.title,
                "year": getattr(paper, 'publication_date', 'Unknown')[:4] if hasattr(paper, 'publication_date') and paper.publication_date else 'Unknown'
            }
            paper_titles.append(paper_info)
        
        # 分批处理以避免上下文窗口限制
        batch_size = 50  # 每批处理50篇论文
        all_relevance_scores = {}
        
        for i in range(0, len(paper_titles), batch_size):
            batch = paper_titles[i:i + batch_size]
            batch_scores = self._assess_relevance_batch(sub_question, batch)
            all_relevance_scores.update(batch_scores)
        
        # 将相关性分数添加到论文对象
        for i, paper in enumerate(papers):
            relevance_score = all_relevance_scores.get(i, 0.5)  # 默认中等相关性
            paper.relevance_score = relevance_score
        
        return papers
    
    def _assess_relevance_batch(self, sub_question: str, paper_batch: List[Dict]) -> Dict[int, float]:
        """批量评估论文相关性"""
        prompt = self._create_relevance_assessment_prompt(sub_question, paper_batch)
        
        try:
            response = self.llm_client.generate(
                prompt,
                temperature=self.agent_config.get("temperature", 0.7),
                max_tokens=self.agent_config.get("max_tokens", 8000)
            )
            
            if response.is_successful():
                return self._parse_relevance_scores(response.content, len(paper_batch))
            else:
                self.logger.warning(f"Relevance assessment failed: {response.error}")
                return {i: 0.5 for i in range(len(paper_batch))}  # 默认分数
                
        except Exception as e:
            self.logger.error(f"Error in relevance assessment: {e}")
            return {i: 0.5 for i in range(len(paper_batch))}
    
    def _create_relevance_assessment_prompt(self, sub_question: str, paper_batch: List[Dict]) -> str:
        """创建相关性评估的prompt"""
        papers_text = "\n".join([
            f"{p['index']}. {p['title']} ({p['year']})"
            for p in paper_batch
        ])
        
        prompt = f"""
You are an expert academic researcher specializing in relevance assessment. Your task is to evaluate how relevant each paper title is to a specific research sub-question.

**Research Sub-Question**: "{sub_question}"

**Papers to Evaluate**:
{papers_text}

**Evaluation Criteria**:
1. **Direct Relevance** (0.8-1.0): Paper directly addresses the sub-question or core concepts
2. **High Relevance** (0.6-0.8): Paper covers related concepts or methodologies that are highly applicable
3. **Moderate Relevance** (0.4-0.6): Paper has some connection but may be tangential
4. **Low Relevance** (0.2-0.4): Paper has minimal connection to the sub-question
5. **No Relevance** (0.0-0.2): Paper is unrelated to the sub-question

**Instructions**:
- Focus ONLY on the paper titles (abstracts are not provided)
- Consider semantic similarity, keyword overlap, and conceptual relevance
- Be precise in your scoring - use the full range from 0.0 to 1.0
- Consider the research context and domain

**Output Format** (JSON only, no additional text):
{{
    "relevance_scores": {{
        "0": 0.85,
        "1": 0.42,
        "2": 0.91,
        ...
    }},
    "assessment_notes": "Brief explanation of scoring approach for this batch"
}}

Provide relevance scores for all {len(paper_batch)} papers in the batch.
"""
        return prompt
    
    def _parse_relevance_scores(self, response_content: str, expected_count: int) -> Dict[int, float]:
        """解析相关性评分结果"""
        try:
            # 尝试解析JSON响应
            response_data = json.loads(response_content.strip())
            relevance_scores = response_data.get("relevance_scores", {})
            
            # 转换为整数索引的字典
            parsed_scores = {}
            for key, score in relevance_scores.items():
                try:
                    index = int(key)
                    score_float = float(score)
                    # 确保分数在合理范围内
                    score_float = max(0.0, min(1.0, score_float))
                    parsed_scores[index] = score_float
                except (ValueError, TypeError):
                    continue
            
            # 为缺失的索引提供默认分数
            for i in range(expected_count):
                if i not in parsed_scores:
                    parsed_scores[i] = 0.5  # 默认中等相关性
            
            return parsed_scores
            
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse relevance scores JSON, using default scores")
            return {i: 0.5 for i in range(expected_count)}
    
    def _assess_importance(self, papers: List[Paper]) -> List[Paper]:
        """评估论文重要性（基于引用数、发表时间等）"""
        self.logger.info("Assessing paper importance based on metadata")
        
        current_year = datetime.now().year
        
        for paper in papers:
            # 引用数评分 (0-1)
            citation_score = self._calculate_citation_score(paper.citation_count)
            
            # 时效性评分 (0-1)
            pub_date = getattr(paper, 'publication_date', '') or ''
            recency_score = self._calculate_recency_score(pub_date, current_year)
            
            # 期刊影响因子评分 (0-1) - 简化版本，基于期刊名称
            journal_score = self._calculate_journal_score(getattr(paper, 'journal', ''))
            
            # 综合重要性评分
            importance_score = (
                citation_score * 0.5 +  # 引用数权重50%
                recency_score * 0.3 +   # 时效性权重30%
                journal_score * 0.2     # 期刊权重20%
            )
            
            paper.importance_score = importance_score
            paper.citation_score = citation_score
            paper.recency_score = recency_score
            paper.journal_score = journal_score
        
        return papers
    
    def _calculate_citation_score(self, citation_count: int) -> float:
        """计算引用数评分"""
        if citation_count <= 0:
            return 0.0
        elif citation_count <= 10:
            return 0.3
        elif citation_count <= 50:
            return 0.5
        elif citation_count <= 100:
            return 0.7
        elif citation_count <= 500:
            return 0.85
        else:
            return 1.0
    
    def _calculate_recency_score(self, publication_date: str, current_year: int) -> float:
        """计算时效性评分"""
        try:
            if not publication_date:
                return 0.3  # 未知日期给予较低分数
            
            # 提取年份
            year_match = re.search(r'\b(19|20)\d{2}\b', publication_date)
            if not year_match:
                return 0.3
            
            pub_year = int(year_match.group())
            years_ago = current_year - pub_year
            
            if years_ago <= 1:
                return 1.0
            elif years_ago <= 3:
                return 0.8
            elif years_ago <= 5:
                return 0.6
            elif years_ago <= 10:
                return 0.4
            else:
                return 0.2
                
        except (ValueError, AttributeError):
            return 0.3
    
    def _calculate_journal_score(self, journal: str) -> float:
        """计算期刊评分（简化版本）"""
        if not journal:
            return 0.5
        
        journal_lower = journal.lower()
        
        # 高影响因子期刊关键词
        high_impact_keywords = [
            'nature', 'science', 'cell', 'lancet', 'nejm', 'physical review',
            'astrophysical journal', 'astronomy', 'monthly notices', 'ieee',
            'acm', 'pnas', 'jama'
        ]
        
        # 中等影响因子期刊关键词
        medium_impact_keywords = [
            'journal', 'proceedings', 'transactions', 'letters', 'communications',
            'review', 'research', 'international', 'european', 'american'
        ]
        
        for keyword in high_impact_keywords:
            if keyword in journal_lower:
                return 0.9
        
        for keyword in medium_impact_keywords:
            if keyword in journal_lower:
                return 0.7
        
        return 0.5  # 默认分数
    
    def _calculate_composite_scores(self, papers: List[Paper]) -> List[Paper]:
        """计算综合评分并排序"""
        for paper in papers:
            relevance = getattr(paper, 'relevance_score', 0.5)
            importance = getattr(paper, 'importance_score', 0.5)
            
            composite_score = (
                relevance * self.relevance_weight +
                importance * self.importance_weight
            )
            
            paper.composite_score = composite_score
        
        # 按综合评分降序排序
        sorted_papers = sorted(papers, key=lambda p: p.composite_score, reverse=True)
        
        return sorted_papers
    
    def _generate_ranking_stats(self, original_papers: List[Paper], 
                              selected_papers: List[Paper], sub_question: str) -> Dict[str, Any]:
        """生成排序统计信息"""
        stats = {
            "sub_question": sub_question,
            "total_papers": len(original_papers),
            "filtered_papers": len(selected_papers),
            "ranking_method": "ai_relevance_plus_importance",
            "weights": {
                "relevance": self.relevance_weight,
                "importance": self.importance_weight
            }
        }
        
        if selected_papers:
            # 计算选中论文的评分统计
            relevance_scores = [getattr(p, 'relevance_score', 0) for p in selected_papers]
            importance_scores = [getattr(p, 'importance_score', 0) for p in selected_papers]
            composite_scores = [getattr(p, 'composite_score', 0) for p in selected_papers]
            
            stats.update({
                "score_statistics": {
                    "relevance": {
                        "min": min(relevance_scores),
                        "max": max(relevance_scores),
                        "avg": sum(relevance_scores) / len(relevance_scores)
                    },
                    "importance": {
                        "min": min(importance_scores),
                        "max": max(importance_scores),
                        "avg": sum(importance_scores) / len(importance_scores)
                    },
                    "composite": {
                        "min": min(composite_scores),
                        "max": max(composite_scores),
                        "avg": sum(composite_scores) / len(composite_scores)
                    }
                },
                "top_paper": {
                    "title": selected_papers[0].title,
                    "composite_score": selected_papers[0].composite_score,
                    "relevance_score": getattr(selected_papers[0], 'relevance_score', 0),
                    "importance_score": getattr(selected_papers[0], 'importance_score', 0)
                }
            })
        
        return stats
