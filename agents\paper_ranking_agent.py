# agents/paper_ranking_agent.py
#
# PaperRankingAgent - 论文排序和筛选代理
# 在PlannerAgent和SynthesizerAgent之间插入，优化学术论文检索和筛选策略

import logging
import json
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from clients.llm_client import LLMClient
from models.response import LLMResponse
from models.paper import Paper
from config import config


class PaperRankingAgent:
    """
    论文排序和筛选代理
    
    功能：
    1. 相关性评估：基于论文标题与子问题的语义相关性
    2. 重要性评估：综合引用数、期刊影响因子、发表时间
    3. 综合排序：结合相关性和重要性得分
    4. 智能筛选：从大量论文中筛选出最相关的前N篇
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """初始化PaperRankingAgent - 天体物理学专用版本"""
        if llm_client:
            self.llm_client = llm_client
        else:
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("paper_ranking")

        self.logger = logging.getLogger(__name__)
        self.agent_config = config.get_agent_config("paper_ranking")

        # 从配置文件加载所有参数 - 天体物理学优化
        self.relevance_weight = self.agent_config.get("relevance_weight", 0.65)
        self.importance_weight = self.agent_config.get("importance_weight", 0.35)
        self.default_top_n = self.agent_config.get("default_top_n", 25)
        self.citation_threshold_high = self.agent_config.get("citation_threshold_high", 100)
        self.citation_threshold_medium = self.agent_config.get("citation_threshold_medium", 20)
        self.recency_boost_years = self.agent_config.get("recency_boost_years", 3)
        self.astrophysics_journal_boost = self.agent_config.get("astrophysics_journal_boost", 0.15)

        self.logger.info(f"PaperRankingAgent initialized for astrophysics research")
        self.logger.info(f"Configuration: relevance_weight={self.relevance_weight}, importance_weight={self.importance_weight}")
        self.logger.info(f"Default top_n={self.default_top_n}, citation thresholds=({self.citation_threshold_medium}, {self.citation_threshold_high})")
    
    def rank_and_filter_papers(self, sub_question: str, papers: List[Paper], 
                             top_n: int = None) -> Tuple[List[Paper], Dict[str, Any]]:
        """
        对论文进行排序和筛选
        
        Args:
            sub_question: 子问题文本
            papers: 待排序的论文列表
            top_n: 筛选的论文数量，默认为30
            
        Returns:
            Tuple[排序后的前N篇论文, 排序统计信息]
        """
        if not papers:
            self.logger.warning("No papers provided for ranking")
            return [], {"total_papers": 0, "filtered_papers": 0}
        
        if top_n is None:
            top_n = self.default_top_n
        
        self.logger.info(f"Starting paper ranking for sub-question: '{sub_question[:50]}...'")
        self.logger.info(f"Total papers to rank: {len(papers)}, Target top N: {top_n}")
        
        try:
            # 第一步：相关性评估
            papers_with_relevance = self._assess_relevance(sub_question, papers)
            
            # 第二步：重要性评估
            papers_with_scores = self._assess_importance(papers_with_relevance)
            
            # 第三步：综合排序
            ranked_papers = self._calculate_composite_scores(papers_with_scores)
            
            # 第四步：筛选前N篇
            top_papers = ranked_papers[:min(top_n, len(ranked_papers))]
            
            # 生成统计信息
            stats = self._generate_ranking_stats(papers, top_papers, sub_question)
            
            self.logger.info(f"Paper ranking completed. Selected {len(top_papers)} papers from {len(papers)}")
            
            return top_papers, stats
            
        except Exception as e:
            self.logger.error(f"Error in paper ranking: {e}")
            # 返回原始论文的前N篇作为备用
            fallback_papers = papers[:min(top_n, len(papers))]
            fallback_stats = {
                "total_papers": len(papers),
                "filtered_papers": len(fallback_papers),
                "ranking_method": "fallback",
                "error": str(e)
            }
            return fallback_papers, fallback_stats
    
    def _assess_relevance(self, sub_question: str, papers: List[Paper]) -> List[Paper]:
        """评估论文与子问题的相关性 - 天体物理学专用单次AI调用"""
        self.logger.info(f"Assessing relevance for {len(papers)} astrophysics papers using single AI call")

        # 准备论文信息用于AI评估（只使用标题和年份）
        paper_titles = []
        for i, paper in enumerate(papers):
            paper_info = {
                "index": i,
                "title": paper.title,
                "year": getattr(paper, 'publication_date', 'Unknown')[:4] if hasattr(paper, 'publication_date') and paper.publication_date else 'Unknown',
                "citations": getattr(paper, 'citation_count', 0)
            }
            paper_titles.append(paper_info)

        # 单次AI调用处理所有论文
        all_relevance_scores = self._assess_relevance_single_call(sub_question, paper_titles)

        # 将相关性分数添加到论文对象
        for i, paper in enumerate(papers):
            relevance_score = all_relevance_scores.get(i, 0.5)  # 默认中等相关性
            paper.relevance_score = relevance_score

        return papers
    
    def _assess_relevance_single_call(self, sub_question: str, paper_list: List[Dict]) -> Dict[int, float]:
        """单次AI调用评估所有论文相关性 - 天体物理学专用"""
        prompt = self._create_astrophysics_relevance_prompt(sub_question, paper_list)

        try:
            response = self.llm_client.generate(
                prompt,
                temperature=self.agent_config.get("temperature", 0.7),
                max_tokens=self.agent_config.get("max_tokens", 50000)  # 增加token限制支持更多论文
            )

            if response.is_successful():
                return self._parse_relevance_scores(response.content, len(paper_list))
            else:
                self.logger.warning(f"Astrophysics relevance assessment failed: {response.error}")
                return {i: 0.5 for i in range(len(paper_list))}  # 默认分数

        except Exception as e:
            self.logger.error(f"Error in astrophysics relevance assessment: {e}")
            return {i: 0.5 for i in range(len(paper_list))}
    
    def _create_astrophysics_relevance_prompt(self, sub_question: str, paper_list: List[Dict]) -> str:
        """创建天体物理学专用的相关性评估prompt"""
        papers_text = "\n".join([
            f"{p['index']}. {p['title']} ({p['year']}, {p['citations']} citations)"
            for p in paper_list
        ])

        prompt = f"""
You are an expert astrophysicist and research evaluator with deep knowledge of astronomical phenomena, observational techniques, theoretical models, and computational astrophysics. Your task is to evaluate how relevant each astrophysics paper is to a specific research sub-question.

**Astrophysics Research Sub-Question**: "{sub_question}"

**Papers to Evaluate**:
{papers_text}

**Astrophysics-Specific Evaluation Criteria**:

1. **Direct Astrophysical Relevance** (0.8-1.0):
   - Paper directly addresses the astronomical phenomenon or research question
   - Uses relevant observational data, theoretical models, or simulations
   - Focuses on the same astrophysical objects or processes

2. **High Astrophysical Relevance** (0.6-0.8):
   - Paper covers related astrophysical concepts or methodologies
   - Uses similar observational techniques or theoretical approaches
   - Studies related astronomical objects or physical processes

3. **Moderate Astrophysical Relevance** (0.4-0.6):
   - Paper has some connection to the astrophysical domain
   - May use relevant mathematical/computational methods
   - Covers tangentially related astronomical topics

4. **Low Astrophysical Relevance** (0.2-0.4):
   - Paper has minimal connection to the specific astrophysical question
   - May be from related physics domains but not astronomy-focused
   - Limited applicability to the research question

5. **No Astrophysical Relevance** (0.0-0.2):
   - Paper is from unrelated scientific domains
   - No connection to astronomical objects, phenomena, or methods

**Astrophysics Evaluation Guidelines**:
- Consider observational vs. theoretical vs. computational approaches
- Evaluate relevance of astronomical objects (stars, galaxies, planets, etc.)
- Assess methodological relevance (photometry, spectroscopy, simulations)
- Consider multi-wavelength and multi-messenger astronomy connections
- Account for the hierarchical nature of astrophysical scales (stellar, galactic, cosmological)
- Recognize interdisciplinary connections (astrobiology, astroparticle physics, etc.)

**Output Format** (JSON only, no additional text):
{{
    "relevance_scores": {{
        "0": 0.85,
        "1": 0.42,
        "2": 0.91,
        ...
    }},
    "astrophysics_assessment_notes": "Brief explanation of astrophysics-specific scoring approach"
}}

Provide relevance scores for all {len(paper_list)} papers based on astrophysical research standards.
"""
        return prompt
    
    def _parse_relevance_scores(self, response_content: str, expected_count: int) -> Dict[int, float]:
        """解析相关性评分结果"""
        try:
            # 尝试解析JSON响应
            response_data = json.loads(response_content.strip())
            relevance_scores = response_data.get("relevance_scores", {})
            
            # 转换为整数索引的字典
            parsed_scores = {}
            for key, score in relevance_scores.items():
                try:
                    index = int(key)
                    score_float = float(score)
                    # 确保分数在合理范围内
                    score_float = max(0.0, min(1.0, score_float))
                    parsed_scores[index] = score_float
                except (ValueError, TypeError):
                    continue
            
            # 为缺失的索引提供默认分数
            for i in range(expected_count):
                if i not in parsed_scores:
                    parsed_scores[i] = 0.5  # 默认中等相关性
            
            return parsed_scores
            
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse relevance scores JSON, using default scores")
            return {i: 0.5 for i in range(expected_count)}
    
    def _assess_importance(self, papers: List[Paper]) -> List[Paper]:
        """评估论文重要性（基于引用数、发表时间等）"""
        self.logger.info("Assessing paper importance based on metadata")
        
        current_year = datetime.now().year
        
        for paper in papers:
            # 引用数评分 (0-1)
            citation_score = self._calculate_citation_score(paper.citation_count)
            
            # 时效性评分 (0-1)
            pub_date = getattr(paper, 'publication_date', '') or ''
            recency_score = self._calculate_recency_score(pub_date, current_year)
            
            # 期刊影响因子评分 (0-1) - 简化版本，基于期刊名称
            journal_score = self._calculate_journal_score(getattr(paper, 'journal', ''))
            
            # 综合重要性评分
            importance_score = (
                citation_score * 0.5 +  # 引用数权重50%
                recency_score * 0.3 +   # 时效性权重30%
                journal_score * 0.2     # 期刊权重20%
            )
            
            paper.importance_score = importance_score
            paper.citation_score = citation_score
            paper.recency_score = recency_score
            paper.journal_score = journal_score
        
        return papers
    
    def _calculate_citation_score(self, citation_count: int) -> float:
        """计算引用数评分 - 天体物理学优化"""
        if citation_count <= 0:
            return 0.0
        elif citation_count <= self.citation_threshold_medium:  # 默认20
            return 0.3
        elif citation_count <= self.citation_threshold_high:   # 默认100
            return 0.6
        elif citation_count <= 200:
            return 0.75
        elif citation_count <= 500:
            return 0.9
        else:
            return 1.0
    
    def _calculate_recency_score(self, publication_date: str, current_year: int) -> float:
        """计算时效性评分 - 天体物理学优化"""
        try:
            if not publication_date:
                return 0.3  # 未知日期给予较低分数

            # 提取年份
            year_match = re.search(r'\b(19|20)\d{2}\b', publication_date)
            if not year_match:
                return 0.3

            pub_year = int(year_match.group())
            years_ago = current_year - pub_year

            # 天体物理学优化的时效性评分
            if years_ago <= 1:
                return 1.0
            elif years_ago <= self.recency_boost_years:  # 默认3年
                return 0.85
            elif years_ago <= 5:
                return 0.7
            elif years_ago <= 8:
                return 0.5
            elif years_ago <= 15:  # 天体物理学经典论文仍有价值
                return 0.35
            else:
                return 0.2

        except (ValueError, AttributeError):
            return 0.3
    
    def _calculate_journal_score(self, journal: str) -> float:
        """计算期刊评分 - 天体物理学专用"""
        if not journal:
            return 0.5

        journal_lower = journal.lower()
        base_score = 0.5

        # 顶级天体物理学期刊
        top_astrophysics_journals = [
            'astrophysical journal', 'apj', 'astronomical journal', 'aj',
            'monthly notices of the royal astronomical society', 'mnras',
            'astronomy and astrophysics', 'a&a',
            'nature astronomy', 'science', 'nature',
            'annual review of astronomy and astrophysics', 'araa'
        ]

        # 高影响天体物理学期刊
        high_impact_astrophysics = [
            'astrophysical journal supplement', 'apjs', 'icarus',
            'journal of cosmology and astroparticle physics', 'jcap',
            'living reviews in relativity', 'classical and quantum gravity',
            'physical review d', 'prd', 'space science reviews'
        ]

        # 专业天体物理学期刊
        specialized_astrophysics = [
            'publications of the astronomical society of the pacific', 'pasp',
            'astronomical society of the pacific', 'solar physics',
            'planetary and space science', 'advances in space research',
            'astrobiology', 'astroparticle physics', 'new astronomy'
        ]

        # 通用高影响期刊
        general_high_impact = [
            'nature', 'science', 'physical review letters', 'prl',
            'proceedings of the national academy of sciences', 'pnas'
        ]

        # 评分逻辑
        for journal_name in top_astrophysics_journals:
            if journal_name in journal_lower:
                base_score = 1.0
                break

        if base_score < 1.0:
            for journal_name in high_impact_astrophysics:
                if journal_name in journal_lower:
                    base_score = 0.9
                    break

        if base_score < 0.9:
            for journal_name in specialized_astrophysics:
                if journal_name in journal_lower:
                    base_score = 0.75
                    break

        if base_score < 0.75:
            for journal_name in general_high_impact:
                if journal_name in journal_lower:
                    base_score = 0.95
                    break

        # 天体物理学期刊额外加分
        if any(keyword in journal_lower for keyword in ['astronomy', 'astrophys', 'cosmology', 'stellar', 'galactic', 'planetary']):
            base_score += self.astrophysics_journal_boost
            base_score = min(base_score, 1.0)  # 确保不超过1.0

        return base_score
    
    def _calculate_composite_scores(self, papers: List[Paper]) -> List[Paper]:
        """计算综合评分并排序"""
        for paper in papers:
            relevance = getattr(paper, 'relevance_score', 0.5)
            importance = getattr(paper, 'importance_score', 0.5)
            
            composite_score = (
                relevance * self.relevance_weight +
                importance * self.importance_weight
            )
            
            paper.composite_score = composite_score
        
        # 按综合评分降序排序
        sorted_papers = sorted(papers, key=lambda p: p.composite_score, reverse=True)
        
        return sorted_papers
    
    def _generate_ranking_stats(self, original_papers: List[Paper], 
                              selected_papers: List[Paper], sub_question: str) -> Dict[str, Any]:
        """生成排序统计信息"""
        stats = {
            "sub_question": sub_question,
            "total_papers": len(original_papers),
            "filtered_papers": len(selected_papers),
            "ranking_method": "ai_relevance_plus_importance",
            "weights": {
                "relevance": self.relevance_weight,
                "importance": self.importance_weight
            }
        }
        
        if selected_papers:
            # 计算选中论文的评分统计
            relevance_scores = [getattr(p, 'relevance_score', 0) for p in selected_papers]
            importance_scores = [getattr(p, 'importance_score', 0) for p in selected_papers]
            composite_scores = [getattr(p, 'composite_score', 0) for p in selected_papers]
            
            stats.update({
                "score_statistics": {
                    "relevance": {
                        "min": min(relevance_scores),
                        "max": max(relevance_scores),
                        "avg": sum(relevance_scores) / len(relevance_scores)
                    },
                    "importance": {
                        "min": min(importance_scores),
                        "max": max(importance_scores),
                        "avg": sum(importance_scores) / len(importance_scores)
                    },
                    "composite": {
                        "min": min(composite_scores),
                        "max": max(composite_scores),
                        "avg": sum(composite_scores) / len(composite_scores)
                    }
                },
                "top_paper": {
                    "title": selected_papers[0].title,
                    "composite_score": selected_papers[0].composite_score,
                    "relevance_score": getattr(selected_papers[0], 'relevance_score', 0),
                    "importance_score": getattr(selected_papers[0], 'importance_score', 0)
                }
            })
        
        return stats
