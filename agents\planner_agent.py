# agents/planner_agent.py
#
# 课题规划代理 - 负责将研究课题分解为关键子问题

import logging
import json
import re
from typing import List, Optional, Dict, Any

from clients import LLMClient
from models import LLMResponse, ResearchQuery
from prompts import Prompts
from config import config


class PlannerAgent:
    """课题规划代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化规划代理

        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为PlannerAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("planner")
        self.logger = logging.getLogger(__name__)
    
    def generate_research_plan(self, topic: str) -> ResearchQuery:
        """
        为给定课题生成增强的研究计划，包含子问题和优化的关键词

        Args:
            topic: 研究课题

        Returns:
            ResearchQuery: 包含主题、子问题和优化关键词的研究查询对象
        """
        self.logger.info(f"Generating enhanced research plan for topic: {topic}")

        # 获取代理特定配置
        agent_config = config.get_agent_config("planner")

        # 使用增强的提示词生成子问题和关键词
        prompt = Prompts.PLANNER_PROMPT.format(topic=topic)

        try:
            # 调用LLM生成响应
            response = self.llm_client.generate(
                prompt,
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE),
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS)
            )

            if not response.is_successful():
                self.logger.error(f"LLM request failed: {response.error}")
                return self._create_fallback_plan(topic)

            # 解析增强的LLM响应（包含子问题和关键词）
            parsed_result = self._parse_enhanced_response(response.response, topic)

            if not parsed_result:
                self.logger.warning("Failed to parse enhanced response, using fallback")
                return self._create_fallback_plan(topic)

            # 创建研究查询对象
            research_query = ResearchQuery(
                main_topic=topic,
                sub_questions=parsed_result["sub_questions"]
            )

            # 添加优化的关键词
            for keyword_set in parsed_result["keyword_sets"].values():
                for keyword in keyword_set:
                    research_query.add_keyword(keyword)

            # 存储子问题到关键词的映射（用于后续检索）
            research_query.sub_question_keywords = parsed_result["keyword_sets"]

            self.logger.info(f"Generated {len(parsed_result['sub_questions'])} sub-questions with optimized keywords")

            return research_query

        except Exception as e:
            self.logger.error(f"Error generating research plan: {e}")
            return self._create_fallback_plan(topic)
    
    def _parse_sub_questions(self, llm_response: str) -> List[str]:
        """
        从LLM响应中解析子问题列表
        
        Args:
            llm_response: LLM的响应文本
            
        Returns:
            List[str]: 子问题列表
        """
        sub_questions = []
        
        try:
            # 首先尝试解析JSON格式的列表
            # 查找类似 ["问题1", "问题2", ...] 的模式
            json_pattern = r'\[([^\]]+)\]'
            json_matches = re.findall(json_pattern, llm_response, re.DOTALL)
            
            for match in json_matches:
                try:
                    # 尝试解析为JSON数组
                    full_json = f'[{match}]'
                    parsed_list = json.loads(full_json)
                    if isinstance(parsed_list, list):
                        sub_questions.extend([str(item).strip().strip('"\'') for item in parsed_list])
                        break
                except json.JSONDecodeError:
                    continue
            
            # 如果JSON解析失败，尝试其他模式
            if not sub_questions:
                # 查找编号列表模式 (1. 问题, 2. 问题, ...)
                numbered_pattern = r'^\s*\d+\.\s*(.+?)(?=\n\s*\d+\.|$)'
                numbered_matches = re.findall(numbered_pattern, llm_response, re.MULTILINE | re.DOTALL)
                if numbered_matches:
                    sub_questions = [q.strip().rstrip('?') + '?' for q in numbered_matches]
                
                # 查找破折号列表模式 (- 问题, - 问题, ...)
                if not sub_questions:
                    dash_pattern = r'^\s*[-*]\s*(.+?)(?=\n\s*[-*]|$)'
                    dash_matches = re.findall(dash_pattern, llm_response, re.MULTILINE | re.DOTALL)
                    if dash_matches:
                        sub_questions = [q.strip().rstrip('?') + '?' for q in dash_matches]
                
                # 查找问号结尾的句子
                if not sub_questions:
                    question_pattern = r'([^.!?]*\?)'
                    question_matches = re.findall(question_pattern, llm_response)
                    if question_matches:
                        sub_questions = [q.strip() for q in question_matches if len(q.strip()) > 10]
            
            # 清理和验证子问题
            cleaned_questions = []
            for question in sub_questions:
                question = question.strip().strip('"\'')
                if question and len(question) > 5:  # 过滤太短的问题
                    if not question.endswith('?'):
                        question += '?'
                    cleaned_questions.append(question)
            
            # 限制子问题数量
            if len(cleaned_questions) > 7:
                cleaned_questions = cleaned_questions[:7]
            
            return cleaned_questions

        except Exception as e:
            self.logger.error(f"Error parsing sub-questions: {e}")
            return []

    def _parse_enhanced_response(self, llm_response: str, topic: str) -> Optional[Dict[str, Any]]:
        """
        解析增强的LLM响应，包含子问题和关键词

        Args:
            llm_response: LLM的响应文本
            topic: 研究主题

        Returns:
            Dict[str, Any]: 包含sub_questions和keyword_sets的字典，失败时返回None
        """
        try:
            # 首先尝试解析JSON格式的响应
            import json

            # 清理响应文本
            cleaned_response = llm_response.strip()

            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                try:
                    parsed_data = json.loads(json_str)

                    # 验证必需字段
                    if "sub_questions" in parsed_data and "keyword_sets" in parsed_data:
                        # 清理和验证子问题
                        sub_questions = []
                        for q in parsed_data["sub_questions"]:
                            if isinstance(q, str) and len(q.strip()) > 5:
                                question = q.strip()
                                if not question.endswith('?'):
                                    question += '?'
                                sub_questions.append(question)

                        # 验证关键词集合
                        keyword_sets = {}
                        if isinstance(parsed_data["keyword_sets"], dict):
                            for i, (key, keywords) in enumerate(parsed_data["keyword_sets"].items()):
                                if isinstance(keywords, list):
                                    clean_keywords = [kw.strip() for kw in keywords if isinstance(kw, str) and kw.strip()]
                                    if clean_keywords:
                                        keyword_sets[f"sub_question_{i+1}"] = clean_keywords

                        if sub_questions and keyword_sets:
                            return {
                                "sub_questions": sub_questions[:7],  # 限制为最多7个子问题
                                "keyword_sets": keyword_sets
                            }

                except json.JSONDecodeError:
                    pass

            # 如果JSON解析失败，尝试生成基于传统方法的结果
            self.logger.warning("Failed to parse JSON response, falling back to traditional parsing")
            sub_questions = self._parse_sub_questions(llm_response)

            if sub_questions:
                # 为每个子问题生成关键词
                keyword_sets = {}
                for i, question in enumerate(sub_questions):
                    keywords = self._generate_keywords_for_question(question, topic)
                    if keywords:
                        keyword_sets[f"sub_question_{i+1}"] = keywords

                return {
                    "sub_questions": sub_questions,
                    "keyword_sets": keyword_sets
                }

            return None

        except Exception as e:
            self.logger.error(f"Error parsing enhanced response: {e}")
            return None

    def _generate_keywords_for_question(self, question: str, main_topic: str) -> List[str]:
        """
        为特定子问题生成优化的关键词（完全依赖AI生成）

        Args:
            question: 子问题
            main_topic: 主要研究主题

        Returns:
            List[str]: 关键词列表
        """
        try:
            prompt = Prompts.KEYWORD_GENERATOR_PROMPT.format(
                sub_question=question,
                main_topic=main_topic
            )

            # 使用更高的max_tokens以获得更完整的关键词列表
            response = self.llm_client.generate(prompt, max_tokens=800, temperature=0.3)

            if response.is_successful():
                # 解析关键词JSON数组
                import json
                try:
                    # 清理响应文本，提取JSON部分
                    cleaned_response = response.response.strip()

                    # 查找JSON数组
                    import re
                    json_pattern = r'\[.*?\]'
                    json_match = re.search(json_pattern, cleaned_response, re.DOTALL)

                    if json_match:
                        json_str = json_match.group(0)
                        keywords = json.loads(json_str)

                        if isinstance(keywords, list):
                            # 过滤和清理关键词
                            clean_keywords = []
                            for kw in keywords:
                                if isinstance(kw, str) and kw.strip():
                                    clean_kw = kw.strip()
                                    # 确保关键词不为空且有意义
                                    if len(clean_kw) > 2 and clean_kw not in clean_keywords:
                                        clean_keywords.append(clean_kw)

                            if clean_keywords:
                                self.logger.info(f"AI generated {len(clean_keywords)} keywords for question: {question[:50]}...")
                                return clean_keywords[:8]  # 限制为最多8个关键词

                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse JSON keywords: {e}")

            # 如果AI生成失败，尝试重新生成一次
            self.logger.warning("First attempt failed, retrying keyword generation...")
            return self._retry_keyword_generation(question, main_topic)

        except Exception as e:
            self.logger.error(f"Error generating keywords for question: {e}")
            return self._retry_keyword_generation(question, main_topic)

    def _retry_keyword_generation(self, question: str, main_topic: str) -> List[str]:
        """
        重试关键词生成（使用简化的提示词）

        Args:
            question: 子问题
            main_topic: 主要研究主题

        Returns:
            List[str]: 关键词列表
        """
        try:
            # 使用简化的提示词进行重试
            simplified_prompt = f"""
Generate 5-8 academic search keywords for this research question: "{question}"
Main topic: "{main_topic}"

Return only a JSON array of keyword strings, for example:
["keyword1", "keyword2", "keyword3"]

Focus on academic terminology suitable for scientific database searches.
"""

            response = self.llm_client.generate(simplified_prompt, max_tokens=300, temperature=0.2)

            if response.is_successful():
                import json
                import re

                # 查找JSON数组
                json_pattern = r'\[.*?\]'
                json_match = re.search(json_pattern, response.response, re.DOTALL)

                if json_match:
                    json_str = json_match.group(0)
                    keywords = json.loads(json_str)

                    if isinstance(keywords, list):
                        clean_keywords = [kw.strip() for kw in keywords if isinstance(kw, str) and kw.strip()]
                        if clean_keywords:
                            self.logger.info(f"Retry successful: generated {len(clean_keywords)} keywords")
                            return clean_keywords[:6]

            # 如果重试也失败，返回基于主题的基础关键词
            self.logger.warning("Retry also failed, using fallback keywords")
            return self._create_fallback_keywords(question, main_topic)

        except Exception as e:
            self.logger.error(f"Error in retry keyword generation: {e}")
            return self._create_fallback_keywords(question, main_topic)

    def _create_fallback_keywords(self, question: str, main_topic: str) -> List[str]:
        """
        创建备用关键词（基于主题的基础关键词）

        Args:
            question: 子问题
            main_topic: 主要研究主题

        Returns:
            List[str]: 基础关键词列表
        """
        # 基于主题创建基础关键词，而不是文本处理
        fallback_keywords = []

        # 添加主题本身作为关键词
        if main_topic:
            fallback_keywords.append(main_topic)

        # 根据问题类型添加通用学术关键词
        question_lower = question.lower()

        if any(word in question_lower for word in ['fundamental', 'basic', 'concept', 'definition']):
            fallback_keywords.extend(['fundamentals', 'concepts', 'theory'])
        elif any(word in question_lower for word in ['history', 'evolution', 'development']):
            fallback_keywords.extend(['history', 'development', 'evolution'])
        elif any(word in question_lower for word in ['method', 'technique', 'approach']):
            fallback_keywords.extend(['methods', 'techniques', 'approaches'])
        elif any(word in question_lower for word in ['recent', 'advance', 'breakthrough']):
            fallback_keywords.extend(['recent advances', 'breakthroughs', 'innovations'])
        elif any(word in question_lower for word in ['application', 'practical', 'implementation']):
            fallback_keywords.extend(['applications', 'implementation', 'practical'])
        elif any(word in question_lower for word in ['challenge', 'problem', 'limitation']):
            fallback_keywords.extend(['challenges', 'limitations', 'problems'])
        elif any(word in question_lower for word in ['future', 'direction', 'trend']):
            fallback_keywords.extend(['future directions', 'trends', 'prospects'])

        # 去重并限制数量
        unique_keywords = []
        for kw in fallback_keywords:
            if kw not in unique_keywords:
                unique_keywords.append(kw)

        self.logger.info(f"Created {len(unique_keywords)} fallback keywords")
        return unique_keywords[:5]

    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """
        从文本中提取关键词（已弃用 - 仅作为最后备用）

        注意：此方法已被弃用，系统现在完全依赖AI生成关键词。
        此方法仅在极端情况下作为最后备用选项。

        Args:
            text: 输入文本

        Returns:
            List[str]: 关键词列表
        """
        self.logger.warning("Using deprecated text-based keyword extraction as last resort")

        # 返回基于主题的基础关键词，而不是文本处理
        # 这样可以避免简单的文本处理，同时保持向后兼容性
        return self._create_fallback_keywords("", text)
    
    def _extract_keywords(self, topic: str, sub_questions: List[str]) -> List[str]:
        """
        从主题和子问题中提取关键词（现在完全依赖AI生成）

        注意：此方法现在完全依赖AI生成关键词，不再使用简单的文本处理。
        所有关键词都通过LLM的智能分析生成，针对学术数据库搜索进行了优化。

        Args:
            topic: 主要研究课题
            sub_questions: 子问题列表

        Returns:
            List[str]: AI生成的关键词列表
        """
        self.logger.info("Generating keywords using AI-based approach")

        all_keywords = set()

        # 为每个子问题生成关键词
        for i, question in enumerate(sub_questions):
            try:
                question_keywords = self._generate_keywords_for_question(question, topic)
                all_keywords.update(question_keywords)
                self.logger.debug(f"Generated {len(question_keywords)} keywords for sub-question {i+1}")
            except Exception as e:
                self.logger.warning(f"Failed to generate keywords for sub-question {i+1}: {e}")

        # 如果没有生成任何关键词，使用主题生成通用关键词
        if not all_keywords:
            self.logger.warning("No keywords generated from sub-questions, generating from main topic")
            try:
                topic_keywords = self._generate_keywords_for_question(f"Research on {topic}", topic)
                all_keywords.update(topic_keywords)
            except Exception as e:
                self.logger.error(f"Failed to generate keywords from main topic: {e}")
                # 最后的备用方案
                all_keywords.update(self._create_fallback_keywords("", topic))

        # 转换为列表并限制数量
        final_keywords = list(all_keywords)[:15]  # 增加到15个关键词以提供更多搜索选项

        self.logger.info(f"Final keyword set contains {len(final_keywords)} unique keywords")
        return final_keywords
    
    def _extract_words_from_text(self, text: str) -> List[str]:
        """
        从文本中提取有意义的词汇（已弃用）

        注意：此方法已被弃用，系统现在完全依赖AI生成关键词。
        此方法不再被使用，保留仅为向后兼容性。

        Args:
            text: 输入文本

        Returns:
            List[str]: 空列表（不再执行文本处理）
        """
        self.logger.warning("_extract_words_from_text method is deprecated and no longer used")
        return []  # 返回空列表，不再执行文本处理
    
    def _create_fallback_plan(self, topic: str) -> ResearchQuery:
        """
        创建备用研究计划（当LLM失败时使用）
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchQuery: 基本的研究查询对象
        """
        self.logger.info("Creating fallback research plan")
        
        # 生成通用的子问题
        fallback_questions = [
            f"{topic}的基本概念和定义是什么？",
            f"{topic}的历史发展过程如何？",
            f"{topic}的主要理论和方法有哪些？",
            f"{topic}的最新研究进展是什么？",
            f"{topic}面临的主要挑战和问题有哪些？",
            f"{topic}的未来发展趋势和应用前景如何？"
        ]
        
        research_query = ResearchQuery(
            main_topic=topic,
            sub_questions=fallback_questions
        )
        
        # 从主题中提取基本关键词
        keywords = self._extract_keywords(topic, fallback_questions)
        for keyword in keywords:
            research_query.add_keyword(keyword)
        
        return research_query
    
    def validate_research_plan(self, research_query: ResearchQuery) -> bool:
        """
        验证研究计划的质量
        
        Args:
            research_query: 研究查询对象
            
        Returns:
            bool: 计划是否有效
        """
        if not research_query.main_topic:
            return False
        
        if len(research_query.sub_questions) < 3:
            return False
        
        # 检查子问题的质量
        for question in research_query.sub_questions:
            if len(question) < 10 or not question.endswith('?'):
                return False
        
        return True
