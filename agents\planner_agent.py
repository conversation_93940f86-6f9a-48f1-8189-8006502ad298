# agents/planner_agent.py
#
# 课题规划代理 - 负责将研究课题分解为关键子问题

import logging
import json
import re
from typing import List, Optional

from clients import LLMClient
from models import LLMResponse, ResearchQuery
from prompts import Prompts


class PlannerAgent:
    """课题规划代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化规划代理
        
        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        self.llm_client = llm_client or LLMClient()
        self.logger = logging.getLogger(__name__)
    
    def generate_research_plan(self, topic: str) -> ResearchQuery:
        """
        为给定课题生成研究计划
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchQuery: 包含主题和子问题的研究查询对象
        """
        self.logger.info(f"Generating research plan for topic: {topic}")
        
        # 使用提示词生成子问题
        prompt = Prompts.PLANNER_PROMPT.format(topic=topic)
        
        try:
            # 调用LLM生成响应
            response = self.llm_client.generate(prompt)
            
            if not response.is_successful():
                self.logger.error(f"LLM request failed: {response.error}")
                # 返回基本的研究查询，包含一些默认子问题
                return self._create_fallback_plan(topic)
            
            # 解析LLM响应中的子问题列表
            sub_questions = self._parse_sub_questions(response.response)
            
            if not sub_questions:
                self.logger.warning("No sub-questions parsed from LLM response, using fallback")
                return self._create_fallback_plan(topic)
            
            # 创建研究查询对象
            research_query = ResearchQuery(
                main_topic=topic,
                sub_questions=sub_questions
            )
            
            # 生成关键词
            keywords = self._extract_keywords(topic, sub_questions)
            for keyword in keywords:
                research_query.add_keyword(keyword)
            
            self.logger.info(f"Generated {len(sub_questions)} sub-questions and {len(keywords)} keywords")
            
            return research_query
        
        except Exception as e:
            self.logger.error(f"Error generating research plan: {e}")
            return self._create_fallback_plan(topic)
    
    def _parse_sub_questions(self, llm_response: str) -> List[str]:
        """
        从LLM响应中解析子问题列表
        
        Args:
            llm_response: LLM的响应文本
            
        Returns:
            List[str]: 子问题列表
        """
        sub_questions = []
        
        try:
            # 首先尝试解析JSON格式的列表
            # 查找类似 ["问题1", "问题2", ...] 的模式
            json_pattern = r'\[([^\]]+)\]'
            json_matches = re.findall(json_pattern, llm_response, re.DOTALL)
            
            for match in json_matches:
                try:
                    # 尝试解析为JSON数组
                    full_json = f'[{match}]'
                    parsed_list = json.loads(full_json)
                    if isinstance(parsed_list, list):
                        sub_questions.extend([str(item).strip().strip('"\'') for item in parsed_list])
                        break
                except json.JSONDecodeError:
                    continue
            
            # 如果JSON解析失败，尝试其他模式
            if not sub_questions:
                # 查找编号列表模式 (1. 问题, 2. 问题, ...)
                numbered_pattern = r'^\s*\d+\.\s*(.+?)(?=\n\s*\d+\.|$)'
                numbered_matches = re.findall(numbered_pattern, llm_response, re.MULTILINE | re.DOTALL)
                if numbered_matches:
                    sub_questions = [q.strip().rstrip('?') + '?' for q in numbered_matches]
                
                # 查找破折号列表模式 (- 问题, - 问题, ...)
                if not sub_questions:
                    dash_pattern = r'^\s*[-*]\s*(.+?)(?=\n\s*[-*]|$)'
                    dash_matches = re.findall(dash_pattern, llm_response, re.MULTILINE | re.DOTALL)
                    if dash_matches:
                        sub_questions = [q.strip().rstrip('?') + '?' for q in dash_matches]
                
                # 查找问号结尾的句子
                if not sub_questions:
                    question_pattern = r'([^.!?]*\?)'
                    question_matches = re.findall(question_pattern, llm_response)
                    if question_matches:
                        sub_questions = [q.strip() for q in question_matches if len(q.strip()) > 10]
            
            # 清理和验证子问题
            cleaned_questions = []
            for question in sub_questions:
                question = question.strip().strip('"\'')
                if question and len(question) > 5:  # 过滤太短的问题
                    if not question.endswith('?'):
                        question += '?'
                    cleaned_questions.append(question)
            
            # 限制子问题数量
            if len(cleaned_questions) > 7:
                cleaned_questions = cleaned_questions[:7]
            
            return cleaned_questions
        
        except Exception as e:
            self.logger.error(f"Error parsing sub-questions: {e}")
            return []
    
    def _extract_keywords(self, topic: str, sub_questions: List[str]) -> List[str]:
        """
        从主题和子问题中提取关键词
        
        Args:
            topic: 主要研究课题
            sub_questions: 子问题列表
            
        Returns:
            List[str]: 关键词列表
        """
        keywords = set()
        
        # 从主题中提取关键词
        topic_words = self._extract_words_from_text(topic)
        keywords.update(topic_words)
        
        # 从子问题中提取关键词
        for question in sub_questions:
            question_words = self._extract_words_from_text(question)
            keywords.update(question_words)
        
        # 过滤和清理关键词
        filtered_keywords = []
        stop_words = {'什么', '如何', '为什么', '哪些', '怎样', '是否', '能否', '可以', '应该', 
                     'what', 'how', 'why', 'which', 'when', 'where', 'can', 'should', 'is', 'are'}
        
        for keyword in keywords:
            if (len(keyword) > 2 and 
                keyword.lower() not in stop_words and 
                not keyword.isdigit()):
                filtered_keywords.append(keyword)
        
        return list(set(filtered_keywords))[:10]  # 限制关键词数量
    
    def _extract_words_from_text(self, text: str) -> List[str]:
        """
        从文本中提取有意义的词汇
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 词汇列表
        """
        # 简单的词汇提取，可以根据需要改进
        import re
        
        # 移除标点符号，分割单词
        words = re.findall(r'\b\w+\b', text)
        
        # 过滤长度
        meaningful_words = [word for word in words if len(word) > 2]
        
        return meaningful_words
    
    def _create_fallback_plan(self, topic: str) -> ResearchQuery:
        """
        创建备用研究计划（当LLM失败时使用）
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchQuery: 基本的研究查询对象
        """
        self.logger.info("Creating fallback research plan")
        
        # 生成通用的子问题
        fallback_questions = [
            f"{topic}的基本概念和定义是什么？",
            f"{topic}的历史发展过程如何？",
            f"{topic}的主要理论和方法有哪些？",
            f"{topic}的最新研究进展是什么？",
            f"{topic}面临的主要挑战和问题有哪些？",
            f"{topic}的未来发展趋势和应用前景如何？"
        ]
        
        research_query = ResearchQuery(
            main_topic=topic,
            sub_questions=fallback_questions
        )
        
        # 从主题中提取基本关键词
        keywords = self._extract_keywords(topic, fallback_questions)
        for keyword in keywords:
            research_query.add_keyword(keyword)
        
        return research_query
    
    def validate_research_plan(self, research_query: ResearchQuery) -> bool:
        """
        验证研究计划的质量
        
        Args:
            research_query: 研究查询对象
            
        Returns:
            bool: 计划是否有效
        """
        if not research_query.main_topic:
            return False
        
        if len(research_query.sub_questions) < 3:
            return False
        
        # 检查子问题的质量
        for question in research_query.sub_questions:
            if len(question) < 10 or not question.endswith('?'):
                return False
        
        return True
