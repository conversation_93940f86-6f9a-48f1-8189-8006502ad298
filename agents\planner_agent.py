# agents/planner_agent.py
#
# 课题规划代理 - 负责将研究课题分解为关键子问题 (重构优化版)

import logging
import json
import re
from typing import List, Optional, Dict, Any, Tuple

from clients import LLMClient
from models import LLMResponse, ResearchQuery
from config import config


class PlannerAgent:
    """
    课题规划代理 - 优化版
    
    使用单次AI调用生成完整的研究计划，包括子问题和学术优化的关键词。
    移除了所有文本处理和备用逻辑，完全依赖AI生成。
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化规划代理
        
        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为PlannerAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("planner")
        self.logger = logging.getLogger(__name__)

    def generate_research_plan(self, topic: str) -> ResearchQuery:
        """
        为给定课题生成研究计划 - 单次AI调用优化版
        
        使用单次AI调用获取完整的研究计划，包括子问题和学术优化的关键词。
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchQuery: 包含主题、子问题和优化关键词的研究查询对象
        """
        self.logger.info(f"Generating research plan for topic: {topic}")
        
        # 获取代理特定配置
        agent_config = config.get_agent_config("planner")
        
        # 使用优化的综合提示词
        prompt = self._create_comprehensive_prompt(topic)
        
        try:
            # 单次AI调用获取完整研究计划
            response = self.llm_client.generate(
                prompt,
                temperature=agent_config.get("temperature", 0.7),
                max_tokens=agent_config.get("max_tokens", 8000)
            )
            
            if not response.is_successful():
                self.logger.error(f"AI request failed: {response.error}")
                raise Exception(f"AI generation failed: {response.error}")
            
            # 解析AI响应
            research_data = self._parse_ai_response(response.response)
            
            # 创建研究查询对象
            research_query = ResearchQuery(
                main_topic=topic,
                sub_questions=research_data["sub_questions"]
            )
            
            # 添加所有关键词
            all_keywords = set()
            
            # 添加通用关键词
            if research_data.get("general_keywords"):
                all_keywords.update(research_data["general_keywords"])
            
            # 添加子问题特定关键词
            if research_data.get("sub_question_keywords"):
                for keywords in research_data["sub_question_keywords"].values():
                    all_keywords.update(keywords)
                # 存储子问题关键词映射
                research_query.sub_question_keywords = research_data["sub_question_keywords"]
            
            # 添加关键词到研究查询
            for keyword in all_keywords:
                research_query.add_keyword(keyword)
            
            self.logger.info(f"Generated {len(research_data['sub_questions'])} sub-questions and {len(all_keywords)} keywords")
            
            return research_query
        
        except Exception as e:
            self.logger.error(f"Error generating research plan: {e}")
            raise Exception(f"Failed to generate research plan: {e}")
    
    def _create_comprehensive_prompt(self, topic: str) -> str:
        """
        创建综合性的AI提示词，一次性获取所有需要的信息
        
        Args:
            topic: 研究主题
            
        Returns:
            str: 优化的提示词
        """
        return f"""You are an expert research planner specializing in academic research. Your task is to create a comprehensive research plan for the given topic.

Research Topic: "{topic}"

Please generate a complete research plan with the following structure:

1. **Sub-questions**: Create 5-7 specific, focused research questions that comprehensively cover the topic
2. **General Keywords**: Generate 8-12 general academic keywords for the overall topic
3. **Sub-question Keywords**: For each sub-question, provide 3-5 specific academic keywords optimized for scholarly database searches (like ADS, arXiv, PubMed)

**Requirements:**
- All keywords should be optimized for academic database searches
- Use precise scientific terminology
- Include both broad and specific terms
- Consider synonyms and related concepts
- Focus on terms that would appear in academic paper titles and abstracts

**Output Format:**
Return your response as a valid JSON object with this exact structure:

{{
    "sub_questions": [
        "Question 1?",
        "Question 2?",
        "Question 3?",
        "Question 4?",
        "Question 5?"
    ],
    "general_keywords": [
        "keyword1",
        "keyword2",
        "keyword3"
    ],
    "sub_question_keywords": {{
        "sub_question_1": ["keyword1", "keyword2", "keyword3"],
        "sub_question_2": ["keyword4", "keyword5", "keyword6"],
        "sub_question_3": ["keyword7", "keyword8", "keyword9"],
        "sub_question_4": ["keyword10", "keyword11", "keyword12"],
        "sub_question_5": ["keyword13", "keyword14", "keyword15"]
    }}
}}

Ensure the JSON is valid and properly formatted. Focus on creating high-quality, academically relevant content."""
    
    def _parse_ai_response(self, ai_response: str) -> Dict[str, Any]:
        """
        解析AI响应，提取研究计划数据
        
        Args:
            ai_response: AI的响应文本
            
        Returns:
            Dict[str, Any]: 包含子问题和关键词的字典
        """
        try:
            # 清理响应文本
            cleaned_response = ai_response.strip()
            
            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)
            
            if not json_match:
                raise ValueError("No JSON object found in AI response")
            
            json_str = json_match.group(0)
            parsed_data = json.loads(json_str)
            
            # 验证必需字段
            required_fields = ["sub_questions", "general_keywords", "sub_question_keywords"]
            for field in required_fields:
                if field not in parsed_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # 验证和清理子问题
            sub_questions = []
            for q in parsed_data["sub_questions"]:
                if isinstance(q, str) and len(q.strip()) > 10:
                    question = q.strip()
                    if not question.endswith('?'):
                        question += '?'
                    sub_questions.append(question)
            
            if len(sub_questions) < 3:
                raise ValueError("Insufficient number of valid sub-questions")
            
            # 验证和清理通用关键词
            general_keywords = []
            for kw in parsed_data["general_keywords"]:
                if isinstance(kw, str) and len(kw.strip()) > 2:
                    general_keywords.append(kw.strip())
            
            # 验证和清理子问题关键词
            sub_question_keywords = {}
            if isinstance(parsed_data["sub_question_keywords"], dict):
                for i, (_, keywords) in enumerate(parsed_data["sub_question_keywords"].items()):
                    if isinstance(keywords, list):
                        clean_keywords = [kw.strip() for kw in keywords if isinstance(kw, str) and len(kw.strip()) > 2]
                        if clean_keywords:
                            sub_question_keywords[f"sub_question_{i+1}"] = clean_keywords[:5]  # 限制每个子问题最多5个关键词
            
            result = {
                "sub_questions": sub_questions[:7],  # 限制最多7个子问题
                "general_keywords": general_keywords[:12],  # 限制最多12个通用关键词
                "sub_question_keywords": sub_question_keywords
            }
            
            self.logger.info(f"Successfully parsed AI response: {len(result['sub_questions'])} questions, "
                           f"{len(result['general_keywords'])} general keywords, "
                           f"{len(result['sub_question_keywords'])} sub-question keyword sets")
            
            return result
        
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON parsing error: {e}")
            raise ValueError(f"Invalid JSON in AI response: {e}")
        
        except Exception as e:
            self.logger.error(f"Error parsing AI response: {e}")
            raise ValueError(f"Failed to parse AI response: {e}")

    def generate_research_plan_with_confirmation(self, topic: str, use_gui: bool = True) -> ResearchQuery:
        """
        Generate research plan with user confirmation

        Args:
            topic: Research topic
            use_gui: Whether to use GUI for confirmation (default: True)

        Returns:
            ResearchQuery: Confirmed research query object
        """
        self.logger.info(f"Generating research plan with confirmation for: {topic}")

        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            self.logger.info(f"Generation attempt {attempt}/{max_attempts}")

            try:
                # Generate initial plan
                query = self.generate_research_plan(topic)

                if use_gui:
                    # Show GUI confirmation dialog
                    result = self._show_gui_confirmation(topic, query)
                else:
                    # Show console confirmation
                    result = self._show_console_confirmation(topic, query)

                if result is None:
                    # User cancelled
                    raise KeyboardInterrupt("User cancelled research plan generation")

                action, sub_questions, keywords, sub_question_keywords = result

                if action == 'confirm':
                    # User confirmed, update query if modified
                    query.sub_questions = sub_questions
                    query.keywords = keywords
                    query.sub_question_keywords = sub_question_keywords or {}
                    self.logger.info("Research plan confirmed by user")
                    return query

                elif action == 'regenerate':
                    # User wants to regenerate
                    self.logger.info("User requested plan regeneration")
                    continue

                else:
                    raise ValueError(f"Unknown action: {action}")

            except KeyboardInterrupt:
                raise
            except Exception as e:
                self.logger.error(f"Error in attempt {attempt}: {e}")
                if attempt >= max_attempts:
                    raise
                continue

        raise RuntimeError(f"Failed to generate acceptable research plan after {max_attempts} attempts")

    def _show_gui_confirmation(self, topic: str, query: ResearchQuery) -> Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]:
        """Show GUI confirmation dialog"""
        try:
            from gui.planner_review_dialog import show_planner_review_dialog

            # Estimate papers and time
            estimated_papers = len(query.keywords) * 20  # Rough estimate
            estimated_time = f"{estimated_papers // 10}-{estimated_papers // 8} minutes"

            return show_planner_review_dialog(
                topic=topic,
                sub_questions=query.sub_questions,
                keywords=query.keywords,
                estimated_papers=estimated_papers,
                estimated_time=estimated_time,
                sub_question_keywords=query.sub_question_keywords
            )

        except ImportError as e:
            self.logger.warning(f"GUI not available, falling back to console: {e}")
            return self._show_console_confirmation(topic, query)
        except Exception as e:
            self.logger.error(f"Error showing GUI confirmation: {e}")
            return self._show_console_confirmation(topic, query)

    def _show_console_confirmation(self, topic: str, query: ResearchQuery) -> Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]:
        """Show console confirmation dialog"""
        print("\n" + "="*80)
        print("RESEARCH PLAN REVIEW")
        print("="*80)
        print(f"Topic: {topic}")
        print(f"\nSub-questions ({len(query.sub_questions)}):")
        for i, question in enumerate(query.sub_questions, 1):
            print(f"  {i}. {question}")
            # 显示子问题关键词
            sub_keywords = query.get_keywords_for_subquestion(i-1)
            if sub_keywords:
                print(f"     Keywords: {', '.join(sub_keywords)}")

        print(f"\nGeneral Keywords ({len(query.keywords)}):")
        for i, keyword in enumerate(query.keywords, 1):
            print(f"  {i}. {keyword}")

        estimated_papers = len(query.keywords) * 20
        print(f"\nEstimated papers: {estimated_papers}")
        print(f"Estimated time: {estimated_papers // 10}-{estimated_papers // 8} minutes")

        print("\nOptions:")
        print("1. Confirm and continue")
        print("2. Regenerate plan")
        print("3. Cancel")

        while True:
            choice = input("\nEnter your choice (1-3): ").strip()

            if choice == '1':
                return ('confirm', query.sub_questions, query.keywords, query.sub_question_keywords or {})
            elif choice == '2':
                return ('regenerate', [], [], {})
            elif choice == '3':
                return None
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")
