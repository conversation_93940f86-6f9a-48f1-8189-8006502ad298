# agents/planner_agent.py
#
# 课题规划代理 - 负责将研究课题分解为关键子问题 (重构优化版)

import logging
import json
import re
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
from datetime import datetime

from clients import LLMClient
from models import LLMResponse, ResearchQuery
from config import config
from utils.research_plan_storage import ResearchPlanStorage


class PlannerAgent:
    """
    课题规划代理 - 优化版
    
    使用单次AI调用生成完整的研究计划，包括子问题和学术优化的关键词。
    移除了所有文本处理和备用逻辑，完全依赖AI生成。
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化规划代理
        
        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为PlannerAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("planner")
        self.logger = logging.getLogger(__name__)

        # 初始化研究计划存储
        self.plan_storage = ResearchPlanStorage()

    def generate_research_plan(self, topic: str) -> ResearchQuery:
        """
        为给定课题生成研究计划 - 单次AI调用优化版
        
        使用单次AI调用获取完整的研究计划，包括子问题和学术优化的关键词。
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchQuery: 包含主题、子问题和优化关键词的研究查询对象
        """
        self.logger.info(f"Generating research plan for topic: {topic}")
        
        # 获取代理特定配置
        agent_config = config.get_agent_config("planner")
        
        # 使用优化的综合提示词
        prompt = self._create_comprehensive_prompt(topic)
        
        try:
            # 单次AI调用获取完整研究计划
            response = self.llm_client.generate(
                prompt,
                temperature=agent_config.get("temperature", 0.7),
                max_tokens=agent_config.get("max_tokens", 8000)
            )
            
            if not response.is_successful():
                self.logger.error(f"AI request failed: {response.error}")
                raise Exception(f"AI generation failed: {response.error}")
            
            # 解析AI响应
            research_data = self._parse_ai_response(response.response)
            
            # 创建研究查询对象
            research_query = ResearchQuery(
                main_topic=topic,
                sub_questions=research_data["sub_questions"]
            )
            
            # 添加所有关键词
            all_keywords = set()
            
            # 添加通用关键词
            if research_data.get("general_keywords"):
                all_keywords.update(research_data["general_keywords"])
            
            # 添加子问题特定关键词
            if research_data.get("sub_question_keywords"):
                for keywords in research_data["sub_question_keywords"].values():
                    all_keywords.update(keywords)
                # 存储子问题关键词映射
                research_query.sub_question_keywords = research_data["sub_question_keywords"]

            # 存储ADS搜索建议
            if research_data.get("ads_search_suggestions"):
                research_query.ads_search_suggestions = research_data["ads_search_suggestions"]

            # 添加关键词到研究查询
            for keyword in all_keywords:
                research_query.add_keyword(keyword)

            # 保存研究计划到outputs目录
            self._save_research_plan(research_query)

            self.logger.info(f"Generated {len(research_data['sub_questions'])} sub-questions and {len(all_keywords)} keywords")

            return research_query
        
        except Exception as e:
            self.logger.error(f"Error generating research plan: {e}")
            raise Exception(f"Failed to generate research plan: {e}")
    
    def _create_comprehensive_prompt(self, topic: str) -> str:
        """
        创建增强的AI提示词，包含ADS搜索建议

        Args:
            topic: 研究主题

        Returns:
            str: 增强的提示词
        """
        return f"""You are an expert astrophysics research planner with deep knowledge of astronomical phenomena, observational techniques, theoretical models, computational methods, and NASA ADS database search strategies. Your task is to create a comprehensive research plan specifically tailored for astrophysical research with optimized ADS search strategies.

Research Topic: "{topic}"

**Astrophysics Research Framework:**
Generate a research plan that considers the unique aspects of astrophysical research:
- Observational data from telescopes and space missions
- Theoretical modeling and analytical approaches
- Numerical simulations and computational astrophysics
- Multi-wavelength and multi-messenger astronomy
- Statistical analysis of large astronomical datasets

**Research Plan Structure:**

1. **Sub-questions**: Create 5-7 specific research questions that cover:
   - Observational aspects (what can be observed/measured)
   - Theoretical understanding (physical mechanisms and models)
   - Methodological approaches (techniques and instruments)
   - Computational/simulation aspects (if applicable)
   - Current challenges and future prospects

2. **General Keywords**: Generate 8-12 astrophysics-specific keywords optimized for ADS (Astrophysics Data System)

3. **Sub-question Keywords**: For each sub-question, provide 3-5 specialized keywords that would appear in astrophysical literature

4. **ADS Search Suggestions**: For each sub-question, provide specific NASA ADS database search recommendations including:
   - Recommended search fields (title, abstract, keywords, author)
   - Suggested search operators (AND, OR, NOT)
   - Publication date ranges when relevant
   - Specific journal or conference filters

**Astrophysics Keyword Requirements:**
- Use standard astronomical terminology and nomenclature
- Include observational terms (photometry, spectroscopy, astrometry)
- Consider theoretical concepts (stellar evolution, galaxy formation, cosmology)
- Include instrument/mission names when relevant
- Use both technical terms and common astrophysical objects/phenomena
- Optimize for ADS database searches (the primary astrophysics literature database)

**Output Format:**
Return your response as a valid JSON object with this exact structure:

{{
    "sub_questions": [
        "What observational techniques and instruments are most effective for [specific aspect]?",
        "What are the current theoretical models explaining [phenomenon] and their limitations?",
        "How do numerical simulations contribute to understanding [process]?",
        "What are the key physical parameters and their observational signatures?",
        "What are the current challenges and future research directions?"
    ],
    "general_keywords": [
        "astrophysical_term1",
        "observational_technique",
        "theoretical_model",
        "astronomical_object"
    ],
    "sub_question_keywords": {{
        "sub_question_1": ["photometry", "spectroscopy", "telescope_name"],
        "sub_question_2": ["stellar_evolution", "galaxy_formation", "cosmological_model"],
        "sub_question_3": ["numerical_simulation", "hydrodynamics", "N-body"],
        "sub_question_4": ["physical_parameter", "observational_signature", "multi-wavelength"],
        "sub_question_5": ["future_missions", "next_generation", "emerging_techniques"]
    }},
    "ads_search_suggestions": {{
        "sub_question_1": {{
            "search_strategy": "title:(photometry OR spectroscopy) AND database:astronomy",
            "fields": ["title", "abstract"],
            "date_range": "2020-2024",
            "notes": "Focus on recent observational techniques"
        }},
        "sub_question_2": {{
            "search_strategy": "abstract:(stellar evolution) AND keywords:(galaxy formation)",
            "fields": ["abstract", "keywords"],
            "date_range": "2015-2024",
            "notes": "Include both theoretical and observational papers"
        }}
    }}
}}

**Important:**
- Use precise astrophysical terminology that would appear in ADS database
- Include both observational and theoretical perspectives
- Consider multi-wavelength approaches when relevant
- Ensure JSON is valid and properly formatted
- Focus on creating research questions that reflect current astrophysical research methodologies"""
    
    def _parse_ai_response(self, ai_response: str) -> Dict[str, Any]:
        """
        解析AI响应，提取研究计划数据
        
        Args:
            ai_response: AI的响应文本
            
        Returns:
            Dict[str, Any]: 包含子问题和关键词的字典
        """
        try:
            # 清理响应文本
            cleaned_response = ai_response.strip()
            
            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)
            
            if not json_match:
                raise ValueError("No JSON object found in AI response")
            
            json_str = json_match.group(0)
            parsed_data = json.loads(json_str)
            
            # 验证必需字段
            required_fields = ["sub_questions", "general_keywords", "sub_question_keywords"]
            for field in required_fields:
                if field not in parsed_data:
                    raise ValueError(f"Missing required field: {field}")

            # ADS搜索建议是可选的
            ads_suggestions = parsed_data.get("ads_search_suggestions", {})
            
            # 验证和清理子问题
            sub_questions = []
            for q in parsed_data["sub_questions"]:
                if isinstance(q, str) and len(q.strip()) > 10:
                    question = q.strip()
                    if not question.endswith('?'):
                        question += '?'
                    sub_questions.append(question)
            
            if len(sub_questions) < 3:
                raise ValueError("Insufficient number of valid sub-questions")
            
            # 验证和清理通用关键词
            general_keywords = []
            for kw in parsed_data["general_keywords"]:
                if isinstance(kw, str) and len(kw.strip()) > 2:
                    general_keywords.append(kw.strip())
            
            # 验证和清理子问题关键词
            sub_question_keywords = {}
            if isinstance(parsed_data["sub_question_keywords"], dict):
                for i, (_, keywords) in enumerate(parsed_data["sub_question_keywords"].items()):
                    if isinstance(keywords, list):
                        clean_keywords = [kw.strip() for kw in keywords if isinstance(kw, str) and len(kw.strip()) > 2]
                        if clean_keywords:
                            sub_question_keywords[f"sub_question_{i+1}"] = clean_keywords[:5]  # 限制每个子问题最多5个关键词
            
            result = {
                "sub_questions": sub_questions[:7],  # 限制最多7个子问题
                "general_keywords": general_keywords[:12],  # 限制最多12个通用关键词
                "sub_question_keywords": sub_question_keywords,
                "ads_search_suggestions": ads_suggestions  # 包含ADS搜索建议
            }
            
            self.logger.info(f"Successfully parsed AI response: {len(result['sub_questions'])} questions, "
                           f"{len(result['general_keywords'])} general keywords, "
                           f"{len(result['sub_question_keywords'])} sub-question keyword sets")
            
            return result
        
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON parsing error: {e}")
            raise ValueError(f"Invalid JSON in AI response: {e}")
        
        except Exception as e:
            self.logger.error(f"Error parsing AI response: {e}")
            raise ValueError(f"Failed to parse AI response: {e}")

    def _save_research_plan(self, query: ResearchQuery) -> str:
        """
        保存研究计划到outputs目录

        Args:
            query: 研究查询对象

        Returns:
            str: 保存的文件路径
        """
        try:
            # 确保outputs目录存在
            outputs_dir = Path("outputs")
            outputs_dir.mkdir(exist_ok=True)

            # 生成文件名：research_plan_[研究主题简化名]_[时间戳].json
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 简化主题名（移除特殊字符，限制长度）
            simplified_topic = re.sub(r'[^\w\s-]', '', query.main_topic)
            simplified_topic = re.sub(r'[-\s]+', '_', simplified_topic)
            simplified_topic = simplified_topic[:30]  # 限制长度

            filename = f"research_plan_{simplified_topic}_{timestamp}.json"
            file_path = outputs_dir / filename

            # 准备保存的数据
            plan_data = {
                "main_topic": query.main_topic,
                "sub_questions": query.sub_questions,
                "general_keywords": query.keywords,
                "sub_question_keywords": query.sub_question_keywords or {},
                "ads_search_suggestions": query.ads_search_suggestions or {},
                "created_at": query.created_at.isoformat(),
                "saved_at": datetime.now().isoformat()
            }

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(plan_data, f, indent=2, ensure_ascii=False)

            # 同时保存到ResearchPlanStorage以供GUI历史计划功能使用
            try:
                plan_id = self.plan_storage.save_research_plan(
                    main_topic=query.main_topic,
                    sub_questions=query.sub_questions,
                    general_keywords=query.keywords,
                    sub_question_keywords=query.sub_question_keywords or {}
                )
                self.logger.info(f"Research plan also saved to storage with ID: {plan_id}")
            except Exception as storage_error:
                self.logger.warning(f"Failed to save to plan storage: {storage_error}")

            self.logger.info(f"Research plan saved to: {file_path}")
            return str(file_path)

        except Exception as e:
            self.logger.error(f"Error saving research plan: {e}")
            # 不抛出异常，只记录错误，不影响主流程
            return ""

    def generate_research_plan_with_confirmation(self, topic: str, use_gui: bool = True) -> ResearchQuery:
        """
        Generate research plan with user confirmation

        Args:
            topic: Research topic
            use_gui: Whether to use GUI for confirmation (default: True)

        Returns:
            ResearchQuery: Confirmed research query object
        """
        self.logger.info(f"Generating research plan with confirmation for: {topic}")

        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            self.logger.info(f"Generation attempt {attempt}/{max_attempts}")

            try:
                # Generate initial plan
                query = self.generate_research_plan(topic)

                if use_gui:
                    # Show GUI confirmation dialog
                    result = self._show_gui_confirmation(topic, query)
                else:
                    # Show console confirmation
                    result = self._show_console_confirmation(topic, query)

                if result is None:
                    # User cancelled
                    raise KeyboardInterrupt("User cancelled research plan generation")

                action, sub_questions, keywords, sub_question_keywords = result

                if action == 'confirm':
                    # User confirmed, update query if modified
                    query.sub_questions = sub_questions
                    query.keywords = keywords
                    query.sub_question_keywords = sub_question_keywords or {}
                    self.logger.info("Research plan confirmed by user")
                    return query

                elif action == 'regenerate':
                    # User wants to regenerate
                    self.logger.info("User requested plan regeneration")
                    continue

                else:
                    raise ValueError(f"Unknown action: {action}")

            except KeyboardInterrupt:
                raise
            except Exception as e:
                self.logger.error(f"Error in attempt {attempt}: {e}")
                if attempt >= max_attempts:
                    raise
                continue

        raise RuntimeError(f"Failed to generate acceptable research plan after {max_attempts} attempts")

    def _show_gui_confirmation(self, topic: str, query: ResearchQuery) -> Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]:
        """Show GUI confirmation dialog"""
        try:
            from gui.planner_review_dialog import show_planner_review_dialog

            # Estimate papers and time
            estimated_papers = len(query.keywords) * 20  # Rough estimate
            estimated_time = f"{estimated_papers // 10}-{estimated_papers // 8} minutes"

            return show_planner_review_dialog(
                topic=topic,
                sub_questions=query.sub_questions,
                keywords=query.keywords,
                estimated_papers=estimated_papers,
                estimated_time=estimated_time,
                sub_question_keywords=query.sub_question_keywords,
                ads_search_suggestions=getattr(query, 'ads_search_suggestions', {})
            )

        except ImportError as e:
            self.logger.warning(f"GUI not available, falling back to console: {e}")
            return self._show_console_confirmation(topic, query)
        except Exception as e:
            self.logger.error(f"Error showing GUI confirmation: {e}")
            return self._show_console_confirmation(topic, query)

    def _show_console_confirmation(self, topic: str, query: ResearchQuery) -> Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]:
        """Show console confirmation dialog"""
        print("\n" + "="*80)
        print("RESEARCH PLAN REVIEW")
        print("="*80)
        print(f"Topic: {topic}")
        print(f"\nSub-questions ({len(query.sub_questions)}):")
        for i, question in enumerate(query.sub_questions, 1):
            print(f"  {i}. {question}")
            # 显示子问题关键词
            sub_keywords = query.get_keywords_for_subquestion(i-1)
            if sub_keywords:
                print(f"     Keywords: {', '.join(sub_keywords)}")

        print(f"\nGeneral Keywords ({len(query.keywords)}):")
        for i, keyword in enumerate(query.keywords, 1):
            print(f"  {i}. {keyword}")

        estimated_papers = len(query.keywords) * 20
        print(f"\nEstimated papers: {estimated_papers}")
        print(f"Estimated time: {estimated_papers // 10}-{estimated_papers // 8} minutes")

        print("\nOptions:")
        print("1. Confirm and continue")
        print("2. Regenerate plan")
        print("3. Cancel")

        while True:
            choice = input("\nEnter your choice (1-3): ").strip()

            if choice == '1':
                return ('confirm', query.sub_questions, query.keywords, query.sub_question_keywords or {})
            elif choice == '2':
                return ('regenerate', [], [], {})
            elif choice == '3':
                return None
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")
