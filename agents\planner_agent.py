# agents/planner_agent.py
#
# 课题规划代理 - 负责将研究课题分解为关键子问题

import logging
import json
import re
from typing import List, Optional, Dict, Any

from clients import LLMClient
from models import LLMResponse, ResearchQuery
from prompts import Prompts
from config import config


class PlannerAgent:
    """课题规划代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化规划代理

        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为PlannerAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("planner")
        self.logger = logging.getLogger(__name__)
    
    def generate_research_plan(self, topic: str) -> ResearchQuery:
        """
        为给定课题生成增强的研究计划，包含子问题和优化的关键词

        Args:
            topic: 研究课题

        Returns:
            ResearchQuery: 包含主题、子问题和优化关键词的研究查询对象
        """
        self.logger.info(f"Generating enhanced research plan for topic: {topic}")

        # 获取代理特定配置
        agent_config = config.get_agent_config("planner")

        # 使用增强的提示词生成子问题和关键词
        prompt = Prompts.PLANNER_PROMPT.format(topic=topic)

        try:
            # 调用LLM生成响应
            response = self.llm_client.generate(
                prompt,
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE),
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS)
            )

            if not response.is_successful():
                self.logger.error(f"LLM request failed: {response.error}")
                return self._create_fallback_plan(topic)

            # 解析增强的LLM响应（包含子问题和关键词）
            parsed_result = self._parse_enhanced_response(response.response, topic)

            if not parsed_result:
                self.logger.warning("Failed to parse enhanced response, using fallback")
                return self._create_fallback_plan(topic)

            # 创建研究查询对象
            research_query = ResearchQuery(
                main_topic=topic,
                sub_questions=parsed_result["sub_questions"]
            )

            # 添加优化的关键词
            for keyword_set in parsed_result["keyword_sets"].values():
                for keyword in keyword_set:
                    research_query.add_keyword(keyword)

            # 存储子问题到关键词的映射（用于后续检索）
            research_query.sub_question_keywords = parsed_result["keyword_sets"]

            self.logger.info(f"Generated {len(parsed_result['sub_questions'])} sub-questions with optimized keywords")

            return research_query

        except Exception as e:
            self.logger.error(f"Error generating research plan: {e}")
            return self._create_fallback_plan(topic)
    
    def _parse_sub_questions(self, llm_response: str) -> List[str]:
        """
        从LLM响应中解析子问题列表
        
        Args:
            llm_response: LLM的响应文本
            
        Returns:
            List[str]: 子问题列表
        """
        sub_questions = []
        
        try:
            # 首先尝试解析JSON格式的列表
            # 查找类似 ["问题1", "问题2", ...] 的模式
            json_pattern = r'\[([^\]]+)\]'
            json_matches = re.findall(json_pattern, llm_response, re.DOTALL)
            
            for match in json_matches:
                try:
                    # 尝试解析为JSON数组
                    full_json = f'[{match}]'
                    parsed_list = json.loads(full_json)
                    if isinstance(parsed_list, list):
                        sub_questions.extend([str(item).strip().strip('"\'') for item in parsed_list])
                        break
                except json.JSONDecodeError:
                    continue
            
            # 如果JSON解析失败，尝试其他模式
            if not sub_questions:
                # 查找编号列表模式 (1. 问题, 2. 问题, ...)
                numbered_pattern = r'^\s*\d+\.\s*(.+?)(?=\n\s*\d+\.|$)'
                numbered_matches = re.findall(numbered_pattern, llm_response, re.MULTILINE | re.DOTALL)
                if numbered_matches:
                    sub_questions = [q.strip().rstrip('?') + '?' for q in numbered_matches]
                
                # 查找破折号列表模式 (- 问题, - 问题, ...)
                if not sub_questions:
                    dash_pattern = r'^\s*[-*]\s*(.+?)(?=\n\s*[-*]|$)'
                    dash_matches = re.findall(dash_pattern, llm_response, re.MULTILINE | re.DOTALL)
                    if dash_matches:
                        sub_questions = [q.strip().rstrip('?') + '?' for q in dash_matches]
                
                # 查找问号结尾的句子
                if not sub_questions:
                    question_pattern = r'([^.!?]*\?)'
                    question_matches = re.findall(question_pattern, llm_response)
                    if question_matches:
                        sub_questions = [q.strip() for q in question_matches if len(q.strip()) > 10]
            
            # 清理和验证子问题
            cleaned_questions = []
            for question in sub_questions:
                question = question.strip().strip('"\'')
                if question and len(question) > 5:  # 过滤太短的问题
                    if not question.endswith('?'):
                        question += '?'
                    cleaned_questions.append(question)
            
            # 限制子问题数量
            if len(cleaned_questions) > 7:
                cleaned_questions = cleaned_questions[:7]
            
            return cleaned_questions

        except Exception as e:
            self.logger.error(f"Error parsing sub-questions: {e}")
            return []

    def _parse_enhanced_response(self, llm_response: str, topic: str) -> Optional[Dict[str, Any]]:
        """
        解析增强的LLM响应，包含子问题和关键词

        Args:
            llm_response: LLM的响应文本
            topic: 研究主题

        Returns:
            Dict[str, Any]: 包含sub_questions和keyword_sets的字典，失败时返回None
        """
        try:
            # 首先尝试解析JSON格式的响应
            import json

            # 清理响应文本
            cleaned_response = llm_response.strip()

            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                try:
                    parsed_data = json.loads(json_str)

                    # 验证必需字段
                    if "sub_questions" in parsed_data and "keyword_sets" in parsed_data:
                        # 清理和验证子问题
                        sub_questions = []
                        for q in parsed_data["sub_questions"]:
                            if isinstance(q, str) and len(q.strip()) > 5:
                                question = q.strip()
                                if not question.endswith('?'):
                                    question += '?'
                                sub_questions.append(question)

                        # 验证关键词集合
                        keyword_sets = {}
                        if isinstance(parsed_data["keyword_sets"], dict):
                            for i, (key, keywords) in enumerate(parsed_data["keyword_sets"].items()):
                                if isinstance(keywords, list):
                                    clean_keywords = [kw.strip() for kw in keywords if isinstance(kw, str) and kw.strip()]
                                    if clean_keywords:
                                        keyword_sets[f"sub_question_{i+1}"] = clean_keywords

                        if sub_questions and keyword_sets:
                            return {
                                "sub_questions": sub_questions[:7],  # 限制为最多7个子问题
                                "keyword_sets": keyword_sets
                            }

                except json.JSONDecodeError:
                    pass

            # 如果JSON解析失败，尝试生成基于传统方法的结果
            self.logger.warning("Failed to parse JSON response, falling back to traditional parsing")
            sub_questions = self._parse_sub_questions(llm_response)

            if sub_questions:
                # 为每个子问题生成关键词
                keyword_sets = {}
                for i, question in enumerate(sub_questions):
                    keywords = self._generate_keywords_for_question(question, topic)
                    if keywords:
                        keyword_sets[f"sub_question_{i+1}"] = keywords

                return {
                    "sub_questions": sub_questions,
                    "keyword_sets": keyword_sets
                }

            return None

        except Exception as e:
            self.logger.error(f"Error parsing enhanced response: {e}")
            return None

    def _generate_keywords_for_question(self, question: str, main_topic: str) -> List[str]:
        """
        为特定子问题生成优化的关键词

        Args:
            question: 子问题
            main_topic: 主要研究主题

        Returns:
            List[str]: 关键词列表
        """
        try:
            prompt = Prompts.KEYWORD_GENERATOR_PROMPT.format(
                sub_question=question,
                main_topic=main_topic
            )

            response = self.llm_client.generate(prompt, max_tokens=500)

            if response.is_successful():
                # 解析关键词JSON数组
                import json
                try:
                    keywords = json.loads(response.response)
                    if isinstance(keywords, list):
                        return [kw.strip() for kw in keywords if isinstance(kw, str) and kw.strip()]
                except json.JSONDecodeError:
                    pass

            # 备用方法：从问题和主题中提取关键词
            return self._extract_keywords_from_text(f"{question} {main_topic}")[:5]

        except Exception as e:
            self.logger.error(f"Error generating keywords for question: {e}")
            return self._extract_keywords_from_text(f"{question} {main_topic}")[:5]

    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """
        从文本中提取关键词（改进版本）

        Args:
            text: 输入文本

        Returns:
            List[str]: 关键词列表
        """
        import re

        # 移除标点符号，分割单词
        words = re.findall(r'\b\w+\b', text.lower())

        # 停用词列表（扩展版）
        stop_words = {
            'what', 'how', 'why', 'which', 'when', 'where', 'can', 'should', 'is', 'are', 'was', 'were',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
            'between', 'among', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }

        # 过滤停用词和短词
        meaningful_words = [word for word in words if len(word) > 2 and word not in stop_words]

        # 去重并返回
        return list(dict.fromkeys(meaningful_words))  # 保持顺序的去重
    
    def _extract_keywords(self, topic: str, sub_questions: List[str]) -> List[str]:
        """
        从主题和子问题中提取关键词
        
        Args:
            topic: 主要研究课题
            sub_questions: 子问题列表
            
        Returns:
            List[str]: 关键词列表
        """
        keywords = set()
        
        # 从主题中提取关键词
        topic_words = self._extract_words_from_text(topic)
        keywords.update(topic_words)
        
        # 从子问题中提取关键词
        for question in sub_questions:
            question_words = self._extract_words_from_text(question)
            keywords.update(question_words)
        
        # 过滤和清理关键词
        filtered_keywords = []
        stop_words = {'什么', '如何', '为什么', '哪些', '怎样', '是否', '能否', '可以', '应该', 
                     'what', 'how', 'why', 'which', 'when', 'where', 'can', 'should', 'is', 'are'}
        
        for keyword in keywords:
            if (len(keyword) > 2 and 
                keyword.lower() not in stop_words and 
                not keyword.isdigit()):
                filtered_keywords.append(keyword)
        
        return list(set(filtered_keywords))[:10]  # 限制关键词数量
    
    def _extract_words_from_text(self, text: str) -> List[str]:
        """
        从文本中提取有意义的词汇
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 词汇列表
        """
        # 简单的词汇提取，可以根据需要改进
        import re
        
        # 移除标点符号，分割单词
        words = re.findall(r'\b\w+\b', text)
        
        # 过滤长度
        meaningful_words = [word for word in words if len(word) > 2]
        
        return meaningful_words
    
    def _create_fallback_plan(self, topic: str) -> ResearchQuery:
        """
        创建备用研究计划（当LLM失败时使用）
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchQuery: 基本的研究查询对象
        """
        self.logger.info("Creating fallback research plan")
        
        # 生成通用的子问题
        fallback_questions = [
            f"{topic}的基本概念和定义是什么？",
            f"{topic}的历史发展过程如何？",
            f"{topic}的主要理论和方法有哪些？",
            f"{topic}的最新研究进展是什么？",
            f"{topic}面临的主要挑战和问题有哪些？",
            f"{topic}的未来发展趋势和应用前景如何？"
        ]
        
        research_query = ResearchQuery(
            main_topic=topic,
            sub_questions=fallback_questions
        )
        
        # 从主题中提取基本关键词
        keywords = self._extract_keywords(topic, fallback_questions)
        for keyword in keywords:
            research_query.add_keyword(keyword)
        
        return research_query
    
    def validate_research_plan(self, research_query: ResearchQuery) -> bool:
        """
        验证研究计划的质量
        
        Args:
            research_query: 研究查询对象
            
        Returns:
            bool: 计划是否有效
        """
        if not research_query.main_topic:
            return False
        
        if len(research_query.sub_questions) < 3:
            return False
        
        # 检查子问题的质量
        for question in research_query.sub_questions:
            if len(question) < 10 or not question.endswith('?'):
                return False
        
        return True
