# agents/synthesizer_agent.py
#
# 信息综合代理 - 负责综合多源信息和分析论文

import logging
import json
import re
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from clients import LLMClient
from models import Paper, PaperAnalysis, WebSearchResult, LLMResponse
from prompts import Prompts
from utils.enhanced_logger import EnhancedLogger


class SynthesizerAgent:
    """信息综合代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化综合代理

        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为SynthesizerAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("synthesizer")

        self.logger = logging.getLogger(__name__)
        self.enhanced_logger = EnhancedLogger("SynthesizerAgent")

        # 确保outputs目录存在
        self.outputs_dir = Path("outputs")
        self.outputs_dir.mkdir(exist_ok=True)
    
    def synthesize_web_results(self, sub_question: str, web_results: List[WebSearchResult]) -> str:
        """
        综合网络搜索结果，生成简明摘要
        
        Args:
            sub_question: 子问题
            web_results: 网络搜索结果列表
            
        Returns:
            str: 综合摘要
        """
        if not web_results:
            return f"未找到关于'{sub_question}'的相关网络信息。"
        
        self.logger.info(f"Synthesizing {len(web_results)} web results for: {sub_question}")
        
        # 构建网络信息片段
        web_snippets = []
        for i, result in enumerate(web_results, 1):
            snippet = f"[{i}] {result.title}\n{result.snippet}\n来源: {result.url}\n"
            web_snippets.append(snippet)
        
        web_snippets_text = "\n".join(web_snippets)
        
        # 使用天体物理学专用提示词生成综合摘要
        prompt = self._create_astrophysics_web_synthesis_prompt(sub_question, web_snippets_text)
        
        try:
            response = self.llm_client.generate(prompt)
            
            if response.is_successful() and response.response:
                return response.response.strip()
            else:
                self.logger.warning(f"LLM synthesis failed: {response.error}")
                return self._create_fallback_summary(sub_question, web_results)
        
        except Exception as e:
            self.logger.error(f"Error synthesizing web results: {e}")
            return self._create_fallback_summary(sub_question, web_results)

    def _create_astrophysics_web_synthesis_prompt(self, sub_question: str, web_snippets: str) -> str:
        """创建天体物理学专用的网络结果综合prompt"""
        return f"""
You are an expert astrophysicist and research synthesizer with deep knowledge of astronomical phenomena, observational techniques, theoretical models, and computational astrophysics. Your task is to analyze and synthesize web-based information specifically for astrophysical research.

**Astrophysics Research Sub-Question**: "{sub_question}"

**Web Sources**:
{web_snippets}

**Astrophysics-Specific Analysis Framework**:

1. **OBSERVATIONAL CONTEXT ASSESSMENT**:
   - Identify observational techniques, instruments, and missions mentioned
   - Evaluate data sources (ground-based telescopes, space missions, surveys)
   - Note wavelength ranges and observational signatures
   - Assess data quality and observational challenges

2. **THEORETICAL FRAMEWORK EVALUATION**:
   - Identify theoretical models and physical mechanisms
   - Evaluate mathematical formulations and approximations
   - Note connections to fundamental physics principles
   - Assess model limitations and assumptions

3. **COMPUTATIONAL ASPECTS**:
   - Identify numerical simulation methods and codes
   - Evaluate computational approaches and algorithms
   - Note computational challenges and resource requirements
   - Assess simulation validation and comparison with observations

**Output Structure**:

## Astrophysical Synthesis: {sub_question}

### Key Observational Findings
[Synthesize observational results, techniques, and data sources with emphasis on astrophysical significance]

### Theoretical Understanding
[Consolidate theoretical models, physical mechanisms, and mathematical frameworks]

### Computational Insights
[Summarize numerical methods, simulation results, and computational challenges]

### Multi-Scale Connections
[Identify connections across different astrophysical scales and phenomena]

### Current Challenges and Limitations
[Highlight observational, theoretical, and computational challenges specific to astrophysics]

### Research Context Integration
[How these findings advance understanding of: "{sub_question}"]

### Confidence Assessment
- **High Confidence**: [Well-established astrophysical results]
- **Medium Confidence**: [Emerging results with some support]
- **Requires Investigation**: [Preliminary findings needing validation]

Focus on astrophysical research aspects: observations, theory, simulations, multi-scale phenomena, and multi-wavelength approaches.
"""

    def analyze_paper(self, paper: Paper, topic: str) -> Optional[PaperAnalysis]:
        """
        分析单篇论文，提取结构化信息
        
        Args:
            paper: 论文对象
            topic: 研究课题
            
        Returns:
            Optional[PaperAnalysis]: 论文分析结果，失败时返回None
        """
        if not paper.abstract:
            self.logger.warning(f"No abstract available for paper: {paper.title}")
            return None
        
        self.logger.info(f"Analyzing paper: {paper.title}")
        
        # 使用提示词分析论文
        prompt = Prompts.PAPER_ANALYZER_PROMPT.format(
            topic=topic,
            paper_title=paper.title,
            paper_abstract=paper.abstract
        )
        
        try:
            response = self.llm_client.generate(prompt)
            
            if not response.is_successful():
                self.logger.error(f"LLM analysis failed: {response.error}")
                return None
            
            # 解析JSON响应
            analysis_data = self._parse_paper_analysis(response.response)
            
            if analysis_data:
                return PaperAnalysis(**analysis_data)
            else:
                self.logger.warning(f"Failed to parse analysis for paper: {paper.title}")
                return None
        
        except Exception as e:
            self.logger.error(f"Error analyzing paper '{paper.title}': {e}")
            return None
    
    def batch_analyze_papers_enhanced(self, papers: List[Paper], topic: str) -> List[Paper]:
        """
        增强的批量分析论文 - 每批处理多篇论文以提高效率

        Args:
            papers: 论文列表
            topic: 研究课题

        Returns:
            List[Paper]: 包含分析结果的论文列表
        """
        if not papers:
            return []

        self.logger.info(f"Enhanced batch analyzing {len(papers)} papers")

        analyzed_papers = []
        batch_size = config.PAPERS_PER_ANALYSIS_BATCH
        agent_config = config.get_agent_config("synthesizer")

        # 按批次处理论文
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            batch_number = i // batch_size + 1

            self.logger.info(f"Processing batch {batch_number}: {len(batch)} papers")

            try:
                # 批量分析这批论文
                batch_results = self._analyze_paper_batch(batch, topic, agent_config)

                # 将分析结果应用到论文对象
                for j, analysis in enumerate(batch_results):
                    if j < len(batch) and analysis:
                        batch[j].analysis = analysis
                        self.logger.debug(f"Successfully analyzed: {batch[j].title}")
                    elif j < len(batch):
                        self.logger.warning(f"Failed to analyze: {batch[j].title}")

                analyzed_papers.extend(batch)

            except Exception as e:
                self.logger.error(f"Error in batch {batch_number}: {e}")
                # 即使批次失败，也添加论文（没有分析结果）
                analyzed_papers.extend(batch)

        successful_analyses = len([p for p in analyzed_papers if p.has_analysis()])
        self.logger.info(f"Enhanced batch analysis completed: {successful_analyses}/{len(papers)} successful")

        return analyzed_papers

    def _analyze_paper_batch(self, papers: List[Paper], topic: str, agent_config: dict) -> List[Optional[PaperAnalysis]]:
        """
        分析一批论文

        Args:
            papers: 论文批次
            topic: 研究课题
            agent_config: 代理配置

        Returns:
            List[Optional[PaperAnalysis]]: 分析结果列表
        """
        if not papers:
            return []

        # 构建批量分析的输入
        papers_info = []
        for i, paper in enumerate(papers):
            if paper.abstract:
                papers_info.append(f"""
Paper {i+1}:
Title: {paper.title}
Abstract: {paper.abstract}
""")
            else:
                papers_info.append(f"""
Paper {i+1}:
Title: {paper.title}
Abstract: [No abstract available]
""")

        papers_info_text = "\n---\n".join(papers_info)

        # 使用天体物理学专用批量分析提示词
        prompt = self._create_astrophysics_batch_analysis_prompt(topic, papers_info_text, len(papers))

        try:
            response = self.llm_client.generate(
                prompt,
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE),
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS)
            )

            if not response.is_successful():
                self.logger.error(f"Batch analysis LLM request failed: {response.error}")
                return [None] * len(papers)

            # 解析批量分析结果
            batch_analyses = self._parse_batch_analysis_response(response.response, len(papers))

            return batch_analyses

        except Exception as e:
            self.logger.error(f"Error in batch analysis: {e}")
            return [None] * len(papers)

    def _parse_batch_analysis_response(self, llm_response: str, expected_count: int) -> List[Optional[PaperAnalysis]]:
        """
        解析批量分析的LLM响应

        Args:
            llm_response: LLM响应文本
            expected_count: 期望的分析结果数量

        Returns:
            List[Optional[PaperAnalysis]]: 分析结果列表
        """
        try:
            import json
            import re

            # 清理响应文本
            cleaned_response = llm_response.strip()

            # 查找JSON数组
            json_pattern = r'\[.*\]'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                try:
                    parsed_data = json.loads(json_str)

                    if isinstance(parsed_data, list):
                        analyses = []

                        for item in parsed_data:
                            if isinstance(item, dict):
                                try:
                                    # 验证必需字段
                                    required_fields = [
                                        'short_summary', 'relevance_to_topic', 'research_subject',
                                        'methodology', 'data_used', 'key_findings_or_results'
                                    ]

                                    if all(field in item for field in required_fields):
                                        # 确保key_findings_or_results是列表
                                        if not isinstance(item['key_findings_or_results'], list):
                                            findings = item['key_findings_or_results']
                                            if isinstance(findings, str):
                                                item['key_findings_or_results'] = [findings]
                                            else:
                                                item['key_findings_or_results'] = []

                                        analysis = PaperAnalysis(**item)
                                        analyses.append(analysis)
                                    else:
                                        analyses.append(None)

                                except Exception as e:
                                    self.logger.warning(f"Failed to create PaperAnalysis from item: {e}")
                                    analyses.append(None)
                            else:
                                analyses.append(None)

                        # 确保返回正确数量的结果
                        while len(analyses) < expected_count:
                            analyses.append(None)

                        return analyses[:expected_count]

                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error in batch analysis: {e}")

            # 如果解析失败，返回空结果
            return [None] * expected_count

        except Exception as e:
            self.logger.error(f"Error parsing batch analysis response: {e}")
            return [None] * expected_count

    def _create_astrophysics_batch_analysis_prompt(self, topic: str, papers_info: str, paper_count: int) -> str:
        """创建天体物理学专用的批量论文分析prompt"""
        return f"""
You are a distinguished astrophysicist and literature analyst specializing in comprehensive analysis of astronomical research. Your task is to analyze multiple PRE-RANKED astrophysics papers simultaneously and extract structured information with deep astrophysical insight.

**Main Astrophysics Research Topic**: "{topic}"
**Paper Selection**: These {paper_count} papers have been intelligently pre-ranked for relevance and importance in astrophysical research

**Pre-Ranked Astrophysics Papers**:
{papers_info}

**Astrophysics-Specific Analysis Context**:
- These papers represent high-quality, relevant astrophysical research
- Focus on deep astrophysical analysis rather than basic relevance assessment
- Consider observational, theoretical, and computational aspects
- Evaluate multi-wavelength and multi-scale connections

For each paper, extract structured information considering astrophysical research standards:

**Individual Paper Analysis Format** (JSON array):
[
    {{
        "paper_index": 1,
        "astrophysical_summary": "Concise summary emphasizing astrophysical significance (2-3 sentences)",
        "relevance_to_topic": "Specific astrophysical relevance to '{topic}' with detailed reasoning",
        "observational_aspects": "Observational techniques, instruments, data sources, wavelength ranges",
        "theoretical_framework": "Theoretical models, physical mechanisms, mathematical approaches",
        "computational_methods": "Numerical simulations, codes, computational techniques (if applicable)",
        "astrophysical_objects": "Primary astronomical objects or phenomena studied",
        "key_findings": [
            "Key astrophysical finding 1 with quantitative details",
            "Key astrophysical finding 2 with observational/theoretical context",
            "Key astrophysical finding 3 with broader implications"
        ],
        "methodological_significance": "Novel observational, theoretical, or computational contributions",
        "multi_scale_connections": "Connections to other astrophysical scales (stellar, galactic, cosmological)",
        "future_implications": "Implications for future astrophysical research and observations"
    }},
    ...
]

**Astrophysical Comparative Analysis** (add after the array):
{{
    "astrophysical_insights": {{
        "observational_trends": "Common observational approaches and emerging techniques",
        "theoretical_evolution": "Evolution of theoretical understanding in this field",
        "computational_advances": "Advances in numerical methods and simulations",
        "multi_wavelength_connections": "Multi-wavelength and multi-messenger astronomy aspects",
        "convergent_results": "Consistent findings across different astrophysical approaches",
        "contradictory_findings": "Areas where different studies present conflicting results",
        "research_gaps": "Identified gaps in current astrophysical understanding",
        "emerging_frontiers": "New frontiers and emerging research directions",
        "instrumental_requirements": "Future observational and computational requirements"
    }}
}}

Analyze all {paper_count} papers with deep astrophysical expertise, providing both individual analysis and comparative insights that advance understanding of "{topic}" in the context of modern astrophysical research.
"""

    def batch_analyze_papers(self, papers: List[Paper], topic: str) -> List[Paper]:
        """
        批量分析论文
        
        Args:
            papers: 论文列表
            topic: 研究课题
            
        Returns:
            List[Paper]: 包含分析结果的论文列表
        """
        self.logger.info(f"Batch analyzing {len(papers)} papers")
        
        analyzed_papers = []
        
        for i, paper in enumerate(papers, 1):
            self.logger.info(f"Analyzing paper {i}/{len(papers)}: {paper.title}")
            
            try:
                analysis = self.analyze_paper(paper, topic)
                if analysis:
                    paper.analysis = analysis
                    self.logger.info(f"Successfully analyzed paper: {paper.title}")
                else:
                    self.logger.warning(f"Failed to analyze paper: {paper.title}")
                
                analyzed_papers.append(paper)
            
            except Exception as e:
                self.logger.error(f"Error in batch analysis for paper '{paper.title}': {e}")
                analyzed_papers.append(paper)  # 仍然添加到列表中，即使分析失败
        
        successful_analyses = len([p for p in analyzed_papers if p.has_analysis()])
        self.logger.info(f"Batch analysis completed: {successful_analyses}/{len(papers)} successful")
        
        return analyzed_papers
    
    def _parse_paper_analysis(self, llm_response: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM返回的论文分析JSON
        
        Args:
            llm_response: LLM响应文本
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的分析数据，失败时返回None
        """
        try:
            # 尝试直接解析JSON
            # 首先清理响应文本
            cleaned_response = llm_response.strip()
            
            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(0)
                try:
                    analysis_data = json.loads(json_str)
                    
                    # 验证必需字段
                    required_fields = [
                        'short_summary', 'relevance_to_topic', 'research_subject',
                        'methodology', 'data_used', 'key_findings_or_results'
                    ]
                    
                    for field in required_fields:
                        if field not in analysis_data:
                            self.logger.warning(f"Missing required field: {field}")
                            return None
                    
                    # 确保key_findings_or_results是列表
                    if not isinstance(analysis_data['key_findings_or_results'], list):
                        findings = analysis_data['key_findings_or_results']
                        if isinstance(findings, str):
                            analysis_data['key_findings_or_results'] = [findings]
                        else:
                            analysis_data['key_findings_or_results'] = []
                    
                    return analysis_data
                
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error: {e}")
                    return None
            
            else:
                self.logger.error("No JSON object found in LLM response")
                return None
        
        except Exception as e:
            self.logger.error(f"Error parsing paper analysis: {e}")
            return None
    
    def _create_fallback_summary(self, sub_question: str, web_results: List[WebSearchResult]) -> str:
        """
        创建备用摘要（当LLM失败时使用）
        
        Args:
            sub_question: 子问题
            web_results: 网络搜索结果
            
        Returns:
            str: 备用摘要
        """
        if not web_results:
            return f"关于'{sub_question}'的信息搜索未返回结果。"
        
        # 简单地组合搜索结果
        summary_parts = [f"关于'{sub_question}'的搜索发现以下信息：\n"]
        
        for i, result in enumerate(web_results[:3], 1):  # 只使用前3个结果
            summary_parts.append(f"{i}. {result.title}")
            if result.snippet:
                summary_parts.append(f"   {result.snippet[:200]}...")
            summary_parts.append("")
        
        summary_parts.append(f"以上信息来源于{len(web_results)}个网络搜索结果。")
        
        return "\n".join(summary_parts)
    
    def create_timeline_summary(self, papers: List[Paper]) -> str:
        """
        创建基于论文发表时间的发展时间线摘要
        
        Args:
            papers: 已分析的论文列表
            
        Returns:
            str: 时间线摘要
        """
        # 过滤有分析结果和发表日期的论文
        valid_papers = [
            paper for paper in papers 
            if paper.has_analysis() and paper.publication_date
        ]
        
        if not valid_papers:
            return "无法构建时间线：缺少有效的论文数据。"
        
        # 按发表日期排序
        sorted_papers = sorted(valid_papers, key=lambda p: p.publication_date)
        
        timeline_entries = []
        for paper in sorted_papers:
            year = paper.get_year()
            citation = paper.get_short_citation()
            summary = paper.analysis.short_summary
            
            entry = f"**{year}年**: {citation} - {summary}"
            timeline_entries.append(entry)
        
        timeline_text = "\n\n".join(timeline_entries)
        
        return f"## 领域发展时间线\n\n{timeline_text}"
    
    def get_structured_paper_data(self, papers: List[Paper]) -> str:
        """
        获取结构化的论文分析数据（JSON格式）
        
        Args:
            papers: 已分析的论文列表
            
        Returns:
            str: JSON格式的结构化数据
        """
        structured_data = []
        
        for paper in papers:
            if paper.has_analysis():
                paper_data = {
                    "title": paper.title,
                    "authors": paper.authors,
                    "year": paper.get_year(),
                    "journal": paper.journal,
                    "citation": paper.get_short_citation(),
                    "analysis": paper.analysis.to_dict()
                }
                structured_data.append(paper_data)
        
        try:
            return json.dumps(structured_data, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Error creating structured data: {e}")
            return "[]"

    def generate_chinese_reading_notes(self, papers: List[Paper], research_topic: str) -> str:
        """
        生成中文精读笔记

        Args:
            papers: 论文列表
            research_topic: 研究主题

        Returns:
            str: 保存的文件路径
        """
        try:
            self.enhanced_logger.step("生成中文精读笔记")

            # 首先更新论文结构化信息（英文）
            self._update_paper_structured_info(papers)

            # 生成中文笔记内容
            notes_content = self._create_chinese_notes_content(papers, research_topic)

            # 保存到文件
            file_path = self._save_chinese_notes(notes_content, research_topic)

            self.enhanced_logger.success(f"中文精读笔记生成完成: {len(papers)} 篇论文")
            return file_path

        except Exception as e:
            self.enhanced_logger.error(f"生成中文精读笔记失败: {e}")
            return ""

    def _update_paper_structured_info(self, papers: List[Paper]):
        """
        更新论文结构化信息到final_ranked_papers.json

        Args:
            papers: 论文列表
        """
        try:
            final_papers_file = self.outputs_dir / "final_ranked_papers.json"

            if not final_papers_file.exists():
                self.enhanced_logger.warning("final_ranked_papers.json 文件不存在，跳过更新")
                return

            # 读取现有数据
            with open(final_papers_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 更新论文信息
            paper_dict = {paper.title: paper for paper in papers if paper.has_analysis()}

            for paper_data in data.get("papers", []):
                title = paper_data.get("title", "")
                if title in paper_dict:
                    paper = paper_dict[title]
                    # 添加结构化分析信息
                    paper_data["structured_analysis"] = {
                        "key_findings": paper.analysis.key_findings if paper.analysis else [],
                        "methodology": paper.analysis.methodology if paper.analysis else "",
                        "limitations": paper.analysis.limitations if paper.analysis else "",
                        "significance": paper.analysis.significance if paper.analysis else ""
                    }

            # 保存更新后的数据
            with open(final_papers_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.enhanced_logger.progress("已更新final_ranked_papers.json中的结构化信息")

        except Exception as e:
            self.enhanced_logger.warning(f"更新论文结构化信息失败: {e}")

    def _create_chinese_notes_content(self, papers: List[Paper], research_topic: str) -> str:
        """
        创建中文笔记内容

        Args:
            papers: 论文列表
            research_topic: 研究主题

        Returns:
            str: 中文笔记内容
        """
        content = f"# {research_topic} - 论文精读笔记\n\n"
        content += f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n"
        content += f"**论文总数**: {len(papers)} 篇\n\n"
        content += "---\n\n"

        for i, paper in enumerate(papers, 1):
            if not paper.has_analysis():
                continue

            content += f"## {i}. {paper.title}\n\n"

            # 基本信息
            content += f"**作者**: {', '.join(paper.authors) if paper.authors else '未知'}\n"
            content += f"**发表年份**: {paper.get_year()}\n"
            content += f"**期刊**: {paper.journal or '未知'}\n"
            if paper.doi:
                content += f"**DOI**: {paper.doi}\n"
            content += "\n"

            # 中文摘要（通过AI翻译生成）
            chinese_abstract = self._generate_chinese_abstract(paper)
            content += f"**中文摘要**:\n{chinese_abstract}\n\n"

            # 关键发现
            if paper.analysis and paper.analysis.key_findings:
                content += "**关键发现**:\n"
                for finding in paper.analysis.key_findings:
                    chinese_finding = self._translate_to_chinese(finding)
                    content += f"- {chinese_finding}\n"
                content += "\n"

            # 研究方法
            if paper.analysis and paper.analysis.methodology:
                chinese_methodology = self._translate_to_chinese(paper.analysis.methodology)
                content += f"**研究方法**: {chinese_methodology}\n\n"

            # 局限性
            if paper.analysis and paper.analysis.limitations:
                chinese_limitations = self._translate_to_chinese(paper.analysis.limitations)
                content += f"**局限性**: {chinese_limitations}\n\n"

            content += "---\n\n"

        return content

    def _generate_chinese_abstract(self, paper: Paper) -> str:
        """
        生成中文摘要

        Args:
            paper: 论文对象

        Returns:
            str: 中文摘要
        """
        try:
            if not paper.abstract:
                return "摘要信息不可用"

            prompt = f"""请将以下英文学术论文摘要翻译成中文，保持学术性和准确性：

原文摘要：
{paper.abstract}

要求：
1. 翻译准确，保持学术术语的专业性
2. 语言流畅，符合中文学术写作习惯
3. 保持原文的逻辑结构和重点信息
4. 长度控制在200字以内

中文摘要："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return "摘要翻译失败"

        except Exception as e:
            self.logger.warning(f"生成中文摘要失败: {e}")
            return "摘要生成出错"

    def _translate_to_chinese(self, text: str) -> str:
        """
        将英文文本翻译成中文

        Args:
            text: 英文文本

        Returns:
            str: 中文文本
        """
        try:
            if not text or len(text.strip()) == 0:
                return "信息不可用"

            prompt = f"""请将以下英文学术内容翻译成中文，保持学术性和准确性：

英文原文：
{text}

要求：
1. 翻译准确，保持专业术语的准确性
2. 语言简洁明了，符合中文表达习惯
3. 保持原文的核心信息

中文翻译："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return text  # 翻译失败时返回原文

        except Exception as e:
            self.logger.warning(f"翻译失败: {e}")
            return text

    def _save_chinese_notes(self, content: str, research_topic: str) -> str:
        """
        保存中文笔记到文件

        Args:
            content: 笔记内容
            research_topic: 研究主题

        Returns:
            str: 保存的文件路径
        """
        try:
            filename = "paper_analysis_notes.md"
            file_path = self.outputs_dir / filename

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            self.enhanced_logger.file_info("保存中文精读笔记", str(file_path))
            return str(file_path)

        except Exception as e:
            self.enhanced_logger.error(f"保存中文笔记失败: {e}")
            return ""
