# agents/synthesizer_agent.py
#
# 信息综合代理 - 负责综合多源信息和分析论文

import logging
import json
import re
from typing import List, Dict, Any, Optional

from clients import LLMClient
from models import Paper, PaperAnalysis, WebSearchResult, LLMResponse
from prompts import Prompts


class SynthesizerAgent:
    """信息综合代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化综合代理

        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为SynthesizerAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("synthesizer")
        self.logger = logging.getLogger(__name__)
    
    def synthesize_web_results(self, sub_question: str, web_results: List[WebSearchResult]) -> str:
        """
        综合网络搜索结果，生成简明摘要
        
        Args:
            sub_question: 子问题
            web_results: 网络搜索结果列表
            
        Returns:
            str: 综合摘要
        """
        if not web_results:
            return f"未找到关于'{sub_question}'的相关网络信息。"
        
        self.logger.info(f"Synthesizing {len(web_results)} web results for: {sub_question}")
        
        # 构建网络信息片段
        web_snippets = []
        for i, result in enumerate(web_results, 1):
            snippet = f"[{i}] {result.title}\n{result.snippet}\n来源: {result.url}\n"
            web_snippets.append(snippet)
        
        web_snippets_text = "\n".join(web_snippets)
        
        # 使用天体物理学专用提示词生成综合摘要
        prompt = self._create_astrophysics_web_synthesis_prompt(sub_question, web_snippets_text)
        
        try:
            response = self.llm_client.generate(prompt)
            
            if response.is_successful() and response.response:
                return response.response.strip()
            else:
                self.logger.warning(f"LLM synthesis failed: {response.error}")
                return self._create_fallback_summary(sub_question, web_results)
        
        except Exception as e:
            self.logger.error(f"Error synthesizing web results: {e}")
            return self._create_fallback_summary(sub_question, web_results)

    def _create_astrophysics_web_synthesis_prompt(self, sub_question: str, web_snippets: str) -> str:
        """创建天体物理学专用的网络结果综合prompt"""
        return f"""
You are an expert astrophysicist and research synthesizer with deep knowledge of astronomical phenomena, observational techniques, theoretical models, and computational astrophysics. Your task is to analyze and synthesize web-based information specifically for astrophysical research.

**Astrophysics Research Sub-Question**: "{sub_question}"

**Web Sources**:
{web_snippets}

**Astrophysics-Specific Analysis Framework**:

1. **OBSERVATIONAL CONTEXT ASSESSMENT**:
   - Identify observational techniques, instruments, and missions mentioned
   - Evaluate data sources (ground-based telescopes, space missions, surveys)
   - Note wavelength ranges and observational signatures
   - Assess data quality and observational challenges

2. **THEORETICAL FRAMEWORK EVALUATION**:
   - Identify theoretical models and physical mechanisms
   - Evaluate mathematical formulations and approximations
   - Note connections to fundamental physics principles
   - Assess model limitations and assumptions

3. **COMPUTATIONAL ASPECTS**:
   - Identify numerical simulation methods and codes
   - Evaluate computational approaches and algorithms
   - Note computational challenges and resource requirements
   - Assess simulation validation and comparison with observations

**Output Structure**:

## Astrophysical Synthesis: {sub_question}

### Key Observational Findings
[Synthesize observational results, techniques, and data sources with emphasis on astrophysical significance]

### Theoretical Understanding
[Consolidate theoretical models, physical mechanisms, and mathematical frameworks]

### Computational Insights
[Summarize numerical methods, simulation results, and computational challenges]

### Multi-Scale Connections
[Identify connections across different astrophysical scales and phenomena]

### Current Challenges and Limitations
[Highlight observational, theoretical, and computational challenges specific to astrophysics]

### Research Context Integration
[How these findings advance understanding of: "{sub_question}"]

### Confidence Assessment
- **High Confidence**: [Well-established astrophysical results]
- **Medium Confidence**: [Emerging results with some support]
- **Requires Investigation**: [Preliminary findings needing validation]

Focus on astrophysical research aspects: observations, theory, simulations, multi-scale phenomena, and multi-wavelength approaches.
"""

    def analyze_paper(self, paper: Paper, topic: str) -> Optional[PaperAnalysis]:
        """
        分析单篇论文，提取结构化信息
        
        Args:
            paper: 论文对象
            topic: 研究课题
            
        Returns:
            Optional[PaperAnalysis]: 论文分析结果，失败时返回None
        """
        if not paper.abstract:
            self.logger.warning(f"No abstract available for paper: {paper.title}")
            return None
        
        self.logger.info(f"Analyzing paper: {paper.title}")
        
        # 使用提示词分析论文
        prompt = Prompts.PAPER_ANALYZER_PROMPT.format(
            topic=topic,
            paper_title=paper.title,
            paper_abstract=paper.abstract
        )
        
        try:
            response = self.llm_client.generate(prompt)
            
            if not response.is_successful():
                self.logger.error(f"LLM analysis failed: {response.error}")
                return None
            
            # 解析JSON响应
            analysis_data = self._parse_paper_analysis(response.response)
            
            if analysis_data:
                return PaperAnalysis(**analysis_data)
            else:
                self.logger.warning(f"Failed to parse analysis for paper: {paper.title}")
                return None
        
        except Exception as e:
            self.logger.error(f"Error analyzing paper '{paper.title}': {e}")
            return None
    
    def batch_analyze_papers_enhanced(self, papers: List[Paper], topic: str) -> List[Paper]:
        """
        增强的批量分析论文 - 每批处理多篇论文以提高效率

        Args:
            papers: 论文列表
            topic: 研究课题

        Returns:
            List[Paper]: 包含分析结果的论文列表
        """
        if not papers:
            return []

        self.logger.info(f"Enhanced batch analyzing {len(papers)} papers")

        analyzed_papers = []
        batch_size = config.PAPERS_PER_ANALYSIS_BATCH
        agent_config = config.get_agent_config("synthesizer")

        # 按批次处理论文
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            batch_number = i // batch_size + 1

            self.logger.info(f"Processing batch {batch_number}: {len(batch)} papers")

            try:
                # 批量分析这批论文
                batch_results = self._analyze_paper_batch(batch, topic, agent_config)

                # 将分析结果应用到论文对象
                for j, analysis in enumerate(batch_results):
                    if j < len(batch) and analysis:
                        batch[j].analysis = analysis
                        self.logger.debug(f"Successfully analyzed: {batch[j].title}")
                    elif j < len(batch):
                        self.logger.warning(f"Failed to analyze: {batch[j].title}")

                analyzed_papers.extend(batch)

            except Exception as e:
                self.logger.error(f"Error in batch {batch_number}: {e}")
                # 即使批次失败，也添加论文（没有分析结果）
                analyzed_papers.extend(batch)

        successful_analyses = len([p for p in analyzed_papers if p.has_analysis()])
        self.logger.info(f"Enhanced batch analysis completed: {successful_analyses}/{len(papers)} successful")

        return analyzed_papers

    def _analyze_paper_batch(self, papers: List[Paper], topic: str, agent_config: dict) -> List[Optional[PaperAnalysis]]:
        """
        分析一批论文

        Args:
            papers: 论文批次
            topic: 研究课题
            agent_config: 代理配置

        Returns:
            List[Optional[PaperAnalysis]]: 分析结果列表
        """
        if not papers:
            return []

        # 构建批量分析的输入
        papers_info = []
        for i, paper in enumerate(papers):
            if paper.abstract:
                papers_info.append(f"""
Paper {i+1}:
Title: {paper.title}
Abstract: {paper.abstract}
""")
            else:
                papers_info.append(f"""
Paper {i+1}:
Title: {paper.title}
Abstract: [No abstract available]
""")

        papers_info_text = "\n---\n".join(papers_info)

        # 使用天体物理学专用批量分析提示词
        prompt = self._create_astrophysics_batch_analysis_prompt(topic, papers_info_text, len(papers))

        try:
            response = self.llm_client.generate(
                prompt,
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE),
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS)
            )

            if not response.is_successful():
                self.logger.error(f"Batch analysis LLM request failed: {response.error}")
                return [None] * len(papers)

            # 解析批量分析结果
            batch_analyses = self._parse_batch_analysis_response(response.response, len(papers))

            return batch_analyses

        except Exception as e:
            self.logger.error(f"Error in batch analysis: {e}")
            return [None] * len(papers)

    def _parse_batch_analysis_response(self, llm_response: str, expected_count: int) -> List[Optional[PaperAnalysis]]:
        """
        解析批量分析的LLM响应

        Args:
            llm_response: LLM响应文本
            expected_count: 期望的分析结果数量

        Returns:
            List[Optional[PaperAnalysis]]: 分析结果列表
        """
        try:
            import json
            import re

            # 清理响应文本
            cleaned_response = llm_response.strip()

            # 查找JSON数组
            json_pattern = r'\[.*\]'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                try:
                    parsed_data = json.loads(json_str)

                    if isinstance(parsed_data, list):
                        analyses = []

                        for item in parsed_data:
                            if isinstance(item, dict):
                                try:
                                    # 验证必需字段
                                    required_fields = [
                                        'short_summary', 'relevance_to_topic', 'research_subject',
                                        'methodology', 'data_used', 'key_findings_or_results'
                                    ]

                                    if all(field in item for field in required_fields):
                                        # 确保key_findings_or_results是列表
                                        if not isinstance(item['key_findings_or_results'], list):
                                            findings = item['key_findings_or_results']
                                            if isinstance(findings, str):
                                                item['key_findings_or_results'] = [findings]
                                            else:
                                                item['key_findings_or_results'] = []

                                        analysis = PaperAnalysis(**item)
                                        analyses.append(analysis)
                                    else:
                                        analyses.append(None)

                                except Exception as e:
                                    self.logger.warning(f"Failed to create PaperAnalysis from item: {e}")
                                    analyses.append(None)
                            else:
                                analyses.append(None)

                        # 确保返回正确数量的结果
                        while len(analyses) < expected_count:
                            analyses.append(None)

                        return analyses[:expected_count]

                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error in batch analysis: {e}")

            # 如果解析失败，返回空结果
            return [None] * expected_count

        except Exception as e:
            self.logger.error(f"Error parsing batch analysis response: {e}")
            return [None] * expected_count

    def _create_astrophysics_batch_analysis_prompt(self, topic: str, papers_info: str, paper_count: int) -> str:
        """创建天体物理学专用的批量论文分析prompt"""
        return f"""
You are a distinguished astrophysicist and literature analyst specializing in comprehensive analysis of astronomical research. Your task is to analyze multiple PRE-RANKED astrophysics papers simultaneously and extract structured information with deep astrophysical insight.

**Main Astrophysics Research Topic**: "{topic}"
**Paper Selection**: These {paper_count} papers have been intelligently pre-ranked for relevance and importance in astrophysical research

**Pre-Ranked Astrophysics Papers**:
{papers_info}

**Astrophysics-Specific Analysis Context**:
- These papers represent high-quality, relevant astrophysical research
- Focus on deep astrophysical analysis rather than basic relevance assessment
- Consider observational, theoretical, and computational aspects
- Evaluate multi-wavelength and multi-scale connections

For each paper, extract structured information considering astrophysical research standards:

**Individual Paper Analysis Format** (JSON array):
[
    {{
        "paper_index": 1,
        "astrophysical_summary": "Concise summary emphasizing astrophysical significance (2-3 sentences)",
        "relevance_to_topic": "Specific astrophysical relevance to '{topic}' with detailed reasoning",
        "observational_aspects": "Observational techniques, instruments, data sources, wavelength ranges",
        "theoretical_framework": "Theoretical models, physical mechanisms, mathematical approaches",
        "computational_methods": "Numerical simulations, codes, computational techniques (if applicable)",
        "astrophysical_objects": "Primary astronomical objects or phenomena studied",
        "key_findings": [
            "Key astrophysical finding 1 with quantitative details",
            "Key astrophysical finding 2 with observational/theoretical context",
            "Key astrophysical finding 3 with broader implications"
        ],
        "methodological_significance": "Novel observational, theoretical, or computational contributions",
        "multi_scale_connections": "Connections to other astrophysical scales (stellar, galactic, cosmological)",
        "future_implications": "Implications for future astrophysical research and observations"
    }},
    ...
]

**Astrophysical Comparative Analysis** (add after the array):
{{
    "astrophysical_insights": {{
        "observational_trends": "Common observational approaches and emerging techniques",
        "theoretical_evolution": "Evolution of theoretical understanding in this field",
        "computational_advances": "Advances in numerical methods and simulations",
        "multi_wavelength_connections": "Multi-wavelength and multi-messenger astronomy aspects",
        "convergent_results": "Consistent findings across different astrophysical approaches",
        "contradictory_findings": "Areas where different studies present conflicting results",
        "research_gaps": "Identified gaps in current astrophysical understanding",
        "emerging_frontiers": "New frontiers and emerging research directions",
        "instrumental_requirements": "Future observational and computational requirements"
    }}
}}

Analyze all {paper_count} papers with deep astrophysical expertise, providing both individual analysis and comparative insights that advance understanding of "{topic}" in the context of modern astrophysical research.
"""

    def batch_analyze_papers(self, papers: List[Paper], topic: str) -> List[Paper]:
        """
        批量分析论文
        
        Args:
            papers: 论文列表
            topic: 研究课题
            
        Returns:
            List[Paper]: 包含分析结果的论文列表
        """
        self.logger.info(f"Batch analyzing {len(papers)} papers")
        
        analyzed_papers = []
        
        for i, paper in enumerate(papers, 1):
            self.logger.info(f"Analyzing paper {i}/{len(papers)}: {paper.title}")
            
            try:
                analysis = self.analyze_paper(paper, topic)
                if analysis:
                    paper.analysis = analysis
                    self.logger.info(f"Successfully analyzed paper: {paper.title}")
                else:
                    self.logger.warning(f"Failed to analyze paper: {paper.title}")
                
                analyzed_papers.append(paper)
            
            except Exception as e:
                self.logger.error(f"Error in batch analysis for paper '{paper.title}': {e}")
                analyzed_papers.append(paper)  # 仍然添加到列表中，即使分析失败
        
        successful_analyses = len([p for p in analyzed_papers if p.has_analysis()])
        self.logger.info(f"Batch analysis completed: {successful_analyses}/{len(papers)} successful")
        
        return analyzed_papers
    
    def _parse_paper_analysis(self, llm_response: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM返回的论文分析JSON
        
        Args:
            llm_response: LLM响应文本
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的分析数据，失败时返回None
        """
        try:
            # 尝试直接解析JSON
            # 首先清理响应文本
            cleaned_response = llm_response.strip()
            
            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(0)
                try:
                    analysis_data = json.loads(json_str)
                    
                    # 验证必需字段
                    required_fields = [
                        'short_summary', 'relevance_to_topic', 'research_subject',
                        'methodology', 'data_used', 'key_findings_or_results'
                    ]
                    
                    for field in required_fields:
                        if field not in analysis_data:
                            self.logger.warning(f"Missing required field: {field}")
                            return None
                    
                    # 确保key_findings_or_results是列表
                    if not isinstance(analysis_data['key_findings_or_results'], list):
                        findings = analysis_data['key_findings_or_results']
                        if isinstance(findings, str):
                            analysis_data['key_findings_or_results'] = [findings]
                        else:
                            analysis_data['key_findings_or_results'] = []
                    
                    return analysis_data
                
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error: {e}")
                    return None
            
            else:
                self.logger.error("No JSON object found in LLM response")
                return None
        
        except Exception as e:
            self.logger.error(f"Error parsing paper analysis: {e}")
            return None
    
    def _create_fallback_summary(self, sub_question: str, web_results: List[WebSearchResult]) -> str:
        """
        创建备用摘要（当LLM失败时使用）
        
        Args:
            sub_question: 子问题
            web_results: 网络搜索结果
            
        Returns:
            str: 备用摘要
        """
        if not web_results:
            return f"关于'{sub_question}'的信息搜索未返回结果。"
        
        # 简单地组合搜索结果
        summary_parts = [f"关于'{sub_question}'的搜索发现以下信息：\n"]
        
        for i, result in enumerate(web_results[:3], 1):  # 只使用前3个结果
            summary_parts.append(f"{i}. {result.title}")
            if result.snippet:
                summary_parts.append(f"   {result.snippet[:200]}...")
            summary_parts.append("")
        
        summary_parts.append(f"以上信息来源于{len(web_results)}个网络搜索结果。")
        
        return "\n".join(summary_parts)
    
    def create_timeline_summary(self, papers: List[Paper]) -> str:
        """
        创建基于论文发表时间的发展时间线摘要
        
        Args:
            papers: 已分析的论文列表
            
        Returns:
            str: 时间线摘要
        """
        # 过滤有分析结果和发表日期的论文
        valid_papers = [
            paper for paper in papers 
            if paper.has_analysis() and paper.publication_date
        ]
        
        if not valid_papers:
            return "无法构建时间线：缺少有效的论文数据。"
        
        # 按发表日期排序
        sorted_papers = sorted(valid_papers, key=lambda p: p.publication_date)
        
        timeline_entries = []
        for paper in sorted_papers:
            year = paper.get_year()
            citation = paper.get_short_citation()
            summary = paper.analysis.short_summary
            
            entry = f"**{year}年**: {citation} - {summary}"
            timeline_entries.append(entry)
        
        timeline_text = "\n\n".join(timeline_entries)
        
        return f"## 领域发展时间线\n\n{timeline_text}"
    
    def get_structured_paper_data(self, papers: List[Paper]) -> str:
        """
        获取结构化的论文分析数据（JSON格式）
        
        Args:
            papers: 已分析的论文列表
            
        Returns:
            str: JSON格式的结构化数据
        """
        structured_data = []
        
        for paper in papers:
            if paper.has_analysis():
                paper_data = {
                    "title": paper.title,
                    "authors": paper.authors,
                    "year": paper.get_year(),
                    "journal": paper.journal,
                    "citation": paper.get_short_citation(),
                    "analysis": paper.analysis.to_dict()
                }
                structured_data.append(paper_data)
        
        try:
            return json.dumps(structured_data, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Error creating structured data: {e}")
            return "[]"
