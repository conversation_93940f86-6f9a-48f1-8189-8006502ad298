# agents/synthesizer_agent.py
#
# 信息综合代理 - 负责综合多源信息和分析论文

import logging
import json
import re
from typing import List, Dict, Any, Optional

from clients import LLMClient
from models import Paper, PaperAnalysis, WebSearchResult, LLMResponse
from prompts import Prompts


class SynthesizerAgent:
    """信息综合代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化综合代理
        
        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        self.llm_client = llm_client or LLMClient()
        self.logger = logging.getLogger(__name__)
    
    def synthesize_web_results(self, sub_question: str, web_results: List[WebSearchResult]) -> str:
        """
        综合网络搜索结果，生成简明摘要
        
        Args:
            sub_question: 子问题
            web_results: 网络搜索结果列表
            
        Returns:
            str: 综合摘要
        """
        if not web_results:
            return f"未找到关于'{sub_question}'的相关网络信息。"
        
        self.logger.info(f"Synthesizing {len(web_results)} web results for: {sub_question}")
        
        # 构建网络信息片段
        web_snippets = []
        for i, result in enumerate(web_results, 1):
            snippet = f"[{i}] {result.title}\n{result.snippet}\n来源: {result.url}\n"
            web_snippets.append(snippet)
        
        web_snippets_text = "\n".join(web_snippets)
        
        # 使用提示词生成综合摘要
        prompt = Prompts.WEB_SYNTHESIZER_PROMPT.format(
            sub_question=sub_question,
            web_snippets=web_snippets_text
        )
        
        try:
            response = self.llm_client.generate(prompt)
            
            if response.is_successful() and response.response:
                return response.response.strip()
            else:
                self.logger.warning(f"LLM synthesis failed: {response.error}")
                return self._create_fallback_summary(sub_question, web_results)
        
        except Exception as e:
            self.logger.error(f"Error synthesizing web results: {e}")
            return self._create_fallback_summary(sub_question, web_results)
    
    def analyze_paper(self, paper: Paper, topic: str) -> Optional[PaperAnalysis]:
        """
        分析单篇论文，提取结构化信息
        
        Args:
            paper: 论文对象
            topic: 研究课题
            
        Returns:
            Optional[PaperAnalysis]: 论文分析结果，失败时返回None
        """
        if not paper.abstract:
            self.logger.warning(f"No abstract available for paper: {paper.title}")
            return None
        
        self.logger.info(f"Analyzing paper: {paper.title}")
        
        # 使用提示词分析论文
        prompt = Prompts.PAPER_ANALYZER_PROMPT.format(
            topic=topic,
            paper_title=paper.title,
            paper_abstract=paper.abstract
        )
        
        try:
            response = self.llm_client.generate(prompt)
            
            if not response.is_successful():
                self.logger.error(f"LLM analysis failed: {response.error}")
                return None
            
            # 解析JSON响应
            analysis_data = self._parse_paper_analysis(response.response)
            
            if analysis_data:
                return PaperAnalysis(**analysis_data)
            else:
                self.logger.warning(f"Failed to parse analysis for paper: {paper.title}")
                return None
        
        except Exception as e:
            self.logger.error(f"Error analyzing paper '{paper.title}': {e}")
            return None
    
    def batch_analyze_papers(self, papers: List[Paper], topic: str) -> List[Paper]:
        """
        批量分析论文
        
        Args:
            papers: 论文列表
            topic: 研究课题
            
        Returns:
            List[Paper]: 包含分析结果的论文列表
        """
        self.logger.info(f"Batch analyzing {len(papers)} papers")
        
        analyzed_papers = []
        
        for i, paper in enumerate(papers, 1):
            self.logger.info(f"Analyzing paper {i}/{len(papers)}: {paper.title}")
            
            try:
                analysis = self.analyze_paper(paper, topic)
                if analysis:
                    paper.analysis = analysis
                    self.logger.info(f"Successfully analyzed paper: {paper.title}")
                else:
                    self.logger.warning(f"Failed to analyze paper: {paper.title}")
                
                analyzed_papers.append(paper)
            
            except Exception as e:
                self.logger.error(f"Error in batch analysis for paper '{paper.title}': {e}")
                analyzed_papers.append(paper)  # 仍然添加到列表中，即使分析失败
        
        successful_analyses = len([p for p in analyzed_papers if p.has_analysis()])
        self.logger.info(f"Batch analysis completed: {successful_analyses}/{len(papers)} successful")
        
        return analyzed_papers
    
    def _parse_paper_analysis(self, llm_response: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM返回的论文分析JSON
        
        Args:
            llm_response: LLM响应文本
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的分析数据，失败时返回None
        """
        try:
            # 尝试直接解析JSON
            # 首先清理响应文本
            cleaned_response = llm_response.strip()
            
            # 查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(0)
                try:
                    analysis_data = json.loads(json_str)
                    
                    # 验证必需字段
                    required_fields = [
                        'short_summary', 'relevance_to_topic', 'research_subject',
                        'methodology', 'data_used', 'key_findings_or_results'
                    ]
                    
                    for field in required_fields:
                        if field not in analysis_data:
                            self.logger.warning(f"Missing required field: {field}")
                            return None
                    
                    # 确保key_findings_or_results是列表
                    if not isinstance(analysis_data['key_findings_or_results'], list):
                        findings = analysis_data['key_findings_or_results']
                        if isinstance(findings, str):
                            analysis_data['key_findings_or_results'] = [findings]
                        else:
                            analysis_data['key_findings_or_results'] = []
                    
                    return analysis_data
                
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error: {e}")
                    return None
            
            else:
                self.logger.error("No JSON object found in LLM response")
                return None
        
        except Exception as e:
            self.logger.error(f"Error parsing paper analysis: {e}")
            return None
    
    def _create_fallback_summary(self, sub_question: str, web_results: List[WebSearchResult]) -> str:
        """
        创建备用摘要（当LLM失败时使用）
        
        Args:
            sub_question: 子问题
            web_results: 网络搜索结果
            
        Returns:
            str: 备用摘要
        """
        if not web_results:
            return f"关于'{sub_question}'的信息搜索未返回结果。"
        
        # 简单地组合搜索结果
        summary_parts = [f"关于'{sub_question}'的搜索发现以下信息：\n"]
        
        for i, result in enumerate(web_results[:3], 1):  # 只使用前3个结果
            summary_parts.append(f"{i}. {result.title}")
            if result.snippet:
                summary_parts.append(f"   {result.snippet[:200]}...")
            summary_parts.append("")
        
        summary_parts.append(f"以上信息来源于{len(web_results)}个网络搜索结果。")
        
        return "\n".join(summary_parts)
    
    def create_timeline_summary(self, papers: List[Paper]) -> str:
        """
        创建基于论文发表时间的发展时间线摘要
        
        Args:
            papers: 已分析的论文列表
            
        Returns:
            str: 时间线摘要
        """
        # 过滤有分析结果和发表日期的论文
        valid_papers = [
            paper for paper in papers 
            if paper.has_analysis() and paper.publication_date
        ]
        
        if not valid_papers:
            return "无法构建时间线：缺少有效的论文数据。"
        
        # 按发表日期排序
        sorted_papers = sorted(valid_papers, key=lambda p: p.publication_date)
        
        timeline_entries = []
        for paper in sorted_papers:
            year = paper.get_year()
            citation = paper.get_short_citation()
            summary = paper.analysis.short_summary
            
            entry = f"**{year}年**: {citation} - {summary}"
            timeline_entries.append(entry)
        
        timeline_text = "\n\n".join(timeline_entries)
        
        return f"## 领域发展时间线\n\n{timeline_text}"
    
    def get_structured_paper_data(self, papers: List[Paper]) -> str:
        """
        获取结构化的论文分析数据（JSON格式）
        
        Args:
            papers: 已分析的论文列表
            
        Returns:
            str: JSON格式的结构化数据
        """
        structured_data = []
        
        for paper in papers:
            if paper.has_analysis():
                paper_data = {
                    "title": paper.title,
                    "authors": paper.authors,
                    "year": paper.get_year(),
                    "journal": paper.journal,
                    "citation": paper.get_short_citation(),
                    "analysis": paper.analysis.to_dict()
                }
                structured_data.append(paper_data)
        
        try:
            return json.dumps(structured_data, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Error creating structured data: {e}")
            return "[]"
