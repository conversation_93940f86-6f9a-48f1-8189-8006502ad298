# agents/writer_agent.py
#
# 报告撰写代理 - 负责生成最终的研究报告

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from clients import LLMClient
from models import Paper, LLMResponse
from prompts import Prompts
from config import config
from utils import CitationManager
from utils.enhanced_logger import EnhancedLogger


class WriterAgent:
    """报告撰写代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化撰写代理

        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为WriterAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("writer")

        self.logger = logging.getLogger(__name__)
        self.enhanced_logger = EnhancedLogger("WriterAgent")

        # 初始化引用管理器
        if config.ENABLE_CITATIONS:
            self.citation_manager = CitationManager(config.CITATION_STYLE)
        else:
            self.citation_manager = None

        # 确保outputs目录存在
        self.outputs_dir = Path("outputs")
        self.outputs_dir.mkdir(exist_ok=True)
    
    def write_research_report(self, 
                            topic: str,
                            web_summaries: str,
                            structured_paper_analyses: str,
                            timeline: str) -> str:
        """
        撰写深度研究报告
        
        Args:
            topic: 研究课题
            web_summaries: 网络搜索摘要
            structured_paper_analyses: 结构化论文分析
            timeline: 时间线信息
            
        Returns:
            str: 研究报告内容
        """
        self.logger.info(f"Writing research report for topic: {topic}")
        
        # 使用天体物理学专用报告撰写提示词
        prompt = self._create_astrophysics_research_report_prompt(
            topic, web_summaries, structured_paper_analyses, timeline
        )
        
        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE)
            )
            
            if response.is_successful() and response.response:
                report = response.response.strip()
                self.logger.info("Research report generated successfully")
                return report
            else:
                self.logger.error(f"Failed to generate research report: {response.error}")
                return self._create_fallback_report(topic, web_summaries, timeline)

        except Exception as e:
            self.logger.error(f"Error writing research report: {e}")
            return self._create_fallback_report(topic, web_summaries, timeline)

    def _create_astrophysics_research_report_prompt(self, topic: str, web_summaries: str,
                                                  structured_paper_analyses: str, timeline: str) -> str:
        """创建天体物理学专用的研究报告prompt"""
        return f"""
You are a distinguished astrophysicist and science writer with expertise in astronomical phenomena, observational techniques, theoretical models, and computational astrophysics. Your task is to write a comprehensive research report that reflects the unique aspects and methodologies of astrophysical research.

**Astrophysics Research Topic**: "{topic}"

**Available Research Materials**:

**Web Research Summary**:
{web_summaries}

**Structured Analysis of Pre-Ranked Astrophysics Papers**:
{structured_paper_analyses}

**Historical Timeline**:
{timeline}

**Astrophysics-Specific Writing Guidelines**:

1. **OBSERVATIONAL FOUNDATION**:
   - Emphasize observational evidence and data sources
   - Discuss telescopes, instruments, and observational techniques
   - Include wavelength ranges and observational signatures
   - Address observational challenges and limitations

2. **THEORETICAL FRAMEWORK**:
   - Present theoretical models and physical mechanisms
   - Explain mathematical formulations where appropriate
   - Connect to fundamental physics principles
   - Discuss model assumptions and limitations

3. **COMPUTATIONAL ASPECTS**:
   - Include numerical simulations and computational methods
   - Discuss computational challenges and advances
   - Compare simulation results with observations
   - Address computational limitations and future needs

4. **MULTI-SCALE PERSPECTIVE**:
   - Connect phenomena across different astrophysical scales
   - Discuss stellar, galactic, and cosmological contexts
   - Address evolutionary and temporal aspects
   - Include multi-wavelength and multi-messenger connections

**Report Structure for Astrophysics Research**:

# {topic}: Comprehensive Astrophysical Analysis

## Executive Summary
[200-word summary highlighting key observational findings, theoretical insights, and computational advances]

## Introduction and Astrophysical Context
[Establish the astrophysical significance, observational motivation, and theoretical importance]

## Observational Landscape
[Current observational capabilities, key surveys and missions, data sources and quality]

## Theoretical Understanding
[Physical mechanisms, mathematical models, theoretical predictions and their observational tests]

## Computational Approaches
[Numerical simulations, computational methods, comparison with observations]

## Multi-Wavelength and Multi-Messenger Perspectives
[Connections across electromagnetic spectrum and other messengers (gravitational waves, neutrinos)]

## Current State of Knowledge
[Synthesis of observational, theoretical, and computational results]

## Outstanding Questions and Challenges
[Unresolved issues, observational limitations, theoretical gaps, computational challenges]

## Future Prospects
[Next-generation instruments, theoretical developments, computational advances]

## Implications for Broader Astrophysics
[Connections to other astrophysical phenomena and fields]

## Conclusions
[Key insights and their significance for astrophysical understanding]

**Astrophysics Writing Standards**:
- Use precise astronomical terminology and nomenclature
- Include quantitative results with appropriate units
- Discuss uncertainties and error bars where relevant
- Reference specific missions, instruments, and surveys
- Maintain balance between observational, theoretical, and computational aspects
- Consider the hierarchical nature of astrophysical phenomena
- Address the interplay between different physical processes
- Include discussion of selection effects and observational biases

Generate a comprehensive report that reflects the interdisciplinary nature of modern astrophysical research, emphasizing the crucial interplay between observations, theory, and simulations.
"""

    def write_research_report_with_citations(self,
                                           topic: str,
                                           papers: List[Paper],
                                           web_summaries: str = "",
                                           timeline: str = "") -> str:
        """
        撰写带引用的深度研究报告

        Args:
            topic: 研究课题
            papers: 已分析的论文列表
            web_summaries: 网络搜索摘要
            timeline: 时间线信息

        Returns:
            str: 带引用的研究报告内容
        """
        self.logger.info(f"Writing research report with citations for topic: {topic}")

        if not config.ENABLE_CITATIONS or not self.citation_manager:
            # 如果未启用引用功能，使用原有方法
            structured_analyses = self._format_paper_analyses_for_report(papers)
            return self.write_research_report(topic, web_summaries, structured_analyses, timeline)

        # 重置引用管理器
        self.citation_manager.reset()

        # 准备论文分析数据，同时收集引用信息
        structured_analyses = self._format_paper_analyses_with_citations(papers)

        # 生成带引用要求的提示词
        prompt = self._create_citation_aware_prompt(topic, structured_analyses, web_summaries, timeline)

        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE)
            )

            if response.is_successful() and response.response:
                report_content = response.response.strip()

                # 后处理：添加引用标记和参考文献列表
                final_report = self._post_process_citations(report_content, papers)

                self.logger.info(f"Research report with citations generated successfully. "
                               f"Total citations: {len(self.citation_manager.cited_papers)}")
                return final_report
            else:
                self.logger.error(f"Failed to generate research report: {response.error}")
                return self._create_fallback_report_with_citations(topic, papers, web_summaries, timeline)

        except Exception as e:
            self.logger.error(f"Error generating research report with citations: {e}")
            return self._create_fallback_report_with_citations(topic, papers, web_summaries, timeline)
    
    def write_innovation_proposal(self, research_report: str) -> str:
        """
        基于研究报告撰写创新方案
        
        Args:
            research_report: 完整的研究报告
            
        Returns:
            str: 创新方案内容
        """
        self.logger.info("Writing innovation proposal")
        
        prompt = Prompts.INNOVATION_PROMPT.format(research_report=research_report)
        
        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=min(agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE) + 0.1, 1.0)  # 稍高的温度以增加创新性
            )
            
            if response.is_successful() and response.response:
                proposal = response.response.strip()
                self.logger.info("Innovation proposal generated successfully")
                return proposal
            else:
                self.logger.error(f"Failed to generate innovation proposal: {response.error}")
                return self._create_fallback_proposal()
        
        except Exception as e:
            self.logger.error(f"Error writing innovation proposal: {e}")
            return self._create_fallback_proposal()
    
    def write_feasibility_analysis(self, research_report: str, proposal: str) -> str:
        """
        撰写可行性分析
        
        Args:
            research_report: 研究报告
            proposal: 创新方案
            
        Returns:
            str: 可行性分析内容
        """
        self.logger.info("Writing feasibility analysis")
        
        # 合并研究报告和创新方案
        combined_content = f"{research_report}\n\n---\n\n{proposal}"
        
        prompt = Prompts.FEASIBILITY_PROMPT.format(
            research_report=combined_content,
            proposal=proposal
        )
        
        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=max(agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE) - 0.1, 0.1)  # 较低的温度以保持客观性
            )
            
            if response.is_successful() and response.response:
                analysis = response.response.strip()
                self.logger.info("Feasibility analysis generated successfully")
                return analysis
            else:
                self.logger.error(f"Failed to generate feasibility analysis: {response.error}")
                return self._create_fallback_feasibility()
        
        except Exception as e:
            self.logger.error(f"Error writing feasibility analysis: {e}")
            return self._create_fallback_feasibility()
    
    def create_detailed_notes(self, papers: List[Paper], topic: str) -> str:
        """
        创建详细的研究笔记
        
        Args:
            papers: 论文列表
            topic: 研究课题
            
        Returns:
            str: 详细笔记内容
        """
        self.logger.info(f"Creating detailed notes for {len(papers)} papers")
        
        notes_sections = []
        
        # 添加标题和概述
        notes_sections.append(f"# {topic} - 详细研究笔记\n")
        notes_sections.append(f"本文档包含对 {len(papers)} 篇相关论文的详细分析笔记。\n")
        notes_sections.append("---\n")
        
        # 为每篇论文创建详细笔记
        analyzed_papers = [paper for paper in papers if paper.has_analysis()]
        
        for i, paper in enumerate(analyzed_papers, 1):
            section = self._create_paper_note_section(paper, i)
            notes_sections.append(section)
            notes_sections.append("---\n")
        
        # 添加统计信息
        stats_section = self._create_statistics_section(papers, analyzed_papers)
        notes_sections.append(stats_section)
        
        return "\n".join(notes_sections)
    
    def _create_paper_note_section(self, paper: Paper, index: int) -> str:
        """
        为单篇论文创建笔记部分
        
        Args:
            paper: 论文对象
            index: 论文序号
            
        Returns:
            str: 论文笔记部分
        """
        sections = []
        
        # 标题和基本信息
        sections.append(f"## {index}. {paper.title}\n")
        
        # 作者和发表信息
        if paper.authors:
            authors_str = ", ".join(paper.authors[:3])  # 只显示前3个作者
            if len(paper.authors) > 3:
                authors_str += " et al."
            sections.append(f"**作者**: {authors_str}\n")
        
        if paper.journal:
            sections.append(f"**期刊**: {paper.journal}\n")
        
        if paper.publication_date:
            sections.append(f"**发表时间**: {paper.publication_date.strftime('%Y年%m月')}\n")
        
        if paper.citation_count:
            sections.append(f"**引用次数**: {paper.citation_count}\n")
        
        if paper.doi:
            sections.append(f"**DOI**: {paper.doi}\n")
        
        sections.append("")
        
        # AI分析结果
        if paper.analysis:
            analysis = paper.analysis
            
            sections.append("### 核心贡献")
            sections.append(f"{analysis.short_summary}\n")
            
            sections.append("### 与研究课题的相关性")
            sections.append(f"{analysis.relevance_to_topic}\n")
            
            sections.append("### 研究对象")
            sections.append(f"{analysis.research_subject}\n")
            
            sections.append("### 研究方法")
            sections.append(f"{analysis.methodology}\n")
            
            sections.append("### 数据来源")
            sections.append(f"{analysis.data_used}\n")
            
            sections.append("### 关键发现")
            for j, finding in enumerate(analysis.key_findings_or_results, 1):
                sections.append(f"{j}. {finding}")
            sections.append("")
        
        # 原始摘要
        if paper.abstract:
            sections.append("### 原始摘要")
            sections.append(f"{paper.abstract}\n")
        
        return "\n".join(sections)
    
    def _create_statistics_section(self, all_papers: List[Paper], analyzed_papers: List[Paper]) -> str:
        """
        创建统计信息部分
        
        Args:
            all_papers: 所有论文
            analyzed_papers: 已分析的论文
            
        Returns:
            str: 统计信息部分
        """
        sections = []
        
        sections.append("## 统计信息\n")
        sections.append(f"- **总论文数**: {len(all_papers)}")
        sections.append(f"- **成功分析数**: {len(analyzed_papers)}")
        sections.append(f"- **分析成功率**: {len(analyzed_papers)/len(all_papers)*100:.1f}%\n")
        
        # 按年份统计
        if analyzed_papers:
            year_counts = {}
            for paper in analyzed_papers:
                year = paper.get_year()
                if year:
                    year_counts[year] = year_counts.get(year, 0) + 1
            
            if year_counts:
                sections.append("### 按年份分布")
                for year in sorted(year_counts.keys()):
                    sections.append(f"- **{year}年**: {year_counts[year]}篇")
                sections.append("")
        
        # 按期刊统计
        journal_counts = {}
        for paper in analyzed_papers:
            if paper.journal:
                journal_counts[paper.journal] = journal_counts.get(paper.journal, 0) + 1
        
        if journal_counts:
            sections.append("### 主要期刊分布")
            sorted_journals = sorted(journal_counts.items(), key=lambda x: x[1], reverse=True)
            for journal, count in sorted_journals[:5]:  # 只显示前5个
                sections.append(f"- **{journal}**: {count}篇")
            sections.append("")
        
        return "\n".join(sections)
    
    def _create_fallback_report(self, topic: str, web_summaries: str, timeline: str) -> str:
        """创建备用报告"""
        return f"""# {topic} - 研究报告

## 概述
由于技术限制，本报告采用简化格式生成。

## 网络搜索摘要
{web_summaries}

## 发展时间线
{timeline}

## 结论
本报告基于可用信息生成，建议进一步完善分析。
"""

    def _format_paper_analyses_with_citations(self, papers: List[Paper]) -> str:
        """
        格式化论文分析，同时准备引用信息

        Args:
            papers: 论文列表

        Returns:
            str: 格式化的论文分析文本
        """
        if not papers:
            return "No papers available for analysis."

        analyses = []
        for paper in papers:
            if paper.has_analysis():
                # 为每篇论文生成引用键
                citation_key = paper.generate_citation_key()

                analysis_text = f"""
**Paper: {paper.title}**
Authors: <AUTHORS>
Citation Key: {citation_key}
Journal: {paper.journal or 'Unknown'}
Year: {paper.get_year() or 'Unknown'}

Summary: {paper.analysis.short_summary}
Relevance: {paper.analysis.relevance_to_topic}
Methodology: {paper.analysis.methodology}
Key Findings: {', '.join(paper.analysis.key_findings_or_results)}
"""
                analyses.append(analysis_text)

        return "\n".join(analyses)

    def _format_paper_analyses_for_report(self, papers: List[Paper]) -> str:
        """
        格式化论文分析用于报告生成（不带引用）

        Args:
            papers: 论文列表

        Returns:
            str: 格式化的论文分析文本
        """
        if not papers:
            return "No papers available for analysis."

        analyses = []
        for paper in papers:
            if paper.has_analysis():
                analysis_text = f"""
**{paper.title}**
{paper.analysis.short_summary}
Key findings: {', '.join(paper.analysis.key_findings_or_results)}
"""
                analyses.append(analysis_text)

        return "\n".join(analyses)

    def _create_citation_aware_prompt(self, topic: str, structured_analyses: str,
                                    web_summaries: str, timeline: str) -> str:
        """
        创建带引用要求的提示词

        Args:
            topic: 研究主题
            structured_analyses: 结构化论文分析
            web_summaries: 网络摘要
            timeline: 时间线

        Returns:
            str: 带引用要求的提示词
        """
        citation_instructions = f"""
IMPORTANT CITATION REQUIREMENTS:
- Use {config.CITATION_STYLE} citation style
- Include inline citations when referencing specific papers
- Use the Citation Key provided for each paper
- For {config.CITATION_STYLE} style: {"use (Author, Year) format" if config.CITATION_STYLE == "APA" else "use [number] format"}
- Ensure every claim is properly cited
- I will add the References section automatically, so focus on inline citations

"""

        base_prompt = self._create_astrophysics_research_report_prompt(
            topic, web_summaries, structured_analyses, timeline
        )

        return citation_instructions + base_prompt

    def _post_process_citations(self, report_content: str, papers: List[Paper]) -> str:
        """
        后处理报告内容，添加引用标记和参考文献列表

        Args:
            report_content: 原始报告内容
            papers: 论文列表

        Returns:
            str: 处理后的报告内容
        """
        processed_content = report_content

        # 查找并替换引用键为正式引用
        for paper in papers:
            if paper.citation_key and paper.citation_key in processed_content:
                # 添加引用并获取内联引用标记
                inline_citation = self.citation_manager.add_citation(paper)

                # 替换引用键为内联引用
                processed_content = processed_content.replace(paper.citation_key, inline_citation)

        # 添加参考文献列表
        bibliography = self.citation_manager.generate_bibliography()

        # 如果报告中已有References部分，替换它；否则添加到末尾
        if "## References" in processed_content or "# References" in processed_content:
            import re
            # 替换现有的References部分
            processed_content = re.sub(
                r'#{1,2}\s*References.*?(?=#{1,2}|\Z)',
                bibliography + "\n",
                processed_content,
                flags=re.DOTALL
            )
        else:
            # 添加到末尾
            processed_content += f"\n\n{bibliography}"

        return processed_content

    def _create_fallback_report_with_citations(self, topic: str, papers: List[Paper],
                                             web_summaries: str, timeline: str) -> str:
        """
        创建带引用的备用报告

        Args:
            topic: 研究主题
            papers: 论文列表
            web_summaries: 网络摘要
            timeline: 时间线

        Returns:
            str: 备用报告内容
        """
        # 选择前几篇论文作为示例引用
        sample_papers = papers[:5] if papers else []

        report_parts = [f"# Research Report: {topic}\n"]
        report_parts.append("## Executive Summary")
        report_parts.append("This is a fallback report generated due to technical issues.\n")

        if sample_papers:
            report_parts.append("## Key Literature")
            for paper in sample_papers:
                if paper.has_analysis():
                    citation = self.citation_manager.add_citation(paper)
                    report_parts.append(f"- {paper.title} {citation}")
                    report_parts.append(f"  {paper.analysis.short_summary}\n")

        report_parts.append("## Current State of Knowledge")
        report_parts.append(web_summaries)

        report_parts.append("## Timeline and Development")
        report_parts.append(timeline)

        report_parts.append("## Conclusion")
        report_parts.append("Further analysis is needed to provide comprehensive insights.")

        # 添加参考文献
        bibliography = self.citation_manager.generate_bibliography()
        report_parts.append(bibliography)

        return "\n\n".join(report_parts)
    
    def _create_fallback_proposal(self) -> str:
        """创建备用创新方案"""
        return """# 创新研究方案

## 研究问题
基于现有研究，识别出需要进一步探索的问题。

## 创新点
提出新的研究角度和方法。

## 研究假设
建立可检验的研究假设。

## 方法论
采用适当的研究方法进行验证。

*注：本方案为简化版本，建议进一步完善。*
"""
    
    def _create_fallback_feasibility(self) -> str:
        """创建备用可行性分析"""
        return """# 可行性分析

## 科学价值
评估研究的科学贡献潜力。

## 技术可行性
分析技术实现的可能性。

## 数据可获得性
评估所需数据的获取难度。

## 风险评估
识别主要风险并提出对策。

## 综合评估
给出总体可行性判断。

*注：本分析为简化版本，建议进行更详细的评估。*
"""

    def generate_chinese_research_report(self,
                                       topic: str,
                                       papers: List[Paper],
                                       web_summaries: str = "",
                                       timeline: str = "") -> str:
        """
        生成完整的中文研究报告

        Args:
            topic: 研究主题
            papers: 论文列表
            web_summaries: 网络搜索摘要
            timeline: 时间线信息

        Returns:
            str: 保存的文件路径
        """
        try:
            self.enhanced_logger.step("生成中文研究报告")

            # 生成报告内容
            report_content = self._create_chinese_report_content(
                topic, papers, web_summaries, timeline
            )

            # 保存报告
            file_path = self._save_chinese_report(report_content, topic)

            self.enhanced_logger.success(f"中文研究报告生成完成")
            return file_path

        except Exception as e:
            self.enhanced_logger.error(f"生成中文研究报告失败: {e}")
            return ""

    def _create_chinese_report_content(self,
                                     topic: str,
                                     papers: List[Paper],
                                     web_summaries: str,
                                     timeline: str) -> str:
        """
        创建中文报告内容

        Args:
            topic: 研究主题
            papers: 论文列表
            web_summaries: 网络搜索摘要
            timeline: 时间线信息

        Returns:
            str: 报告内容
        """
        # 报告标题和基本信息
        content = f"# {topic} - 深度研究报告\n\n"
        content += f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n"
        content += f"**分析论文数量**: {len(papers)} 篇\n"
        content += f"**报告类型**: 天体物理学专业研究报告\n\n"
        content += "---\n\n"

        # 1. 研究背景（研究历程）
        content += "## 1. 研究背景\n\n"
        background = self._generate_research_background(topic, timeline)
        content += background + "\n\n"

        # 2. 文献综述
        content += "## 2. 文献综述\n\n"
        literature_review = self._generate_literature_review(papers, topic)
        content += literature_review + "\n\n"

        # 3. 关键发现
        content += "## 3. 关键发现\n\n"
        key_findings = self._generate_key_findings(papers)
        content += key_findings + "\n\n"

        # 4. 技术分析
        content += "## 4. 技术分析\n\n"
        technical_analysis = self._generate_technical_analysis(papers)
        content += technical_analysis + "\n\n"

        # 5. 未来方向
        content += "## 5. 未来研究方向\n\n"
        future_directions = self._generate_future_directions(papers, topic)
        content += future_directions + "\n\n"

        # 6. 参考文献
        content += "## 6. 参考文献\n\n"
        references = self._generate_chinese_references(papers)
        content += references + "\n\n"

        # 报告总结
        content += "---\n\n"
        content += "**报告说明**: 本报告基于AI分析生成，整合了最新的学术文献和研究进展。"
        content += "建议结合具体研究需求进行深入分析。\n\n"

        return content

    def _generate_research_background(self, topic: str, timeline: str) -> str:
        """生成研究背景部分"""
        try:
            prompt = f"""请为天体物理学研究主题"{topic}"撰写研究背景部分，要求：

1. 介绍该研究领域的发展历程和重要里程碑
2. 阐述当前研究的重要性和意义
3. 概述主要的研究挑战和机遇
4. 语言要求：中文学术写作，专业严谨
5. 长度：300-500字

{f"时间线信息参考：{timeline}" if timeline else ""}

请生成研究背景："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return f"在{topic}领域，相关研究正在快速发展，本报告将对最新进展进行深入分析。"

        except Exception as e:
            self.logger.warning(f"生成研究背景失败: {e}")
            return f"本报告聚焦于{topic}的最新研究进展和发展趋势。"

    def _generate_literature_review(self, papers: List[Paper], topic: str) -> str:
        """生成文献综述部分"""
        try:
            # 准备论文信息
            paper_summaries = []
            for paper in papers[:10]:  # 使用前10篇论文
                if paper.has_analysis():
                    summary = f"- {paper.title} ({paper.get_year()}): "
                    if paper.analysis.key_findings:
                        summary += paper.analysis.key_findings[0][:100] + "..."
                    paper_summaries.append(summary)

            papers_text = "\n".join(paper_summaries)

            prompt = f"""基于以下论文信息，为"{topic}"撰写文献综述，要求：

论文信息：
{papers_text}

要求：
1. 系统梳理该领域的研究现状
2. 分析不同研究方法和观点
3. 识别研究热点和趋势
4. 指出研究空白和争议点
5. 语言：中文学术写作
6. 长度：500-800字

请生成文献综述："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return "通过对相关文献的系统分析，我们发现该领域研究活跃，方法多样，为进一步研究提供了坚实基础。"

        except Exception as e:
            self.logger.warning(f"生成文献综述失败: {e}")
            return "文献分析显示，该研究领域具有重要的科学价值和应用前景。"

    def _generate_key_findings(self, papers: List[Paper]) -> str:
        """生成关键发现部分"""
        try:
            # 收集所有关键发现
            all_findings = []
            for paper in papers:
                if paper.has_analysis() and paper.analysis.key_findings:
                    for finding in paper.analysis.key_findings[:2]:  # 每篇论文最多2个发现
                        all_findings.append(f"- {finding}")

            findings_text = "\n".join(all_findings[:15])  # 最多15个发现

            prompt = f"""基于以下研究发现，总结关键发现，要求：

研究发现：
{findings_text}

要求：
1. 将相似发现进行归类整合
2. 突出最重要和最新的发现
3. 用中文学术语言表述
4. 按重要性排序
5. 长度：400-600字

请生成关键发现总结："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return "研究发现了多个重要的科学结论，为该领域的发展提供了新的见解。"

        except Exception as e:
            self.logger.warning(f"生成关键发现失败: {e}")
            return "通过综合分析，识别出了若干重要的研究发现和科学结论。"

    def _generate_technical_analysis(self, papers: List[Paper]) -> str:
        """生成技术分析部分"""
        try:
            # 收集方法论信息
            methodologies = []
            for paper in papers:
                if paper.has_analysis() and paper.analysis.methodology:
                    methodologies.append(paper.analysis.methodology)

            methods_text = "\n".join(methodologies[:10])

            prompt = f"""基于以下研究方法信息，进行技术分析，要求：

研究方法：
{methods_text}

要求：
1. 分析主要的研究方法和技术手段
2. 评估不同方法的优缺点
3. 识别技术发展趋势
4. 讨论方法学创新
5. 中文学术写作
6. 长度：400-600字

请生成技术分析："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return "技术分析显示，该领域采用了多种先进的研究方法，技术手段不断创新发展。"

        except Exception as e:
            self.logger.warning(f"生成技术分析失败: {e}")
            return "研究采用了多种技术方法，展现了该领域的技术多样性和创新性。"

    def _generate_future_directions(self, papers: List[Paper], topic: str) -> str:
        """生成未来方向部分"""
        try:
            prompt = f"""基于对"{topic}"领域的分析，提出未来研究方向，要求：

1. 识别当前研究的局限性和空白
2. 提出具体的研究方向和问题
3. 考虑技术发展和观测能力提升
4. 讨论跨学科合作机会
5. 中文学术写作
6. 长度：300-500字

请生成未来研究方向："""

            response = self.llm_client.generate_response(prompt)
            if response.is_successful():
                return response.content.strip()
            else:
                return f"在{topic}领域，未来研究应关注技术创新、观测能力提升和理论模型完善等方向。"

        except Exception as e:
            self.logger.warning(f"生成未来方向失败: {e}")
            return "未来研究应继续深化理论认识，改进观测技术，拓展研究范围。"

    def _generate_chinese_references(self, papers: List[Paper]) -> str:
        """生成中文格式的参考文献"""
        references = []

        for i, paper in enumerate(papers, 1):
            try:
                # 基本引用格式：作者. 标题. 期刊, 年份
                authors = ", ".join(paper.authors[:3]) if paper.authors else "未知作者"
                if len(paper.authors) > 3:
                    authors += " 等"

                title = paper.title or "未知标题"
                journal = paper.journal or "未知期刊"
                year = paper.get_year()

                reference = f"{i}. {authors}. {title}. {journal}, {year}."

                if paper.doi:
                    reference += f" DOI: {paper.doi}"

                references.append(reference)

            except Exception as e:
                self.logger.warning(f"生成引用失败: {e}")
                continue

        return "\n".join(references)

    def _save_chinese_report(self, content: str, topic: str) -> str:
        """
        保存中文报告到文件

        Args:
            content: 报告内容
            topic: 研究主题

        Returns:
            str: 保存的文件路径
        """
        try:
            # 生成文件名
            import re
            simplified_topic = re.sub(r'[^\w\s-]', '', topic)
            simplified_topic = re.sub(r'[-\s]+', '_', simplified_topic)
            simplified_topic = simplified_topic[:30]  # 限制长度

            filename = f"research_report_{simplified_topic}.md"
            file_path = self.outputs_dir / filename

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            self.enhanced_logger.file_info("保存中文研究报告", str(file_path))
            return str(file_path)

        except Exception as e:
            self.enhanced_logger.error(f"保存中文报告失败: {e}")
            return ""
