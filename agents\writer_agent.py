# agents/writer_agent.py
#
# 报告撰写代理 - 负责生成最终的研究报告

import logging
from typing import List, Dict, Any, Optional

from clients import LLMClient
from models import Paper, LLMResponse
from prompts import Prompts


class WriterAgent:
    """报告撰写代理"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化撰写代理

        Args:
            llm_client: LLM客户端，如果不提供则创建新实例
        """
        if llm_client:
            self.llm_client = llm_client
        else:
            # 创建专门为WriterAgent配置的LLM客户端
            base_client = LLMClient()
            self.llm_client = base_client.create_client_for_agent("writer")
        self.logger = logging.getLogger(__name__)
    
    def write_research_report(self, 
                            topic: str,
                            web_summaries: str,
                            structured_paper_analyses: str,
                            timeline: str) -> str:
        """
        撰写深度研究报告
        
        Args:
            topic: 研究课题
            web_summaries: 网络搜索摘要
            structured_paper_analyses: 结构化论文分析
            timeline: 时间线信息
            
        Returns:
            str: 研究报告内容
        """
        self.logger.info(f"Writing research report for topic: {topic}")
        
        # 使用主报告撰写提示词
        prompt = Prompts.MASTER_WRITER_PROMPT.format(
            topic=topic,
            web_summaries=web_summaries,
            structured_paper_analyses=structured_paper_analyses,
            timeline=timeline
        )
        
        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE)
            )
            
            if response.is_successful() and response.response:
                report = response.response.strip()
                self.logger.info("Research report generated successfully")
                return report
            else:
                self.logger.error(f"Failed to generate research report: {response.error}")
                return self._create_fallback_report(topic, web_summaries, timeline)
        
        except Exception as e:
            self.logger.error(f"Error writing research report: {e}")
            return self._create_fallback_report(topic, web_summaries, timeline)
    
    def write_innovation_proposal(self, research_report: str) -> str:
        """
        基于研究报告撰写创新方案
        
        Args:
            research_report: 完整的研究报告
            
        Returns:
            str: 创新方案内容
        """
        self.logger.info("Writing innovation proposal")
        
        prompt = Prompts.INNOVATION_PROMPT.format(research_report=research_report)
        
        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=min(agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE) + 0.1, 1.0)  # 稍高的温度以增加创新性
            )
            
            if response.is_successful() and response.response:
                proposal = response.response.strip()
                self.logger.info("Innovation proposal generated successfully")
                return proposal
            else:
                self.logger.error(f"Failed to generate innovation proposal: {response.error}")
                return self._create_fallback_proposal()
        
        except Exception as e:
            self.logger.error(f"Error writing innovation proposal: {e}")
            return self._create_fallback_proposal()
    
    def write_feasibility_analysis(self, research_report: str, proposal: str) -> str:
        """
        撰写可行性分析
        
        Args:
            research_report: 研究报告
            proposal: 创新方案
            
        Returns:
            str: 可行性分析内容
        """
        self.logger.info("Writing feasibility analysis")
        
        # 合并研究报告和创新方案
        combined_content = f"{research_report}\n\n---\n\n{proposal}"
        
        prompt = Prompts.FEASIBILITY_PROMPT.format(
            research_report=combined_content,
            proposal=proposal
        )
        
        try:
            agent_config = config.get_agent_config("writer")
            response = self.llm_client.generate(
                prompt,
                max_tokens=agent_config.get("max_tokens", config.DEFAULT_LLM_MAX_TOKENS),
                temperature=max(agent_config.get("temperature", config.DEFAULT_LLM_TEMPERATURE) - 0.1, 0.1)  # 较低的温度以保持客观性
            )
            
            if response.is_successful() and response.response:
                analysis = response.response.strip()
                self.logger.info("Feasibility analysis generated successfully")
                return analysis
            else:
                self.logger.error(f"Failed to generate feasibility analysis: {response.error}")
                return self._create_fallback_feasibility()
        
        except Exception as e:
            self.logger.error(f"Error writing feasibility analysis: {e}")
            return self._create_fallback_feasibility()
    
    def create_detailed_notes(self, papers: List[Paper], topic: str) -> str:
        """
        创建详细的研究笔记
        
        Args:
            papers: 论文列表
            topic: 研究课题
            
        Returns:
            str: 详细笔记内容
        """
        self.logger.info(f"Creating detailed notes for {len(papers)} papers")
        
        notes_sections = []
        
        # 添加标题和概述
        notes_sections.append(f"# {topic} - 详细研究笔记\n")
        notes_sections.append(f"本文档包含对 {len(papers)} 篇相关论文的详细分析笔记。\n")
        notes_sections.append("---\n")
        
        # 为每篇论文创建详细笔记
        analyzed_papers = [paper for paper in papers if paper.has_analysis()]
        
        for i, paper in enumerate(analyzed_papers, 1):
            section = self._create_paper_note_section(paper, i)
            notes_sections.append(section)
            notes_sections.append("---\n")
        
        # 添加统计信息
        stats_section = self._create_statistics_section(papers, analyzed_papers)
        notes_sections.append(stats_section)
        
        return "\n".join(notes_sections)
    
    def _create_paper_note_section(self, paper: Paper, index: int) -> str:
        """
        为单篇论文创建笔记部分
        
        Args:
            paper: 论文对象
            index: 论文序号
            
        Returns:
            str: 论文笔记部分
        """
        sections = []
        
        # 标题和基本信息
        sections.append(f"## {index}. {paper.title}\n")
        
        # 作者和发表信息
        if paper.authors:
            authors_str = ", ".join(paper.authors[:3])  # 只显示前3个作者
            if len(paper.authors) > 3:
                authors_str += " et al."
            sections.append(f"**作者**: {authors_str}\n")
        
        if paper.journal:
            sections.append(f"**期刊**: {paper.journal}\n")
        
        if paper.publication_date:
            sections.append(f"**发表时间**: {paper.publication_date.strftime('%Y年%m月')}\n")
        
        if paper.citation_count:
            sections.append(f"**引用次数**: {paper.citation_count}\n")
        
        if paper.doi:
            sections.append(f"**DOI**: {paper.doi}\n")
        
        sections.append("")
        
        # AI分析结果
        if paper.analysis:
            analysis = paper.analysis
            
            sections.append("### 核心贡献")
            sections.append(f"{analysis.short_summary}\n")
            
            sections.append("### 与研究课题的相关性")
            sections.append(f"{analysis.relevance_to_topic}\n")
            
            sections.append("### 研究对象")
            sections.append(f"{analysis.research_subject}\n")
            
            sections.append("### 研究方法")
            sections.append(f"{analysis.methodology}\n")
            
            sections.append("### 数据来源")
            sections.append(f"{analysis.data_used}\n")
            
            sections.append("### 关键发现")
            for j, finding in enumerate(analysis.key_findings_or_results, 1):
                sections.append(f"{j}. {finding}")
            sections.append("")
        
        # 原始摘要
        if paper.abstract:
            sections.append("### 原始摘要")
            sections.append(f"{paper.abstract}\n")
        
        return "\n".join(sections)
    
    def _create_statistics_section(self, all_papers: List[Paper], analyzed_papers: List[Paper]) -> str:
        """
        创建统计信息部分
        
        Args:
            all_papers: 所有论文
            analyzed_papers: 已分析的论文
            
        Returns:
            str: 统计信息部分
        """
        sections = []
        
        sections.append("## 统计信息\n")
        sections.append(f"- **总论文数**: {len(all_papers)}")
        sections.append(f"- **成功分析数**: {len(analyzed_papers)}")
        sections.append(f"- **分析成功率**: {len(analyzed_papers)/len(all_papers)*100:.1f}%\n")
        
        # 按年份统计
        if analyzed_papers:
            year_counts = {}
            for paper in analyzed_papers:
                year = paper.get_year()
                if year:
                    year_counts[year] = year_counts.get(year, 0) + 1
            
            if year_counts:
                sections.append("### 按年份分布")
                for year in sorted(year_counts.keys()):
                    sections.append(f"- **{year}年**: {year_counts[year]}篇")
                sections.append("")
        
        # 按期刊统计
        journal_counts = {}
        for paper in analyzed_papers:
            if paper.journal:
                journal_counts[paper.journal] = journal_counts.get(paper.journal, 0) + 1
        
        if journal_counts:
            sections.append("### 主要期刊分布")
            sorted_journals = sorted(journal_counts.items(), key=lambda x: x[1], reverse=True)
            for journal, count in sorted_journals[:5]:  # 只显示前5个
                sections.append(f"- **{journal}**: {count}篇")
            sections.append("")
        
        return "\n".join(sections)
    
    def _create_fallback_report(self, topic: str, web_summaries: str, timeline: str) -> str:
        """创建备用报告"""
        return f"""# {topic} - 研究报告

## 概述
由于技术限制，本报告采用简化格式生成。

## 网络搜索摘要
{web_summaries}

## 发展时间线
{timeline}

## 结论
本报告基于可用信息生成，建议进一步完善分析。
"""
    
    def _create_fallback_proposal(self) -> str:
        """创建备用创新方案"""
        return """# 创新研究方案

## 研究问题
基于现有研究，识别出需要进一步探索的问题。

## 创新点
提出新的研究角度和方法。

## 研究假设
建立可检验的研究假设。

## 方法论
采用适当的研究方法进行验证。

*注：本方案为简化版本，建议进一步完善。*
"""
    
    def _create_fallback_feasibility(self) -> str:
        """创建备用可行性分析"""
        return """# 可行性分析

## 科学价值
评估研究的科学贡献潜力。

## 技术可行性
分析技术实现的可能性。

## 数据可获得性
评估所需数据的获取难度。

## 风险评估
识别主要风险并提出对策。

## 综合评估
给出总体可行性判断。

*注：本分析为简化版本，建议进行更详细的评估。*
"""
