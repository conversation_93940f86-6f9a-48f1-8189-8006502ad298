#!/usr/bin/env python3
# audit_agent_model_config.py
#
# Comprehensive audit of AI agents' model configuration and usage

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """Setup logging to capture model usage"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def audit_environment_configuration():
    """Audit 1: Verify environment configuration"""
    print("🔍 AUDIT 1: Environment Configuration")
    print("=" * 80)
    
    try:
        from config import config
        
        print("📋 Environment Variables from .env:")
        print(f"   OPENAI_COMPATIBLE_MODEL: {config.OPENAI_COMPATIBLE_MODEL}")
        print(f"   PLANNER_AGENT_MODEL: {config.PLANNER_AGENT_MODEL}")
        print(f"   SYNTHESIZER_AGENT_MODEL: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WRITER_AGENT_MODEL: {config.WRITER_AGENT_MODEL}")
        
        print("\n📋 Expected Model Usage:")
        print(f"   PlannerAgent should use: {config.PLANNER_AGENT_MODEL}")
        print(f"   SynthesizerAgent should use: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WriterAgent should use: {config.WRITER_AGENT_MODEL}")
        
        # Check if agent models are properly configured
        issues = []
        if not config.PLANNER_AGENT_MODEL:
            issues.append("PLANNER_AGENT_MODEL is empty")
        if not config.SYNTHESIZER_AGENT_MODEL:
            issues.append("SYNTHESIZER_AGENT_MODEL is empty")
        if not config.WRITER_AGENT_MODEL:
            issues.append("WRITER_AGENT_MODEL is empty")
        
        if issues:
            print(f"\n❌ Configuration Issues Found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print(f"\n✅ All agent models are properly configured")
            return True
        
    except Exception as e:
        print(f"❌ Environment configuration audit failed: {e}")
        return False


def audit_get_agent_config_method():
    """Audit 2: Investigate get_agent_config method"""
    print("\n🔍 AUDIT 2: get_agent_config Method Logic")
    print("=" * 80)
    
    try:
        from config import config
        
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 Testing get_agent_config('{agent}'):")
            
            # Get agent config
            agent_config = config.get_agent_config(agent)
            
            # Get expected values
            expected_model = getattr(config, f"{agent.upper()}_AGENT_MODEL")
            expected_provider = getattr(config, f"{agent.upper()}_AGENT_PROVIDER")
            
            print(f"   Expected Provider: {expected_provider}")
            print(f"   Expected Model: {expected_model}")
            print(f"   Actual Provider: {agent_config.get('provider')}")
            print(f"   Actual Model: {agent_config.get('model')}")
            print(f"   Temperature: {agent_config.get('temperature')}")
            print(f"   Max Tokens: {agent_config.get('max_tokens')}")
            
            # Verify correctness
            if (agent_config.get('provider') == expected_provider and 
                agent_config.get('model') == expected_model):
                print(f"   ✅ Configuration CORRECT")
            else:
                print(f"   ❌ Configuration INCORRECT")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ get_agent_config audit failed: {e}")
        return False


def audit_create_client_for_agent_method():
    """Audit 3: Investigate create_client_for_agent method"""
    print("\n🔍 AUDIT 3: create_client_for_agent Method Logic")
    print("=" * 80)
    
    try:
        from clients.llm_client import LLMClient
        from config import config
        
        base_client = LLMClient()
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 Testing create_client_for_agent('{agent}'):")
            
            # Get expected model
            expected_model = getattr(config, f"{agent.upper()}_AGENT_MODEL")
            
            # Create client
            agent_client = base_client.create_client_for_agent(agent)
            
            if agent_client:
                actual_model = getattr(agent_client, 'model', None)
                client_type = type(agent_client).__name__
                
                print(f"   Expected Model: {expected_model}")
                print(f"   Client Type: {client_type}")
                print(f"   Actual Model: {actual_model}")
                
                if actual_model == expected_model:
                    print(f"   ✅ Client model CORRECT")
                else:
                    print(f"   ❌ Client model INCORRECT")
                    print(f"       Expected: {expected_model}")
                    print(f"       Got: {actual_model}")
                    all_correct = False
            else:
                print(f"   ❌ Failed to create client")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ create_client_for_agent audit failed: {e}")
        return False


def audit_runtime_agent_instances():
    """Audit 4: Test runtime verification with actual agent instances"""
    print("\n🔍 AUDIT 4: Runtime Agent Instance Verification")
    print("=" * 80)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        agents_info = [
            ("planner", PlannerAgent, config.PLANNER_AGENT_MODEL),
            ("synthesizer", SynthesizerAgent, config.SYNTHESIZER_AGENT_MODEL),
            ("writer", WriterAgent, config.WRITER_AGENT_MODEL)
        ]
        
        all_correct = True
        
        for agent_name, agent_class, expected_model in agents_info:
            print(f"\n📋 Testing {agent_name.title()}Agent Runtime Instance:")
            
            # Create agent instance
            agent = agent_class()
            
            if hasattr(agent, 'llm_client') and agent.llm_client:
                actual_model = getattr(agent.llm_client, 'model', None)
                client_type = type(agent.llm_client).__name__
                
                print(f"   Agent Class: {agent_class.__name__}")
                print(f"   LLM Client Type: {client_type}")
                print(f"   Expected Model: {expected_model}")
                print(f"   Actual Model: {actual_model}")
                
                if actual_model == expected_model:
                    print(f"   ✅ Runtime model usage CORRECT")
                else:
                    print(f"   ❌ Runtime model usage INCORRECT")
                    print(f"       Agent is using: {actual_model}")
                    print(f"       Should be using: {expected_model}")
                    all_correct = False
            else:
                print(f"   ❌ Agent has no LLM client or client is None")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Runtime agent instance audit failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def audit_fallback_mechanisms():
    """Audit 5: Test fallback mechanisms"""
    print("\n🔍 AUDIT 5: Fallback Mechanism Analysis")
    print("=" * 80)
    
    try:
        from config import config
        
        print("📋 Testing fallback logic when agent model is empty:")
        
        # Test the fallback logic in get_agent_config
        test_cases = [
            ("openai-compatible", config.OPENAI_COMPATIBLE_MODEL),
            ("openai", config.OPENAI_MODEL),
            ("anthropic", config.ANTHROPIC_MODEL),
            ("gemini", config.GEMINI_MODEL)
        ]
        
        all_correct = True
        
        for provider, expected_fallback in test_cases:
            print(f"\n   Testing provider: {provider}")
            
            # Simulate empty model config
            test_config = {
                "provider": provider,
                "model": "",  # Empty model
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            # Apply fallback logic (same as in get_agent_config)
            if not test_config.get("model"):
                if provider == "openai":
                    test_config["model"] = config.OPENAI_MODEL
                elif provider == "openai-compatible":
                    test_config["model"] = config.OPENAI_COMPATIBLE_MODEL
                elif provider == "anthropic":
                    test_config["model"] = config.ANTHROPIC_MODEL
                elif provider == "gemini":
                    test_config["model"] = config.GEMINI_MODEL
            
            actual_fallback = test_config["model"]
            
            print(f"     Expected fallback: {expected_fallback}")
            print(f"     Actual fallback: {actual_fallback}")
            
            if actual_fallback == expected_fallback:
                print(f"     ✅ Fallback logic CORRECT")
            else:
                print(f"     ❌ Fallback logic INCORRECT")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Fallback mechanism audit failed: {e}")
        return False


def audit_model_selection_chain():
    """Audit 6: Trace the complete model selection chain"""
    print("\n🔍 AUDIT 6: Complete Model Selection Chain Trace")
    print("=" * 80)
    
    try:
        from config import config
        from clients.llm_client import LLMClient
        
        print("📋 Tracing model selection for SynthesizerAgent:")
        
        # Step 1: Environment variable
        env_model = config.SYNTHESIZER_AGENT_MODEL
        print(f"   Step 1 - Environment: SYNTHESIZER_AGENT_MODEL = '{env_model}'")
        
        # Step 2: get_agent_config
        agent_config = config.get_agent_config("synthesizer")
        config_model = agent_config.get("model")
        print(f"   Step 2 - get_agent_config: model = '{config_model}'")
        
        # Step 3: create_client_for_agent
        base_client = LLMClient()
        
        # Simulate the logic in create_client_for_agent
        model = agent_config.get("model")
        if not model:
            model = config.get_model_for_provider(agent_config.get("provider"))
        
        print(f"   Step 3 - create_client_for_agent: final model = '{model}'")
        
        # Step 4: Actual client creation
        synthesizer_client = base_client.create_client_for_agent("synthesizer")
        actual_model = getattr(synthesizer_client, 'model', None)
        print(f"   Step 4 - Actual client: model = '{actual_model}'")
        
        # Verify the chain
        if env_model == config_model == model == actual_model:
            print(f"   ✅ Model selection chain is CONSISTENT")
            return True
        else:
            print(f"   ❌ Model selection chain is INCONSISTENT")
            print(f"       Environment: {env_model}")
            print(f"       Config: {config_model}")
            print(f"       Selection: {model}")
            print(f"       Actual: {actual_model}")
            return False
        
    except Exception as e:
        print(f"❌ Model selection chain audit failed: {e}")
        return False


def run_comprehensive_audit():
    """Run all audits and provide summary"""
    print("🚀 COMPREHENSIVE AI AGENT MODEL CONFIGURATION AUDIT")
    print("=" * 100)
    
    # Setup logging
    setup_logging()
    
    audits = [
        ("Environment Configuration", audit_environment_configuration),
        ("get_agent_config Method", audit_get_agent_config_method),
        ("create_client_for_agent Method", audit_create_client_for_agent_method),
        ("Runtime Agent Instances", audit_runtime_agent_instances),
        ("Fallback Mechanisms", audit_fallback_mechanisms),
        ("Model Selection Chain", audit_model_selection_chain),
    ]
    
    results = []
    
    for audit_name, audit_func in audits:
        try:
            result = audit_func()
            results.append((audit_name, result))
        except Exception as e:
            print(f"❌ {audit_name} audit crashed: {e}")
            results.append((audit_name, False))
    
    # Summary
    print("\n🎉 AUDIT RESULTS SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for audit_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {audit_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} audits passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL AUDITS PASSED!")
        print("✅ Each agent is using its specifically configured model")
        print("✅ No fallback mechanisms are incorrectly overriding agent-specific models")
        print("✅ Model selection logic is working correctly")
    else:
        print("\n❌ CONFIGURATION ISSUES FOUND!")
        print("🔧 Issues that need fixing:")
        for audit_name, result in results:
            if not result:
                print(f"   - {audit_name}")
    
    return passed == total


if __name__ == "__main__":
    success = run_comprehensive_audit()
    sys.exit(0 if success else 1)
