#!/usr/bin/env python3
# citation_demo.py
#
# AI研究助理系统引用功能完整演示

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from models.paper import Paper, PaperAnalysis
from agents.writer_agent import WriterAgent
from config import config


def create_demo_papers():
    """创建演示用的论文数据"""
    papers = []
    
    # 创建5篇示例论文
    paper_data = [
        {
            "title": "Deep Learning for Galaxy Classification in Large-Scale Surveys",
            "authors": ["<PERSON>, <PERSON>", "<PERSON>, <PERSON>", "<PERSON>, <PERSON>"],
            "journal": "Astrophysical Journal",
            "year": 2023,
            "volume": "920",
            "issue": "1",
            "pages": "45-62",
            "doi": "10.3847/1538-4357/ac2345",
            "summary": "Presents a novel CNN architecture for automated galaxy classification achieving 97% accuracy on SDSS data.",
            "findings": [
                "97% accuracy in galaxy morphology classification",
                "Reduced processing time by 80%",
                "Identified 1,200 new galaxy candidates"
            ]
        },
        {
            "title": "Neural Network Approaches to Exoplanet Transit Detection",
            "authors": ["<PERSON>, <PERSON>", "<PERSON>, <PERSON>."],
            "journal": "Nature Astronomy",
            "year": 2023,
            "volume": "8",
            "pages": "123-135",
            "doi": "10.1038/s41550-023-02345-6",
            "summary": "Develops a transformer-based model for detecting exoplanet transits in Kepler data with unprecedented sensitivity.",
            "findings": [
                "99.2% detection accuracy for Earth-sized planets",
                "50% reduction in false positives",
                "Discovered 15 new exoplanet candidates"
            ]
        },
        {
            "title": "Machine Learning for Gravitational Wave Signal Processing",
            "authors": ["Wilson, Sarah E.", "Davis, Michael R.", "Thompson, Lisa M.", "Garcia, Carlos J."],
            "journal": "Physical Review D",
            "year": 2023,
            "volume": "108",
            "issue": "4",
            "pages": "042001",
            "doi": "10.1103/PhysRevD.108.042001",
            "summary": "Implements deep learning algorithms for real-time gravitational wave detection and parameter estimation.",
            "findings": [
                "40% improvement in detection sensitivity",
                "Real-time parameter estimation capability",
                "Enhanced noise characterization methods"
            ]
        },
        {
            "title": "AI-Driven Analysis of Stellar Spectra for Chemical Abundance Determination",
            "authors": ["Lee, Jennifer Y.", "Anderson, Robert K."],
            "journal": "Astronomical Journal",
            "year": 2023,
            "volume": "166",
            "issue": "3",
            "pages": "89-104",
            "doi": "10.3847/1538-3881/ace123",
            "summary": "Applies machine learning to automate stellar chemical abundance measurements from high-resolution spectra.",
            "findings": [
                "Precision comparable to manual analysis",
                "1000x faster processing speed",
                "Analyzed 50,000 stellar spectra"
            ]
        },
        {
            "title": "Deep Learning Applications in Cosmic Ray Identification",
            "authors": ["Kumar, Raj", "Patel, Priya", "O'Connor, Sean"],
            "journal": "Astroparticle Physics",
            "year": 2023,
            "volume": "152",
            "pages": "102876",
            "doi": "10.1016/j.astropartphys.2023.102876",
            "summary": "Develops convolutional neural networks for automated cosmic ray identification in astronomical images.",
            "findings": [
                "95% accuracy in cosmic ray detection",
                "Reduced image processing artifacts",
                "Improved photometric precision"
            ]
        }
    ]
    
    for data in paper_data:
        paper = Paper(
            title=data["title"],
            authors=data["authors"],
            journal=data["journal"],
            publication_date=datetime(data["year"], 6, 15),
            volume=data.get("volume"),
            issue=data.get("issue"),
            pages=data.get("pages"),
            doi=data["doi"],
            citation_count=45,
            source="ads"
        )
        
        paper.analysis = PaperAnalysis(
            short_summary=data["summary"],
            relevance_to_topic="Highly relevant to deep learning applications in astrophysics",
            research_subject="Astronomical data analysis using machine learning",
            methodology="Deep learning and neural network approaches",
            data_used="Large-scale astronomical survey data",
            key_findings_or_results=data["findings"]
        )
        
        papers.append(paper)
    
    return papers


def demonstrate_citation_styles():
    """演示不同的引用格式"""
    print("🎨 引用格式演示")
    print("=" * 60)
    
    papers = create_demo_papers()
    sample_paper = papers[0]
    
    print(f"示例论文: {sample_paper.title}")
    print(f"作者: {', '.join(sample_paper.authors)}")
    print(f"期刊: {sample_paper.journal} ({sample_paper.get_year()})")
    print()
    
    # 演示不同格式
    formats = ["APA", "IEEE", "Nature"]
    
    for fmt in formats:
        print(f"📋 {fmt} 格式:")
        print("-" * 30)
        
        # 临时设置格式
        original_style = config.CITATION_STYLE
        config.CITATION_STYLE = fmt
        
        try:
            writer = WriterAgent()
            if writer.citation_manager:
                citation = writer.citation_manager.add_citation(sample_paper)
                print(f"内联引用: {citation}")
                
                bibliography = writer.citation_manager.generate_bibliography()
                print(f"参考文献:")
                print(bibliography.split('\n')[2])  # 只显示第一个引用条目
                print()
        finally:
            config.CITATION_STYLE = original_style


def generate_sample_report_with_citations():
    """生成带引用的示例报告"""
    print("📝 生成带引用的研究报告演示")
    print("=" * 60)
    
    papers = create_demo_papers()
    
    # 确保启用引用功能
    original_enable = config.ENABLE_CITATIONS
    config.ENABLE_CITATIONS = True
    
    try:
        writer = WriterAgent()
        
        # 创建模拟的报告内容（简化版）
        topic = "Deep Learning Applications in Astrophysics"
        
        # 生成结构化分析
        structured_analyses = writer._format_paper_analyses_with_citations(papers)
        
        # 创建示例报告内容
        sample_report = f"""# {topic}: Comprehensive Research Report

## Executive Summary

Deep learning has revolutionized astrophysical data analysis, enabling unprecedented accuracy and efficiency in various astronomical tasks. This report synthesizes recent advances in the field based on analysis of key literature.

## Current State of Knowledge

### Galaxy Classification
Recent work by {papers[0].generate_citation_key()} has demonstrated remarkable success in automated galaxy classification using convolutional neural networks. The study achieved 97% accuracy on large-scale survey data, representing a significant improvement over traditional methods.

### Exoplanet Detection
The field of exoplanet detection has been transformed by neural network approaches. {papers[1].generate_citation_key()} developed a transformer-based model that achieves 99.2% detection accuracy for Earth-sized planets, with a 50% reduction in false positives compared to conventional methods.

### Gravitational Wave Analysis
Machine learning has also made significant contributions to gravitational wave astronomy. {papers[2].generate_citation_key()} implemented deep learning algorithms for real-time gravitational wave detection, achieving a 40% improvement in detection sensitivity.

### Stellar Spectroscopy
Automated analysis of stellar spectra has been enhanced through AI-driven approaches. {papers[3].generate_citation_key()} demonstrated that machine learning can achieve precision comparable to manual analysis while processing data 1000 times faster.

### Cosmic Ray Identification
The identification and removal of cosmic ray artifacts in astronomical images has been improved through deep learning. {papers[4].generate_citation_key()} developed convolutional neural networks that achieve 95% accuracy in cosmic ray detection.

## Key Methodologies

The reviewed studies primarily employ:
- Convolutional Neural Networks (CNNs) for image-based tasks
- Transformer architectures for sequence analysis
- Deep neural networks for signal processing
- Automated feature extraction and classification

## Future Directions

The integration of deep learning in astrophysics continues to evolve, with promising directions including:
- Multi-modal learning approaches
- Unsupervised discovery methods
- Real-time processing capabilities
- Enhanced interpretability of AI models

## Conclusion

Deep learning has become an indispensable tool in modern astrophysics, enabling new discoveries and improving the efficiency of data analysis across multiple domains. The reviewed literature demonstrates consistent improvements in accuracy, speed, and capability compared to traditional methods.
"""
        
        # 后处理添加引用
        final_report = writer._post_process_citations(sample_report, papers)
        
        print("生成的带引用研究报告:")
        print("=" * 40)
        print(final_report)
        
        # 显示引用统计
        if writer.citation_manager:
            stats = writer.citation_manager.get_citation_statistics()
            print(f"\n📊 引用统计:")
            print(f"总引用数: {stats['total_citations']}")
            print(f"引用格式: {stats['citation_style']}")
            print(f"引用期刊: {', '.join(stats['citation_journals'])}")
        
    finally:
        config.ENABLE_CITATIONS = original_enable


def demonstrate_bibtex_export():
    """演示BibTeX导出功能"""
    print("\n📚 BibTeX导出演示")
    print("=" * 60)
    
    papers = create_demo_papers()
    
    print("生成的BibTeX条目:")
    print("-" * 30)
    
    for i, paper in enumerate(papers[:3], 1):  # 只显示前3篇
        print(f"\n论文 {i}:")
        print(paper.get_bibtex_entry())


def main():
    """运行完整的引用功能演示"""
    print("🚀 AI研究助理系统引用功能完整演示")
    print("=" * 80)
    
    try:
        # 演示不同引用格式
        demonstrate_citation_styles()
        
        # 生成带引用的示例报告
        generate_sample_report_with_citations()
        
        # 演示BibTeX导出
        demonstrate_bibtex_export()
        
        print("\n🎉 引用功能演示完成!")
        print("\n✨ 主要特性:")
        print("✅ 支持APA、IEEE、Nature等多种引用格式")
        print("✅ 自动生成内联引用和参考文献列表")
        print("✅ 智能引用键生成和管理")
        print("✅ BibTeX格式导出支持")
        print("✅ 引用统计和分析功能")
        print("✅ 与WriterAgent无缝集成")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
