# clients/ads_client.py
#
# NASA ADS (Astrophysics Data System) API客户端
# 用于搜索和获取学术论文信息

import requests
import time
import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime

from config import config
from models import Paper, SearchResponse, ResponseStatus


class ADSClient:
    """NASA ADS API客户端"""
    
    def __init__(self, api_token: Optional[str] = None):
        """
        初始化ADS客户端
        
        Args:
            api_token: ADS API令牌，如果不提供则从配置中获取
        """
        self.api_token = api_token or config.ADS_API_TOKEN
        self.base_url = config.ADS_BASE_URL
        self.session = requests.Session()
        
        if not self.api_token:
            raise ValueError("ADS API token is required")
        
        # 设置请求头
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        })
        
        self.logger = logging.getLogger(__name__)
    
    def search_papers_for_ranking(self, query: str, sub_question_index: int = 0) -> SearchResponse:
        """
        为PaperRankingAgent优化的论文搜索 - 专注于基础检索，移除复杂排序逻辑

        Args:
            query: 搜索查询字符串
            sub_question_index: 子问题索引（用于日志记录）

        Returns:
            SearchResponse: 搜索响应对象，包含大量原始论文供PaperRankingAgent处理
        """
        start_time = time.time()

        try:
            self.logger.info(f"Bulk retrieval for PaperRankingAgent - sub-question {sub_question_index}: {query}")

            # 直接检索大量论文，不进行复杂排序和筛选
            papers = self._retrieve_papers_bulk_optimized(query, config.ADS_PAPERS_PER_SUBQUESTION)

            if not papers:
                return SearchResponse(
                    query=query,
                    total_results=0,
                    results=[],
                    source="ads",
                    status=ResponseStatus.SUCCESS,
                    search_time=time.time() - start_time
                )

            # 只进行基本去重，保留更多论文供PaperRankingAgent处理
            deduplicated_papers = self._basic_deduplicate_papers(papers)

            search_time = time.time() - start_time

            self.logger.info(f"Retrieved {len(papers)} papers, after basic deduplication: {len(deduplicated_papers)} "
                           f"(ready for PaperRankingAgent processing)")

            return SearchResponse(
                query=query,
                total_results=len(deduplicated_papers),
                results=[paper.to_dict() for paper in deduplicated_papers],
                source="ads",
                status=ResponseStatus.SUCCESS,
                search_time=search_time
            )

        except Exception as e:
            error_msg = f"Error in ADS bulk retrieval for PaperRankingAgent: {str(e)}"
            self.logger.error(error_msg)

            return SearchResponse(
                query=query,
                total_results=0,
                results=[],
                source="ads",
                status=ResponseStatus.ERROR,
                error=error_msg,
                search_time=time.time() - start_time
            )



    def search_papers(self, query: str, max_results: int = None) -> SearchResponse:
        """
        标准论文搜索 - 保持向后兼容性

        Args:
            query: 搜索查询字符串
            max_results: 最大结果数，默认使用配置中的值

        Returns:
            SearchResponse: 搜索响应对象
        """
        if max_results is None:
            max_results = getattr(config, 'ADS_SEARCH_RESULTS_PER_QUERY', 50)

        start_time = time.time()

        try:
            # 构建搜索参数 - 优化字段列表以包含PaperRankingAgent所需的所有元数据
            params = {
                'q': query,
                'fl': 'title,author,abstract,pubdate,pub,doi,bibcode,citation_count,arxiv_class,keyword,property',
                'rows': max_results,
                'sort': 'citation_count desc'  # 按引用次数降序排列
            }

            self.logger.info(f"Standard ADS search for: {query} (max_results: {max_results})")

            # 发送请求
            response = self._make_request('GET', '/search/query', params=params)

            if response.status_code == 200:
                data = response.json()
                papers_data = data.get('response', {}).get('docs', [])

                # 转换为Paper对象
                papers = []
                for paper_data in papers_data:
                    try:
                        paper = self._convert_to_paper_enhanced(paper_data)
                        papers.append(paper)
                    except Exception as e:
                        self.logger.warning(f"Failed to convert paper data: {e}")
                        continue

                search_time = time.time() - start_time

                return SearchResponse(
                    query=query,
                    total_results=len(papers),
                    results=[paper.to_dict() for paper in papers],
                    source="ads",
                    status=ResponseStatus.SUCCESS,
                    search_time=search_time
                )

            else:
                error_msg = f"ADS API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)

                return SearchResponse(
                    query=query,
                    total_results=0,
                    results=[],
                    source="ads",
                    status=ResponseStatus.ERROR,
                    error=error_msg,
                    search_time=time.time() - start_time
                )

        except Exception as e:
            error_msg = f"Error searching ADS: {str(e)}"
            self.logger.error(error_msg)

            return SearchResponse(
                query=query,
                total_results=0,
                results=[],
                source="ads",
                status=ResponseStatus.ERROR,
                error=error_msg,
                search_time=time.time() - start_time
            )
    
    def get_paper_details(self, bibcode: str) -> Optional[Paper]:
        """
        根据bibcode获取论文详细信息
        
        Args:
            bibcode: ADS bibcode
            
        Returns:
            Paper: 论文对象，如果失败返回None
        """
        try:
            params = {
                'q': f'bibcode:{bibcode}',
                'fl': 'title,author,abstract,pubdate,pub,doi,bibcode,citation_count,arxiv_class,keyword'
            }
            
            response = self._make_request('GET', '/search/query', params=params)
            
            if response.status_code == 200:
                data = response.json()
                docs = data.get('response', {}).get('docs', [])
                
                if docs:
                    return self._convert_to_paper(docs[0])
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error getting paper details for {bibcode}: {e}")
            return None
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        url = f"{self.base_url}{endpoint}"
        
        # 添加重试机制
        for attempt in range(config.MAX_RETRIES):
            try:
                response = self.session.request(
                    method, 
                    url, 
                    timeout=config.REQUEST_TIMEOUT,
                    **kwargs
                )
                
                # 如果成功或客户端错误（4xx），不重试
                if response.status_code < 500:
                    return response
                
                # 服务器错误（5xx），等待后重试
                if attempt < config.MAX_RETRIES - 1:
                    wait_time = config.REQUEST_DELAY * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"Server error {response.status_code}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
                
            except requests.exceptions.RequestException as e:
                if attempt < config.MAX_RETRIES - 1:
                    wait_time = config.REQUEST_DELAY * (2 ** attempt)
                    self.logger.warning(f"Request failed: {e}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    raise
        
        return response
    
    def _convert_to_paper(self, paper_data: Dict[str, Any]) -> Paper:
        """
        将ADS API返回的数据转换为Paper对象

        Args:
            paper_data: ADS API返回的论文数据

        Returns:
            Paper: 论文对象
        """
        return self._convert_to_paper_enhanced(paper_data)

    def _convert_to_paper_enhanced(self, paper_data: Dict[str, Any]) -> Paper:
        """
        将ADS API返回的数据转换为Paper对象 - 增强版本，包含PaperRankingAgent所需的所有元数据

        Args:
            paper_data: ADS API返回的论文数据

        Returns:
            Paper: 论文对象，包含完整元数据
        """
        # 处理作者列表
        authors = paper_data.get('author', [])
        if isinstance(authors, list):
            authors = [author.strip() for author in authors if author.strip()]
        else:
            authors = []

        # 处理发表日期 - 增强处理
        pubdate = paper_data.get('pubdate')
        publication_date = None
        publication_date_str = None
        if pubdate:
            try:
                # ADS日期格式通常是 "YYYY-MM-00" 或 "YYYY-MM"
                if isinstance(pubdate, str):
                    publication_date_str = pubdate
                    # 移除 "-00" 部分并补充为 "-01"
                    if pubdate.endswith('-00'):
                        pubdate = pubdate.replace('-00', '-01')
                    elif len(pubdate) == 7:  # YYYY-MM 格式
                        pubdate += '-01'

                    # 尝试解析日期
                    for date_format in ['%Y-%m-%d', '%Y-%m', '%Y']:
                        try:
                            publication_date = datetime.strptime(pubdate, date_format)
                            break
                        except ValueError:
                            continue
            except Exception as e:
                self.logger.debug(f"Date parsing failed for {pubdate}: {e}")

        # 提取arXiv ID - 增强处理
        arxiv_id = None
        bibcode = paper_data.get('bibcode', '')
        if 'arXiv' in bibcode:
            try:
                arxiv_id = bibcode.split('arXiv:')[-1].split('v')[0] if 'arXiv:' in bibcode else None
            except:
                pass

        # 处理期刊信息 - 增强处理
        journal = paper_data.get('pub', '')
        if isinstance(journal, list) and journal:
            journal = journal[0]

        # 处理引用数 - 确保为整数
        citation_count = paper_data.get('citation_count', 0)
        if citation_count is None:
            citation_count = 0

        # 处理DOI
        doi = paper_data.get('doi')
        if isinstance(doi, list) and doi:
            doi = doi[0]
        elif not doi:
            doi = None

        # 处理标题
        title = paper_data.get('title', [''])
        if isinstance(title, list) and title:
            title = title[0]
        elif not title:
            title = ''

        # 处理摘要
        abstract = paper_data.get('abstract', '')
        if not abstract:
            abstract = ''

        # 确定最终的发表日期字符串
        final_publication_date = None
        if publication_date_str:
            final_publication_date = publication_date_str
        elif publication_date:
            final_publication_date = publication_date.strftime('%Y-%m-%d')

        return Paper(
            title=title,
            authors=authors,
            abstract=abstract,
            publication_date=final_publication_date,
            journal=journal,
            doi=doi,
            ads_bibcode=bibcode,
            arxiv_id=arxiv_id,
            citation_count=int(citation_count),
            source='ads'
        )
    
    def build_search_query(self, keywords: List[str], field: str = 'all') -> str:
        """
        构建ADS搜索查询字符串
        
        Args:
            keywords: 关键词列表
            field: 搜索字段 ('all', 'title', 'abstract', 'author')
            
        Returns:
            str: 搜索查询字符串
        """
        if not keywords:
            return ''
        
        # 对关键词进行引号包装以进行精确匹配
        quoted_keywords = [f'"{keyword}"' for keyword in keywords]
        
        if field == 'all':
            # 在所有字段中搜索
            return ' OR '.join(quoted_keywords)
        else:
            # 在特定字段中搜索
            return ' OR '.join([f'{field}:{keyword}' for keyword in quoted_keywords])
    
    def test_connection(self) -> bool:
        """
        测试ADS API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            response = self._make_request('GET', '/search/query', params={'q': 'star', 'rows': 1})
            return response.status_code == 200
        except:
            return False

    def _retrieve_papers_bulk(self, query: str, count: int) -> List[Paper]:
        """
        批量检索论文

        Args:
            query: 搜索查询
            count: 要检索的论文数量

        Returns:
            List[Paper]: 论文列表
        """
        return self._retrieve_papers_bulk_optimized(query, count)

    def _retrieve_papers_bulk_optimized(self, query: str, count: int) -> List[Paper]:
        """
        为PaperRankingAgent优化的批量检索论文方法

        Args:
            query: 搜索查询
            count: 要检索的论文数量

        Returns:
            List[Paper]: 论文列表，包含完整元数据
        """
        try:
            # 优化的参数配置 - 包含PaperRankingAgent所需的所有字段
            params = {
                'q': query,
                'fl': 'title,author,abstract,pubdate,pub,doi,bibcode,citation_count,arxiv_class,keyword,property',
                'rows': count,
                'sort': 'score desc'  # 使用ADS相关性评分，而非简单的日期排序
            }

            self.logger.info(f"Optimized bulk retrieval: {count} papers for query: {query[:100]}...")

            response = self._make_request('GET', '/search/query', params=params)

            if response.status_code == 200:
                data = response.json()
                papers_data = data.get('response', {}).get('docs', [])

                self.logger.info(f"ADS returned {len(papers_data)} papers")

                papers = []
                for paper_data in papers_data:
                    try:
                        paper = self._convert_to_paper_enhanced(paper_data)
                        # 基本质量筛选 - 只保留有标题的论文
                        if paper.title and len(paper.title.strip()) > 10:
                            papers.append(paper)
                    except Exception as e:
                        self.logger.debug(f"Failed to convert paper data: {e}")
                        continue

                self.logger.info(f"Successfully converted {len(papers)} papers with quality filtering")
                return papers

            else:
                self.logger.error(f"ADS API error: {response.status_code} - {response.text}")
                return []

        except Exception as e:
            self.logger.error(f"Error in optimized bulk paper retrieval: {e}")
            return []



    def _deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """
        去重论文，基于标题相似性

        Args:
            papers: 论文列表

        Returns:
            List[Paper]: 去重后的论文列表
        """
        return self._basic_deduplicate_papers(papers)

    def _basic_deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """
        基础去重论文 - 为PaperRankingAgent优化，保留更多论文

        Args:
            papers: 论文列表

        Returns:
            List[Paper]: 去重后的论文列表
        """
        if not papers:
            return []

        deduplicated = []
        seen_bibcodes = set()
        seen_titles = set()

        for paper in papers:
            # 首先基于bibcode去重（最准确）
            if paper.ads_bibcode and paper.ads_bibcode in seen_bibcodes:
                continue

            # 然后基于标题去重（更宽松的标准）
            normalized_title = self._normalize_title_basic(paper.title)
            if normalized_title in seen_titles:
                continue

            # 添加到结果
            deduplicated.append(paper)
            if paper.ads_bibcode:
                seen_bibcodes.add(paper.ads_bibcode)
            seen_titles.add(normalized_title)

        if hasattr(self, 'logger') and self.logger:
            self.logger.info(f"Basic deduplication: {len(papers)} → {len(deduplicated)} papers")
        return deduplicated

    def _normalize_title(self, title: str) -> str:
        """
        标准化论文标题

        Args:
            title: 原始标题

        Returns:
            str: 标准化后的标题
        """
        return self._normalize_title_basic(title)

    def _normalize_title_basic(self, title: str) -> str:
        """
        基础标题标准化 - 为PaperRankingAgent优化，更宽松的去重标准

        Args:
            title: 原始标题

        Returns:
            str: 标准化后的标题
        """
        if not title:
            return ""

        # 转换为小写
        normalized = title.lower().strip()

        # 移除常见的标点符号，但保留更多结构
        normalized = re.sub(r'[^\w\s\-]', ' ', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        # 移除常见的前缀/后缀词汇以提高去重效果
        common_prefixes = ['a ', 'an ', 'the ']
        for prefix in common_prefixes:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):]
                break

        return normalized

    def _titles_are_similar(self, title1: str, title2: str, threshold: float = 0.8) -> bool:
        """
        检查两个标题是否相似

        Args:
            title1: 标题1
            title2: 标题2
            threshold: 相似度阈值

        Returns:
            bool: 是否相似
        """
        # 简单的基于词汇重叠的相似度计算
        words1 = set(title1.split())
        words2 = set(title2.split())

        if not words1 or not words2:
            return False

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
