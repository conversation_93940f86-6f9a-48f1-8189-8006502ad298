# clients/ads_client.py
#
# NASA ADS (Astrophysics Data System) API客户端
# 用于搜索和获取学术论文信息

import requests
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from config import config
from models import Paper, SearchResponse, ResponseStatus


class ADSClient:
    """NASA ADS API客户端"""
    
    def __init__(self, api_token: Optional[str] = None):
        """
        初始化ADS客户端
        
        Args:
            api_token: ADS API令牌，如果不提供则从配置中获取
        """
        self.api_token = api_token or config.ADS_API_TOKEN
        self.base_url = config.ADS_BASE_URL
        self.session = requests.Session()
        
        if not self.api_token:
            raise ValueError("ADS API token is required")
        
        # 设置请求头
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        })
        
        self.logger = logging.getLogger(__name__)
    
    def search_papers(self, query: str, max_results: int = None) -> SearchResponse:
        """
        搜索论文
        
        Args:
            query: 搜索查询字符串
            max_results: 最大结果数，默认使用配置中的值
            
        Returns:
            SearchResponse: 搜索响应对象
        """
        if max_results is None:
            max_results = config.ADS_SEARCH_RESULTS_PER_QUERY
        
        start_time = time.time()
        
        try:
            # 构建搜索参数
            params = {
                'q': query,
                'fl': 'title,author,abstract,pubdate,pub,doi,bibcode,citation_count,arxiv_class',
                'rows': max_results,
                'sort': 'citation_count desc'  # 按引用次数降序排列
            }
            
            self.logger.info(f"Searching ADS for: {query}")
            
            # 发送请求
            response = self._make_request('GET', '/search/query', params=params)
            
            if response.status_code == 200:
                data = response.json()
                papers_data = data.get('response', {}).get('docs', [])
                
                # 转换为Paper对象
                papers = []
                for paper_data in papers_data:
                    try:
                        paper = self._convert_to_paper(paper_data)
                        papers.append(paper)
                    except Exception as e:
                        self.logger.warning(f"Failed to convert paper data: {e}")
                        continue
                
                search_time = time.time() - start_time
                
                return SearchResponse(
                    query=query,
                    total_results=len(papers),
                    results=[paper.to_dict() for paper in papers],
                    source="ads",
                    status=ResponseStatus.SUCCESS,
                    search_time=search_time
                )
            
            else:
                error_msg = f"ADS API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                
                return SearchResponse(
                    query=query,
                    total_results=0,
                    results=[],
                    source="ads",
                    status=ResponseStatus.ERROR,
                    error=error_msg,
                    search_time=time.time() - start_time
                )
        
        except Exception as e:
            error_msg = f"Error searching ADS: {str(e)}"
            self.logger.error(error_msg)
            
            return SearchResponse(
                query=query,
                total_results=0,
                results=[],
                source="ads",
                status=ResponseStatus.ERROR,
                error=error_msg,
                search_time=time.time() - start_time
            )
    
    def get_paper_details(self, bibcode: str) -> Optional[Paper]:
        """
        根据bibcode获取论文详细信息
        
        Args:
            bibcode: ADS bibcode
            
        Returns:
            Paper: 论文对象，如果失败返回None
        """
        try:
            params = {
                'q': f'bibcode:{bibcode}',
                'fl': 'title,author,abstract,pubdate,pub,doi,bibcode,citation_count,arxiv_class,keyword'
            }
            
            response = self._make_request('GET', '/search/query', params=params)
            
            if response.status_code == 200:
                data = response.json()
                docs = data.get('response', {}).get('docs', [])
                
                if docs:
                    return self._convert_to_paper(docs[0])
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error getting paper details for {bibcode}: {e}")
            return None
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        url = f"{self.base_url}{endpoint}"
        
        # 添加重试机制
        for attempt in range(config.MAX_RETRIES):
            try:
                response = self.session.request(
                    method, 
                    url, 
                    timeout=config.REQUEST_TIMEOUT,
                    **kwargs
                )
                
                # 如果成功或客户端错误（4xx），不重试
                if response.status_code < 500:
                    return response
                
                # 服务器错误（5xx），等待后重试
                if attempt < config.MAX_RETRIES - 1:
                    wait_time = config.REQUEST_DELAY * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"Server error {response.status_code}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
                
            except requests.exceptions.RequestException as e:
                if attempt < config.MAX_RETRIES - 1:
                    wait_time = config.REQUEST_DELAY * (2 ** attempt)
                    self.logger.warning(f"Request failed: {e}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    raise
        
        return response
    
    def _convert_to_paper(self, paper_data: Dict[str, Any]) -> Paper:
        """
        将ADS API返回的数据转换为Paper对象
        
        Args:
            paper_data: ADS API返回的论文数据
            
        Returns:
            Paper: 论文对象
        """
        # 处理作者列表
        authors = paper_data.get('author', [])
        if isinstance(authors, list):
            authors = [author.strip() for author in authors if author.strip()]
        else:
            authors = []
        
        # 处理发表日期
        pubdate = paper_data.get('pubdate')
        publication_date = None
        if pubdate:
            try:
                # ADS日期格式通常是 "YYYY-MM-00"
                if isinstance(pubdate, str):
                    # 移除 "-00" 部分
                    pubdate = pubdate.replace('-00', '-01')
                    publication_date = datetime.strptime(pubdate, '%Y-%m-%d')
            except:
                pass
        
        # 提取arXiv ID
        arxiv_id = None
        if 'arxiv_class' in paper_data:
            arxiv_classes = paper_data.get('arxiv_class', [])
            if arxiv_classes and isinstance(arxiv_classes, list):
                # arXiv ID通常在bibcode中
                bibcode = paper_data.get('bibcode', '')
                if 'arXiv' in bibcode:
                    arxiv_id = bibcode.split('arXiv:')[-1].split('v')[0] if 'arXiv:' in bibcode else None
        
        return Paper(
            title=paper_data.get('title', [''])[0] if paper_data.get('title') else '',
            authors=authors,
            abstract=paper_data.get('abstract', ''),
            publication_date=publication_date,
            journal=paper_data.get('pub', ''),
            doi=paper_data.get('doi', [''])[0] if paper_data.get('doi') else None,
            ads_bibcode=paper_data.get('bibcode', ''),
            arxiv_id=arxiv_id,
            citation_count=paper_data.get('citation_count', 0),
            source='ads'
        )
    
    def build_search_query(self, keywords: List[str], field: str = 'all') -> str:
        """
        构建ADS搜索查询字符串
        
        Args:
            keywords: 关键词列表
            field: 搜索字段 ('all', 'title', 'abstract', 'author')
            
        Returns:
            str: 搜索查询字符串
        """
        if not keywords:
            return ''
        
        # 对关键词进行引号包装以进行精确匹配
        quoted_keywords = [f'"{keyword}"' for keyword in keywords]
        
        if field == 'all':
            # 在所有字段中搜索
            return ' OR '.join(quoted_keywords)
        else:
            # 在特定字段中搜索
            return ' OR '.join([f'{field}:{keyword}' for keyword in quoted_keywords])
    
    def test_connection(self) -> bool:
        """
        测试ADS API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            response = self._make_request('GET', '/search/query', params={'q': 'star', 'rows': 1})
            return response.status_code == 200
        except:
            return False
