# clients/llm_client.py
#
# 大语言模型(LLM)客户端
# 支持 OpenAI 和 Anthropic API

import time
import logging
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

from config import config
from models import LLMResponse, ResponseStatus

# 尝试导入LLM客户端库
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False


class BaseLLMClient(ABC):
    """LLM客户端基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应方法，子类必须实现"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """测试连接方法，子类必须实现"""
        pass


class OpenAIClient(BaseLLMClient):
    """OpenAI API客户端"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None, base_url: Optional[str] = None):
        super().__init__()

        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI library not available. Install with: pip install openai")

        self.api_key = api_key or config.OPENAI_API_KEY
        self.model = model or config.OPENAI_MODEL
        self.base_url = base_url or getattr(config, 'OPENAI_BASE_URL', None)

        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        # 初始化OpenAI客户端，支持自定义base_url
        client_kwargs = {'api_key': self.api_key}
        if self.base_url:
            client_kwargs['base_url'] = self.base_url
            self.logger.info(f"Using custom OpenAI-compatible endpoint: {self.base_url}")

        self.client = openai.OpenAI(**client_kwargs)
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        使用OpenAI API生成响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: LLM响应对象
        """
        start_time = time.time()
        
        # 设置默认参数
        params = {
            'model': self.model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': kwargs.get('temperature', config.DEFAULT_LLM_TEMPERATURE),
            'max_tokens': kwargs.get('max_tokens', config.DEFAULT_LLM_MAX_TOKENS)
        }
        
        try:
            self.logger.info(f"Generating response with OpenAI {self.model}")
            
            response = self.client.chat.completions.create(**params)
            
            response_time = time.time() - start_time
            
            # 提取响应内容
            content = response.choices[0].message.content
            
            # 提取使用统计
            usage = response.usage
            
            return LLMResponse(
                prompt=prompt,
                response=content,
                model=self.model,
                provider="openai",
                status=ResponseStatus.SUCCESS,
                prompt_tokens=usage.prompt_tokens if usage else None,
                completion_tokens=usage.completion_tokens if usage else None,
                total_tokens=usage.total_tokens if usage else None,
                response_time=response_time
            )
        
        except Exception as e:
            error_msg = f"OpenAI API error: {str(e)}"
            self.logger.error(error_msg)
            
            return LLMResponse(
                prompt=prompt,
                response=None,
                model=self.model,
                provider="openai",
                status=ResponseStatus.ERROR,
                error=error_msg,
                response_time=time.time() - start_time
            )
    
    def test_connection(self) -> bool:
        """测试OpenAI API连接"""
        try:
            response = self.generate("Hello", max_tokens=5)
            return response.is_successful()
        except:
            return False


class OpenAICompatibleClient(OpenAIClient):
    """OpenAI兼容API客户端 - 支持本地部署和第三方兼容服务"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None,
                 base_url: Optional[str] = None, provider_name: str = "openai-compatible"):
        """
        初始化OpenAI兼容客户端

        Args:
            api_key: API密钥
            model: 模型名称
            base_url: 自定义API端点URL
            provider_name: 提供商名称（用于日志标识）
        """
        # 如果没有提供base_url，从配置中获取
        if not base_url:
            base_url = getattr(config, 'OPENAI_COMPATIBLE_BASE_URL', None)

        if not base_url:
            raise ValueError("OpenAI compatible base URL is required")

        # 使用默认API密钥（某些本地服务可能不需要真实密钥）
        if not api_key:
            api_key = getattr(config, 'OPENAI_COMPATIBLE_API_KEY', 'dummy-key')

        # 使用默认模型名称
        if not model:
            model = getattr(config, 'OPENAI_COMPATIBLE_MODEL', 'gpt-3.5-turbo')

        super().__init__(api_key=api_key, model=model, base_url=base_url)
        self.provider_name = provider_name

        self.logger.info(f"Initialized {provider_name} client with endpoint: {base_url}")

    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        使用OpenAI兼容API生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLMResponse: LLM响应对象
        """
        start_time = time.time()

        try:
            self.logger.info(f"Generating response with {self.provider_name} {self.model}")

            # 调用父类的generate方法，但修改provider标识
            response = super().generate(prompt, **kwargs)

            # 更新provider信息
            response.provider = self.provider_name

            return response

        except Exception as e:
            error_msg = f"{self.provider_name} API error: {str(e)}"
            self.logger.error(error_msg)

            return LLMResponse(
                prompt=prompt,
                response=None,
                model=self.model,
                provider=self.provider_name,
                status=ResponseStatus.ERROR,
                error=error_msg,
                response_time=time.time() - start_time
            )

    def test_connection(self) -> bool:
        """测试OpenAI兼容API连接"""
        try:
            response = self.generate("Hello", max_tokens=5)
            return response.is_successful()
        except:
            return False


class AnthropicClient(BaseLLMClient):
    """Anthropic API客户端"""
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        super().__init__()
        
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic library not available. Install with: pip install anthropic")
        
        self.api_key = api_key or config.ANTHROPIC_API_KEY
        self.model = model or config.ANTHROPIC_MODEL
        
        if not self.api_key:
            raise ValueError("Anthropic API key is required")
        
        # 初始化Anthropic客户端
        self.client = anthropic.Anthropic(api_key=self.api_key)
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        使用Anthropic API生成响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: LLM响应对象
        """
        start_time = time.time()
        
        # 设置默认参数
        params = {
            'model': self.model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': kwargs.get('temperature', config.DEFAULT_LLM_TEMPERATURE),
            'max_tokens': kwargs.get('max_tokens', config.DEFAULT_LLM_MAX_TOKENS)
        }
        
        try:
            self.logger.info(f"Generating response with Anthropic {self.model}")
            
            response = self.client.messages.create(**params)
            
            response_time = time.time() - start_time
            
            # 提取响应内容
            content = response.content[0].text if response.content else ""
            
            # 提取使用统计
            usage = response.usage
            
            return LLMResponse(
                prompt=prompt,
                response=content,
                model=self.model,
                provider="anthropic",
                status=ResponseStatus.SUCCESS,
                prompt_tokens=usage.input_tokens if usage else None,
                completion_tokens=usage.output_tokens if usage else None,
                total_tokens=(usage.input_tokens + usage.output_tokens) if usage else None,
                response_time=response_time
            )
        
        except Exception as e:
            error_msg = f"Anthropic API error: {str(e)}"
            self.logger.error(error_msg)
            
            return LLMResponse(
                prompt=prompt,
                response=None,
                model=self.model,
                provider="anthropic",
                status=ResponseStatus.ERROR,
                error=error_msg,
                response_time=time.time() - start_time
            )
    
    def test_connection(self) -> bool:
        """测试Anthropic API连接"""
        try:
            response = self.generate("Hello", max_tokens=5)
            return response.is_successful()
        except:
            return False


class GeminiClient(BaseLLMClient):
    """Google Gemini API客户端"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        super().__init__()

        if not GEMINI_AVAILABLE:
            raise ImportError("Google Generative AI library not available. Install with: pip install google-generativeai")

        self.api_key = api_key or config.GEMINI_API_KEY
        self.model_name = model or config.GEMINI_MODEL

        if not self.api_key:
            raise ValueError("Gemini API key is required")

        # 配置Gemini API
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(self.model_name)

    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        使用Gemini API生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLMResponse: LLM响应对象
        """
        start_time = time.time()

        # 设置生成配置
        generation_config = genai.types.GenerationConfig(
            temperature=kwargs.get('temperature', config.DEFAULT_LLM_TEMPERATURE),
            max_output_tokens=kwargs.get('max_tokens', config.DEFAULT_LLM_MAX_TOKENS),
        )

        try:
            self.logger.info(f"Generating response with Gemini {self.model_name}")

            response = self.model.generate_content(
                prompt,
                generation_config=generation_config
            )

            response_time = time.time() - start_time

            # 提取响应内容
            content = response.text if response.text else ""

            # Gemini不提供详细的token使用统计，使用估算
            estimated_prompt_tokens = len(prompt.split()) * 1.3  # 粗略估算
            estimated_completion_tokens = len(content.split()) * 1.3

            return LLMResponse(
                prompt=prompt,
                response=content,
                model=self.model_name,
                provider="gemini",
                status=ResponseStatus.SUCCESS,
                prompt_tokens=int(estimated_prompt_tokens),
                completion_tokens=int(estimated_completion_tokens),
                total_tokens=int(estimated_prompt_tokens + estimated_completion_tokens),
                response_time=response_time
            )

        except Exception as e:
            error_msg = f"Gemini API error: {str(e)}"
            self.logger.error(error_msg)

            return LLMResponse(
                prompt=prompt,
                response=None,
                model=self.model_name,
                provider="gemini",
                status=ResponseStatus.ERROR,
                error=error_msg,
                response_time=time.time() - start_time
            )

    def test_connection(self) -> bool:
        """测试Gemini API连接"""
        try:
            response = self.generate("Hello", max_tokens=5)
            return response.is_successful()
        except:
            return False


class LLMClient:
    """统一的LLM客户端"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        
        # 根据配置选择LLM提供商
        if config.DEFAULT_LLM_PROVIDER == "openai" and config.OPENAI_API_KEY:
            try:
                self.client = OpenAIClient()
                self.logger.info("Using OpenAI API")
            except Exception as e:
                self.logger.error(f"Failed to initialize OpenAI client: {e}")

        elif config.DEFAULT_LLM_PROVIDER == "openai-compatible":
            try:
                self.client = OpenAICompatibleClient()
                self.logger.info("Using OpenAI-compatible API")
            except Exception as e:
                self.logger.error(f"Failed to initialize OpenAI-compatible client: {e}")

        elif config.DEFAULT_LLM_PROVIDER == "anthropic" and config.ANTHROPIC_API_KEY:
            try:
                self.client = AnthropicClient()
                self.logger.info("Using Anthropic API")
            except Exception as e:
                self.logger.error(f"Failed to initialize Anthropic client: {e}")

        elif config.DEFAULT_LLM_PROVIDER == "gemini" and config.GEMINI_API_KEY:
            try:
                self.client = GeminiClient()
                self.logger.info("Using Gemini API")
            except Exception as e:
                self.logger.error(f"Failed to initialize Gemini client: {e}")

        if not self.client:
            raise ValueError(f"No LLM client available for provider: {config.DEFAULT_LLM_PROVIDER}")

    def create_client_for_agent(self, agent_type: str):
        """为特定代理创建LLM客户端"""
        agent_config = config.get_agent_config(agent_type)
        provider = agent_config.get("provider", config.DEFAULT_LLM_PROVIDER)
        model = agent_config.get("model")

        # 确保模型配置正确传递
        if not model:
            model = config.get_model_for_provider(provider)

        try:
            if provider == "openai" and config.OPENAI_API_KEY:
                return OpenAIClient(model=model)
            elif provider == "openai-compatible":
                return OpenAICompatibleClient(model=model)
            elif provider == "anthropic" and config.ANTHROPIC_API_KEY:
                return AnthropicClient(model=model)
            elif provider == "gemini" and config.GEMINI_API_KEY:
                return GeminiClient(model=model)
            else:
                self.logger.warning(f"Provider {provider} not available, using default client")
                return self.client
        except Exception as e:
            self.logger.error(f"Failed to create client for {agent_type}: {e}")
            return self.client
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        生成LLM响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: LLM响应对象
        """
        if not self.client:
            return LLMResponse(
                prompt=prompt,
                response=None,
                model="none",
                provider="none",
                status=ResponseStatus.ERROR,
                error="No LLM client available"
            )
        
        # 添加请求延迟以避免API限流
        time.sleep(config.REQUEST_DELAY)
        
        return self.client.generate(prompt, **kwargs)
    
    def test_connection(self) -> bool:
        """测试LLM连接"""
        if not self.client:
            return False
        return self.client.test_connection()
