# clients/llm_client.py
#
# 大语言模型(LLM)客户端
# 支持 OpenAI 和 Anthropic API

import time
import logging
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

from config import config
from models import LLMResponse, ResponseStatus

# 尝试导入LLM客户端库
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False


class BaseLLMClient(ABC):
    """LLM客户端基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应方法，子类必须实现"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """测试连接方法，子类必须实现"""
        pass


class OpenAIClient(BaseLLMClient):
    """OpenAI API客户端"""
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        super().__init__()
        
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI library not available. Install with: pip install openai")
        
        self.api_key = api_key or config.OPENAI_API_KEY
        self.model = model or config.OPENAI_MODEL
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(api_key=self.api_key)
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        使用OpenAI API生成响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: LLM响应对象
        """
        start_time = time.time()
        
        # 设置默认参数
        params = {
            'model': self.model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': kwargs.get('temperature', config.LLM_TEMPERATURE),
            'max_tokens': kwargs.get('max_tokens', config.LLM_MAX_TOKENS)
        }
        
        try:
            self.logger.info(f"Generating response with OpenAI {self.model}")
            
            response = self.client.chat.completions.create(**params)
            
            response_time = time.time() - start_time
            
            # 提取响应内容
            content = response.choices[0].message.content
            
            # 提取使用统计
            usage = response.usage
            
            return LLMResponse(
                prompt=prompt,
                response=content,
                model=self.model,
                provider="openai",
                status=ResponseStatus.SUCCESS,
                prompt_tokens=usage.prompt_tokens if usage else None,
                completion_tokens=usage.completion_tokens if usage else None,
                total_tokens=usage.total_tokens if usage else None,
                response_time=response_time
            )
        
        except Exception as e:
            error_msg = f"OpenAI API error: {str(e)}"
            self.logger.error(error_msg)
            
            return LLMResponse(
                prompt=prompt,
                response=None,
                model=self.model,
                provider="openai",
                status=ResponseStatus.ERROR,
                error=error_msg,
                response_time=time.time() - start_time
            )
    
    def test_connection(self) -> bool:
        """测试OpenAI API连接"""
        try:
            response = self.generate("Hello", max_tokens=5)
            return response.is_successful()
        except:
            return False


class AnthropicClient(BaseLLMClient):
    """Anthropic API客户端"""
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        super().__init__()
        
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic library not available. Install with: pip install anthropic")
        
        self.api_key = api_key or config.ANTHROPIC_API_KEY
        self.model = model or config.ANTHROPIC_MODEL
        
        if not self.api_key:
            raise ValueError("Anthropic API key is required")
        
        # 初始化Anthropic客户端
        self.client = anthropic.Anthropic(api_key=self.api_key)
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        使用Anthropic API生成响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: LLM响应对象
        """
        start_time = time.time()
        
        # 设置默认参数
        params = {
            'model': self.model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': kwargs.get('temperature', config.LLM_TEMPERATURE),
            'max_tokens': kwargs.get('max_tokens', config.LLM_MAX_TOKENS)
        }
        
        try:
            self.logger.info(f"Generating response with Anthropic {self.model}")
            
            response = self.client.messages.create(**params)
            
            response_time = time.time() - start_time
            
            # 提取响应内容
            content = response.content[0].text if response.content else ""
            
            # 提取使用统计
            usage = response.usage
            
            return LLMResponse(
                prompt=prompt,
                response=content,
                model=self.model,
                provider="anthropic",
                status=ResponseStatus.SUCCESS,
                prompt_tokens=usage.input_tokens if usage else None,
                completion_tokens=usage.output_tokens if usage else None,
                total_tokens=(usage.input_tokens + usage.output_tokens) if usage else None,
                response_time=response_time
            )
        
        except Exception as e:
            error_msg = f"Anthropic API error: {str(e)}"
            self.logger.error(error_msg)
            
            return LLMResponse(
                prompt=prompt,
                response=None,
                model=self.model,
                provider="anthropic",
                status=ResponseStatus.ERROR,
                error=error_msg,
                response_time=time.time() - start_time
            )
    
    def test_connection(self) -> bool:
        """测试Anthropic API连接"""
        try:
            response = self.generate("Hello", max_tokens=5)
            return response.is_successful()
        except:
            return False


class LLMClient:
    """统一的LLM客户端"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        
        # 根据配置选择LLM提供商
        if config.LLM_PROVIDER == "openai" and config.OPENAI_API_KEY:
            try:
                self.client = OpenAIClient()
                self.logger.info("Using OpenAI API")
            except Exception as e:
                self.logger.error(f"Failed to initialize OpenAI client: {e}")
        
        elif config.LLM_PROVIDER == "anthropic" and config.ANTHROPIC_API_KEY:
            try:
                self.client = AnthropicClient()
                self.logger.info("Using Anthropic API")
            except Exception as e:
                self.logger.error(f"Failed to initialize Anthropic client: {e}")
        
        if not self.client:
            raise ValueError(f"No LLM client available for provider: {config.LLM_PROVIDER}")
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """
        生成LLM响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: LLM响应对象
        """
        if not self.client:
            return LLMResponse(
                prompt=prompt,
                response=None,
                model="none",
                provider="none",
                status=ResponseStatus.ERROR,
                error="No LLM client available"
            )
        
        # 添加请求延迟以避免API限流
        time.sleep(config.REQUEST_DELAY)
        
        return self.client.generate(prompt, **kwargs)
    
    def test_connection(self) -> bool:
        """测试LLM连接"""
        if not self.client:
            return False
        return self.client.test_connection()
