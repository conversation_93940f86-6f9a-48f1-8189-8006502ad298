# clients/web_search_client.py
#
# 网络搜索API客户端
# 支持 Tavily Search API 和 Google Custom Search API

import requests
import time
import logging
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

from config import config
from models import WebSearchResult, SearchResponse, ResponseStatus


class BaseSearchClient(ABC):
    """搜索客户端基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def search(self, query: str, max_results: int = None) -> SearchResponse:
        """搜索方法，子类必须实现"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """测试连接方法，子类必须实现"""
        pass


class TavilySearchClient(BaseSearchClient):
    """Tavily Search API客户端"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__()
        self.api_key = api_key or config.TAVILY_API_KEY
        self.base_url = "https://api.tavily.com"
        
        if not self.api_key:
            raise ValueError("Tavily API key is required")
    
    def search(self, query: str, max_results: int = None) -> SearchResponse:
        """
        使用Tavily API搜索
        
        Args:
            query: 搜索查询
            max_results: 最大结果数
            
        Returns:
            SearchResponse: 搜索响应
        """
        if max_results is None:
            max_results = config.WEB_SEARCH_RESULTS_PER_QUERY
        
        start_time = time.time()
        
        try:
            payload = {
                "api_key": self.api_key,
                "query": query,
                "search_depth": "advanced",
                "include_answer": False,
                "include_images": False,
                "include_raw_content": False,
                "max_results": max_results
            }
            
            self.logger.info(f"Searching Tavily for: {query}")
            
            response = requests.post(
                f"{self.base_url}/search",
                json=payload,
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # 转换为WebSearchResult对象
                web_results = []
                for result in results:
                    try:
                        web_result = WebSearchResult(
                            title=result.get('title', ''),
                            url=result.get('url', ''),
                            snippet=result.get('content', ''),
                            source='tavily'
                        )
                        web_results.append(web_result)
                    except Exception as e:
                        self.logger.warning(f"Failed to convert search result: {e}")
                        continue
                
                search_time = time.time() - start_time
                
                return SearchResponse(
                    query=query,
                    total_results=len(web_results),
                    results=[result.to_dict() for result in web_results],
                    source="tavily",
                    status=ResponseStatus.SUCCESS,
                    search_time=search_time
                )
            
            else:
                error_msg = f"Tavily API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                
                return SearchResponse(
                    query=query,
                    total_results=0,
                    results=[],
                    source="tavily",
                    status=ResponseStatus.ERROR,
                    error=error_msg,
                    search_time=time.time() - start_time
                )
        
        except Exception as e:
            error_msg = f"Error searching Tavily: {str(e)}"
            self.logger.error(error_msg)
            
            return SearchResponse(
                query=query,
                total_results=0,
                results=[],
                source="tavily",
                status=ResponseStatus.ERROR,
                error=error_msg,
                search_time=time.time() - start_time
            )
    
    def test_connection(self) -> bool:
        """测试Tavily API连接"""
        try:
            response = self.search("test", max_results=1)
            return response.is_successful()
        except:
            return False


class GoogleSearchClient(BaseSearchClient):
    """Google Custom Search API客户端"""
    
    def __init__(self, api_key: Optional[str] = None, cse_id: Optional[str] = None):
        super().__init__()
        self.api_key = api_key or config.GOOGLE_SEARCH_API_KEY
        self.cse_id = cse_id or config.GOOGLE_CSE_ID
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        
        if not self.api_key or not self.cse_id:
            raise ValueError("Google Search API key and CSE ID are required")
    
    def search(self, query: str, max_results: int = None) -> SearchResponse:
        """
        使用Google Custom Search API搜索
        
        Args:
            query: 搜索查询
            max_results: 最大结果数
            
        Returns:
            SearchResponse: 搜索响应
        """
        if max_results is None:
            max_results = config.WEB_SEARCH_RESULTS_PER_QUERY
        
        start_time = time.time()
        
        try:
            params = {
                'key': self.api_key,
                'cx': self.cse_id,
                'q': query,
                'num': min(max_results, 10)  # Google API最多返回10个结果
            }
            
            self.logger.info(f"Searching Google for: {query}")
            
            response = requests.get(
                self.base_url,
                params=params,
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])
                
                # 转换为WebSearchResult对象
                web_results = []
                for item in items:
                    try:
                        web_result = WebSearchResult(
                            title=item.get('title', ''),
                            url=item.get('link', ''),
                            snippet=item.get('snippet', ''),
                            source='google'
                        )
                        web_results.append(web_result)
                    except Exception as e:
                        self.logger.warning(f"Failed to convert search result: {e}")
                        continue
                
                search_time = time.time() - start_time
                
                return SearchResponse(
                    query=query,
                    total_results=len(web_results),
                    results=[result.to_dict() for result in web_results],
                    source="google",
                    status=ResponseStatus.SUCCESS,
                    search_time=search_time
                )
            
            else:
                error_msg = f"Google API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                
                return SearchResponse(
                    query=query,
                    total_results=0,
                    results=[],
                    source="google",
                    status=ResponseStatus.ERROR,
                    error=error_msg,
                    search_time=time.time() - start_time
                )
        
        except Exception as e:
            error_msg = f"Error searching Google: {str(e)}"
            self.logger.error(error_msg)
            
            return SearchResponse(
                query=query,
                total_results=0,
                results=[],
                source="google",
                status=ResponseStatus.ERROR,
                error=error_msg,
                search_time=time.time() - start_time
            )
    
    def test_connection(self) -> bool:
        """测试Google API连接"""
        try:
            response = self.search("test", max_results=1)
            return response.is_successful()
        except:
            return False


class WebSearchClient:
    """统一的网络搜索客户端"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        
        # 根据配置选择搜索提供商
        if config.SEARCH_PROVIDER == "tavily" and config.TAVILY_API_KEY:
            try:
                self.client = TavilySearchClient()
                self.logger.info("Using Tavily Search API")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Tavily client: {e}")
        
        # 如果Tavily不可用，尝试Google
        if not self.client and config.GOOGLE_SEARCH_API_KEY and config.GOOGLE_CSE_ID:
            try:
                self.client = GoogleSearchClient()
                self.logger.info("Using Google Custom Search API")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Google client: {e}")
        
        if not self.client:
            self.logger.warning("No web search client available - web search will be skipped")
    
    def search(self, query: str, max_results: int = None) -> SearchResponse:
        """
        搜索网络内容
        
        Args:
            query: 搜索查询
            max_results: 最大结果数
            
        Returns:
            SearchResponse: 搜索响应
        """
        if not self.client:
            return SearchResponse(
                query=query,
                total_results=0,
                results=[],
                source="none",
                status=ResponseStatus.ERROR,
                error="No web search client available"
            )
        
        # 添加请求延迟以避免API限流
        time.sleep(config.REQUEST_DELAY)
        
        return self.client.search(query, max_results)
    
    def is_available(self) -> bool:
        """检查搜索客户端是否可用"""
        return self.client is not None
    
    def test_connection(self) -> bool:
        """测试搜索连接"""
        if not self.client:
            return False
        return self.client.test_connection()
