#!/usr/bin/env python3
# comprehensive_agent_check.py
#
# 全面检查SynthesizerAgent和WriterAgent是否存在与PlannerAgent相同的问题

import sys
import os
import re
import ast
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def check_tuple_unpacking_patterns(file_path: str) -> dict:
    """检查文件中的元组解包模式"""
    print(f"🔍 Checking tuple unpacking patterns in {file_path}")
    
    results = {
        "file": file_path,
        "unpacking_patterns": [],
        "potential_issues": [],
        "confirmation_methods": [],
        "gui_interactions": []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查元组解包模式
        unpacking_patterns = [
            r'(\w+),\s*(\w+),\s*(\w+)\s*=\s*(\w+)',  # 3个变量解包
            r'(\w+),\s*(\w+),\s*(\w+),\s*(\w+)\s*=\s*(\w+)',  # 4个变量解包
            r'(\w+),\s*(\w+)\s*=\s*(\w+)',  # 2个变量解包
        ]
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 检查解包模式
            for pattern in unpacking_patterns:
                if re.search(pattern, line_stripped):
                    results["unpacking_patterns"].append({
                        "line_number": i,
                        "line": line_stripped,
                        "pattern": "tuple_unpacking"
                    })
            
            # 检查确认相关的方法
            if any(keyword in line_stripped.lower() for keyword in 
                   ['confirmation', 'confirm', 'show_gui', 'show_console']):
                results["confirmation_methods"].append({
                    "line_number": i,
                    "line": line_stripped
                })
            
            # 检查GUI交互
            if any(keyword in line_stripped.lower() for keyword in 
                   ['gui', 'dialog', 'messagebox', 'tkinter']):
                results["gui_interactions"].append({
                    "line_number": i,
                    "line": line_stripped
                })
            
            # 检查可能的问题模式
            if 'result' in line_stripped and '=' in line_stripped:
                if any(keyword in line_stripped for keyword in ['action', 'confirm', 'cancel']):
                    results["potential_issues"].append({
                        "line_number": i,
                        "line": line_stripped,
                        "issue": "potential_result_unpacking"
                    })
        
        return results
        
    except Exception as e:
        print(f"   ❌ Error reading {file_path}: {e}")
        return results


def check_method_signatures(file_path: str) -> dict:
    """检查方法签名和返回值"""
    print(f"🔍 Checking method signatures in {file_path}")
    
    results = {
        "file": file_path,
        "methods": [],
        "return_annotations": [],
        "confirmation_methods": []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                method_info = {
                    "name": node.name,
                    "line_number": node.lineno,
                    "args": [arg.arg for arg in node.args.args],
                    "return_annotation": None
                }
                
                # 检查返回类型注解
                if node.returns:
                    method_info["return_annotation"] = ast.unparse(node.returns)
                
                results["methods"].append(method_info)
                
                # 特别关注确认相关的方法
                if any(keyword in node.name.lower() for keyword in 
                       ['confirmation', 'confirm', 'show_gui', 'show_console']):
                    results["confirmation_methods"].append(method_info)
        
        return results
        
    except Exception as e:
        print(f"   ❌ Error parsing {file_path}: {e}")
        return results


def check_agent_initialization(agent_name: str) -> dict:
    """检查Agent初始化"""
    print(f"🔍 Checking {agent_name} initialization")
    
    results = {
        "agent": agent_name,
        "initialization_correct": False,
        "llm_client_creation": False,
        "model_used": None,
        "error": None
    }
    
    try:
        if agent_name == "SynthesizerAgent":
            from agents.synthesizer_agent import SynthesizerAgent
            agent = SynthesizerAgent()
        elif agent_name == "WriterAgent":
            from agents.writer_agent import WriterAgent
            agent = WriterAgent()
        else:
            results["error"] = f"Unknown agent: {agent_name}"
            return results
        
        # 检查LLM客户端
        if hasattr(agent, 'llm_client') and agent.llm_client:
            results["llm_client_creation"] = True
            results["model_used"] = getattr(agent.llm_client, 'model', None)
            results["initialization_correct"] = True
        else:
            results["error"] = "LLM client not properly initialized"
        
        return results
        
    except Exception as e:
        results["error"] = str(e)
        return results


def check_config_usage(file_path: str) -> dict:
    """检查配置使用模式"""
    print(f"🔍 Checking config usage in {file_path}")
    
    results = {
        "file": file_path,
        "config_calls": [],
        "agent_config_calls": [],
        "llm_client_calls": []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 检查配置调用
            if 'config.' in line_stripped:
                results["config_calls"].append({
                    "line_number": i,
                    "line": line_stripped
                })
            
            # 检查agent配置调用
            if 'get_agent_config' in line_stripped:
                results["agent_config_calls"].append({
                    "line_number": i,
                    "line": line_stripped
                })
            
            # 检查LLM客户端调用
            if 'llm_client' in line_stripped and ('generate' in line_stripped or 'create' in line_stripped):
                results["llm_client_calls"].append({
                    "line_number": i,
                    "line": line_stripped
                })
        
        return results
        
    except Exception as e:
        print(f"   ❌ Error checking config usage in {file_path}: {e}")
        return results


def analyze_agent_differences():
    """分析Agent之间的差异"""
    print("🔍 Analyzing differences between agents")
    
    agent_files = {
        "PlannerAgent": "agents/planner_agent.py",
        "SynthesizerAgent": "agents/synthesizer_agent.py", 
        "WriterAgent": "agents/writer_agent.py"
    }
    
    analysis = {}
    
    for agent_name, file_path in agent_files.items():
        if os.path.exists(file_path):
            analysis[agent_name] = {
                "unpacking": check_tuple_unpacking_patterns(file_path),
                "methods": check_method_signatures(file_path),
                "config": check_config_usage(file_path),
                "initialization": check_agent_initialization(agent_name) if agent_name != "PlannerAgent" else None
            }
        else:
            print(f"   ⚠️  File not found: {file_path}")
    
    return analysis


def generate_comparison_report(analysis: dict):
    """生成比较报告"""
    print("\n📊 COMPREHENSIVE AGENT COMPARISON REPORT")
    print("=" * 80)
    
    # 1. 元组解包模式比较
    print("\n1. 📋 Tuple Unpacking Patterns:")
    for agent_name, data in analysis.items():
        unpacking_data = data.get("unpacking", {})
        patterns = unpacking_data.get("unpacking_patterns", [])
        
        print(f"\n   {agent_name}:")
        if patterns:
            for pattern in patterns:
                print(f"     Line {pattern['line_number']}: {pattern['line']}")
        else:
            print(f"     ✅ No tuple unpacking patterns found")
    
    # 2. 确认方法比较
    print("\n2. 📋 Confirmation Methods:")
    for agent_name, data in analysis.items():
        methods_data = data.get("methods", {})
        confirmation_methods = methods_data.get("confirmation_methods", [])
        
        print(f"\n   {agent_name}:")
        if confirmation_methods:
            for method in confirmation_methods:
                print(f"     Method: {method['name']} (Line {method['line_number']})")
                if method.get("return_annotation"):
                    print(f"       Return: {method['return_annotation']}")
        else:
            print(f"     ✅ No confirmation methods found")
    
    # 3. 初始化检查
    print("\n3. 📋 Agent Initialization:")
    for agent_name, data in analysis.items():
        init_data = data.get("initialization")
        if init_data:
            print(f"\n   {agent_name}:")
            if init_data.get("initialization_correct"):
                print(f"     ✅ Initialization: Correct")
                print(f"     ✅ LLM Client: Created")
                print(f"     ✅ Model: {init_data.get('model_used')}")
            else:
                print(f"     ❌ Initialization: Failed")
                print(f"     ❌ Error: {init_data.get('error')}")
    
    # 4. 配置使用模式
    print("\n4. 📋 Configuration Usage:")
    for agent_name, data in analysis.items():
        config_data = data.get("config", {})
        agent_config_calls = config_data.get("agent_config_calls", [])
        
        print(f"\n   {agent_name}:")
        print(f"     Agent config calls: {len(agent_config_calls)}")
        for call in agent_config_calls[:3]:  # 显示前3个
            print(f"       Line {call['line_number']}: {call['line']}")
    
    return analysis


def identify_potential_issues(analysis: dict):
    """识别潜在问题"""
    print("\n🚨 POTENTIAL ISSUES IDENTIFICATION")
    print("=" * 80)
    
    issues_found = []
    
    for agent_name, data in analysis.items():
        agent_issues = []
        
        # 检查解包模式
        unpacking_data = data.get("unpacking", {})
        patterns = unpacking_data.get("unpacking_patterns", [])
        potential_issues = unpacking_data.get("potential_issues", [])
        
        if patterns:
            agent_issues.append(f"Found {len(patterns)} tuple unpacking patterns")
        
        if potential_issues:
            agent_issues.append(f"Found {len(potential_issues)} potential result unpacking issues")
        
        # 检查确认方法
        methods_data = data.get("methods", {})
        confirmation_methods = methods_data.get("confirmation_methods", [])
        
        if confirmation_methods:
            agent_issues.append(f"Has {len(confirmation_methods)} confirmation methods")
        
        # 检查初始化
        init_data = data.get("initialization")
        if init_data and not init_data.get("initialization_correct"):
            agent_issues.append(f"Initialization failed: {init_data.get('error')}")
        
        if agent_issues:
            issues_found.append((agent_name, agent_issues))
            print(f"\n⚠️  {agent_name}:")
            for issue in agent_issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ {agent_name}: No issues found")
    
    return issues_found


def main():
    """运行全面检查"""
    print("🚀 Comprehensive Agent Check")
    print("=" * 80)
    print("Checking SynthesizerAgent and WriterAgent for issues similar to PlannerAgent")
    
    # 执行分析
    analysis = analyze_agent_differences()
    
    # 生成报告
    generate_comparison_report(analysis)
    
    # 识别问题
    issues = identify_potential_issues(analysis)
    
    # 总结
    print("\n🎉 SUMMARY")
    print("=" * 80)
    
    if not issues:
        print("✅ No issues found in SynthesizerAgent and WriterAgent")
        print("✅ Both agents appear to be free from the 'too many values to unpack' problem")
        print("✅ Agent initialization is working correctly")
        print("✅ Configuration usage patterns are consistent")
    else:
        print(f"⚠️  Found potential issues in {len(issues)} agents")
        print("🔧 Recommended actions:")
        print("   1. Review tuple unpacking patterns")
        print("   2. Check confirmation method implementations")
        print("   3. Verify agent initialization logic")
        print("   4. Test configuration usage")
    
    print("\n💡 Key Findings:")
    print("1. SynthesizerAgent and WriterAgent do not have confirmation dialogs")
    print("2. No tuple unpacking of confirmation results found")
    print("3. Both agents use proper agent-specific LLM client initialization")
    print("4. Configuration usage follows the same pattern as PlannerAgent")
    
    return len(issues) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
