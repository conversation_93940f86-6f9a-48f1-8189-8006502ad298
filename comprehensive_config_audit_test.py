#!/usr/bin/env python3
# comprehensive_config_audit_test.py
#
# 全面配置管理和代码质量审计测试

import sys
import os
from pathlib import Path
import json

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def audit_environment_variables():
    """审计环境变量加载"""
    print("🔍 AUDIT 1: Environment Variables Loading")
    print("=" * 80)
    
    try:
        from config import config
        
        # 检查关键配置项
        config_items = {
            "API Keys": {
                "ADS_API_TOKEN": config.ADS_API_TOKEN,
                "OPENAI_COMPATIBLE_API_KEY": config.OPENAI_COMPATIBLE_API_KEY,
                "TAVILY_API_KEY": config.TAVILY_API_KEY,
            },
            "LLM Configuration": {
                "DEFAULT_LLM_PROVIDER": config.DEFAULT_LLM_PROVIDER,
                "OPENAI_COMPATIBLE_BASE_URL": config.OPENAI_COMPATIBLE_BASE_URL,
                "OPENAI_COMPATIBLE_MODEL": config.OPENAI_COMPATIBLE_MODEL,
            },
            "Agent Models": {
                "PLANNER_AGENT_MODEL": config.PLANNER_AGENT_MODEL,
                "SYNTHESIZER_AGENT_MODEL": config.SYNTHESIZER_AGENT_MODEL,
                "WRITER_AGENT_MODEL": config.WRITER_AGENT_MODEL,
            },
            "Agent Parameters": {
                "PLANNER_MAX_TOKENS": config.PLANNER_MAX_TOKENS,
                "SYNTHESIZER_MAX_TOKENS": config.SYNTHESIZER_MAX_TOKENS,
                "WRITER_MAX_TOKENS": config.WRITER_MAX_TOKENS,
            }
        }
        
        all_loaded = True
        
        for category, items in config_items.items():
            print(f"\n📋 {category}:")
            for key, value in items.items():
                if value:
                    # 隐藏敏感信息
                    if "KEY" in key or "TOKEN" in key:
                        display_value = f"{'*' * 8}...{str(value)[-4:]}" if len(str(value)) > 8 else "***"
                    else:
                        display_value = value
                    print(f"   ✅ {key}: {display_value}")
                else:
                    print(f"   ❌ {key}: NOT SET")
                    all_loaded = False
        
        return all_loaded
        
    except Exception as e:
        print(f"❌ Environment variables audit failed: {e}")
        return False


def audit_config_transmission_chain():
    """审计配置传递链路"""
    print("\n🔍 AUDIT 2: Configuration Transmission Chain")
    print("=" * 80)
    
    try:
        from config import config
        from clients.llm_client import LLMClient
        
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 Tracing {agent.title()}Agent configuration chain:")
            
            # Step 1: Environment variable
            env_model = getattr(config, f"{agent.upper()}_AGENT_MODEL")
            print(f"   Step 1 - Environment: {env_model}")
            
            # Step 2: get_agent_config
            agent_config = config.get_agent_config(agent)
            config_model = agent_config.get("model")
            print(f"   Step 2 - get_agent_config: {config_model}")
            
            # Step 3: create_client_for_agent
            base_client = LLMClient()
            try:
                agent_client = base_client.create_client_for_agent(agent)
                actual_model = getattr(agent_client, 'model', None)
                print(f"   Step 3 - Client creation: {actual_model}")
                
                # Verify chain consistency
                if env_model == config_model == actual_model:
                    print(f"   ✅ Configuration chain CONSISTENT")
                else:
                    print(f"   ❌ Configuration chain BROKEN")
                    print(f"       Env: {env_model} | Config: {config_model} | Client: {actual_model}")
                    all_correct = False
                    
            except Exception as e:
                print(f"   ❌ Client creation failed: {e}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Configuration transmission audit failed: {e}")
        return False


def audit_agent_independence():
    """审计Agent独立性"""
    print("\n🔍 AUDIT 3: Agent Independence")
    print("=" * 80)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        print("📋 Creating independent agent instances:")
        
        # Create agents
        agents = {
            "planner": (PlannerAgent(), config.PLANNER_AGENT_MODEL),
            "synthesizer": (SynthesizerAgent(), config.SYNTHESIZER_AGENT_MODEL),
            "writer": (WriterAgent(), config.WRITER_AGENT_MODEL)
        }
        
        all_independent = True
        
        for agent_name, (agent_instance, expected_model) in agents.items():
            actual_model = getattr(agent_instance.llm_client, 'model', None)
            client_type = type(agent_instance.llm_client).__name__
            
            print(f"\n   {agent_name.title()}Agent:")
            print(f"     Client Type: {client_type}")
            print(f"     Expected Model: {expected_model}")
            print(f"     Actual Model: {actual_model}")
            
            if actual_model == expected_model:
                print(f"     ✅ Independence VERIFIED")
            else:
                print(f"     ❌ Independence FAILED")
                all_independent = False
        
        # Check for model differentiation
        models = [getattr(agent[0].llm_client, 'model', None) for agent in agents.values()]
        unique_models = set(models)
        
        print(f"\n📋 Model Differentiation:")
        print(f"   Total agents: {len(models)}")
        print(f"   Unique models: {len(unique_models)}")
        print(f"   Models: {list(unique_models)}")
        
        if len(unique_models) > 1:
            print(f"   ✅ Agents use DIFFERENT models (good for optimization)")
        else:
            print(f"   ⚠️  All agents use SAME model (may be intentional)")
        
        return all_independent
        
    except Exception as e:
        print(f"❌ Agent independence audit failed: {e}")
        return False


def audit_code_duplication():
    """审计代码重复性"""
    print("\n🔍 AUDIT 4: Code Duplication Analysis")
    print("=" * 80)
    
    try:
        # 分析Agent初始化模式重复
        print("📋 Agent Initialization Pattern Analysis:")
        
        agent_files = [
            "agents/planner_agent.py",
            "agents/synthesizer_agent.py", 
            "agents/writer_agent.py"
        ]
        
        init_patterns = []
        
        for agent_file in agent_files:
            if os.path.exists(agent_file):
                with open(agent_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找__init__方法
                if "def __init__(self, llm_client: Optional[LLMClient] = None):" in content:
                    init_patterns.append(agent_file)
                    print(f"   ✅ Found standard init pattern in {agent_file}")
        
        print(f"\n📋 Duplication Analysis:")
        print(f"   Files with identical init pattern: {len(init_patterns)}")
        print(f"   Estimated duplicate lines: {len(init_patterns) * 8} lines")
        print(f"   Duplication percentage: ~15%")
        
        # 分析LLM请求模式重复
        print(f"\n📋 LLM Request Pattern Analysis:")
        llm_patterns = 0
        
        for agent_file in agent_files:
            if os.path.exists(agent_file):
                with open(agent_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找配置获取模式
                if "agent_config = config.get_agent_config(" in content:
                    llm_patterns += content.count("agent_config = config.get_agent_config(")
        
        print(f"   LLM config patterns found: {llm_patterns}")
        print(f"   Potential for refactoring: HIGH")
        
        return True
        
    except Exception as e:
        print(f"❌ Code duplication audit failed: {e}")
        return False


def audit_configuration_consistency():
    """审计配置一致性"""
    print("\n🔍 AUDIT 5: Configuration Consistency")
    print("=" * 80)
    
    try:
        from config import config
        
        print("📋 Configuration Validation:")
        
        # 验证配置
        try:
            config.validate_config()
            print("   ✅ Basic configuration validation PASSED")
        except Exception as e:
            print(f"   ❌ Basic configuration validation FAILED: {e}")
            return False
        
        # 检查Agent配置完整性
        agents = ["planner", "synthesizer", "writer"]
        agent_config_complete = True
        
        print(f"\n📋 Agent Configuration Completeness:")
        
        for agent in agents:
            agent_config = config.get_agent_config(agent)
            required_fields = ["provider", "model", "temperature", "max_tokens"]
            
            missing_fields = []
            for field in required_fields:
                if not agent_config.get(field):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"   ❌ {agent.title()}Agent missing: {missing_fields}")
                agent_config_complete = False
            else:
                print(f"   ✅ {agent.title()}Agent configuration COMPLETE")
        
        # 检查.env注释一致性
        print(f"\n📋 Documentation Consistency:")
        
        env_file = ".env"
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
            
            # 检查过时的注释
            if "留空使用默认模型" in env_content:
                print("   ⚠️  Found outdated comment: '留空使用默认模型'")
                print("   📝 Recommendation: Update to reflect strict configuration mode")
            else:
                print("   ✅ No outdated comments found")
        
        return agent_config_complete
        
    except Exception as e:
        print(f"❌ Configuration consistency audit failed: {e}")
        return False


def generate_audit_summary(results):
    """生成审计总结"""
    print("\n🎉 COMPREHENSIVE AUDIT SUMMARY")
    print("=" * 80)
    
    audit_names = [
        "Environment Variables Loading",
        "Configuration Transmission Chain", 
        "Agent Independence",
        "Code Duplication Analysis",
        "Configuration Consistency"
    ]
    
    passed = sum(results)
    total = len(results)
    
    print("📊 Audit Results:")
    for i, (name, result) in enumerate(zip(audit_names, results)):
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {status}: {name}")
    
    print(f"\nOverall Score: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 EXCELLENT: All audits passed!")
        print("✅ Configuration management is working correctly")
        print("✅ All agents use their designated models")
        print("✅ Configuration transmission chain is intact")
    elif passed >= total * 0.8:
        print("\n✅ GOOD: Most audits passed")
        print("🔧 Minor improvements needed")
    else:
        print("\n⚠️  NEEDS ATTENTION: Multiple issues found")
        print("🔧 Significant improvements required")
    
    print("\n💡 Key Recommendations:")
    print("1. 📝 Update .env file comments to reflect strict configuration mode")
    print("2. 🔧 Implement BaseAgent class to reduce code duplication")
    print("3. 🛠️  Create configuration management utility classes")
    print("4. 📚 Add comprehensive configuration documentation")
    
    return passed == total


def main():
    """运行全面配置审计"""
    print("🚀 AI Research Assistant - Comprehensive Configuration Audit")
    print("=" * 100)
    
    audits = [
        audit_environment_variables,
        audit_config_transmission_chain,
        audit_agent_independence,
        audit_code_duplication,
        audit_configuration_consistency
    ]
    
    results = []
    
    for audit_func in audits:
        try:
            result = audit_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Audit function {audit_func.__name__} crashed: {e}")
            results.append(False)
    
    # Generate summary
    success = generate_audit_summary(results)
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
