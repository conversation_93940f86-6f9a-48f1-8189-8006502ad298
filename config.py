# config.py
# 
# 项目配置文件 - 集中管理所有配置项
# 敏感信息（API密钥）通过环境变量获取，其他配置在此文件中定义

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """项目配置类"""
    
    # ========== 执行模式配置 ==========
    # 可选值: "deep_research" 或 "full_analysis"
    EXECUTION_MODE = os.getenv("EXECUTION_MODE", "deep_research")
    
    # ========== LLM配置 ==========
    # LLM提供商: "openai", "anthropic", "local"
    LLM_PROVIDER = os.getenv("LLM_PROVIDER", "openai")
    
    # API密钥 (从环境变量获取)
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
    
    # 模型配置
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229")
    
    # LLM请求参数
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.7"))
    LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
    
    # ========== 搜索API配置 ==========
    # NASA ADS API
    ADS_API_TOKEN = os.getenv("ADS_API_TOKEN")
    ADS_BASE_URL = "https://api.adsabs.harvard.edu/v1"
    
    # 网络搜索配置 (优先使用Tavily，备选Google)
    SEARCH_PROVIDER = os.getenv("SEARCH_PROVIDER", "tavily")  # "tavily" 或 "google"
    
    # Tavily Search API
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
    
    # Google Custom Search API (备选)
    GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
    GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID")
    
    # ========== 搜索参数配置 ==========
    # 每个子问题的搜索结果数量
    WEB_SEARCH_RESULTS_PER_QUERY = int(os.getenv("WEB_SEARCH_RESULTS_PER_QUERY", "5"))
    ADS_SEARCH_RESULTS_PER_QUERY = int(os.getenv("ADS_SEARCH_RESULTS_PER_QUERY", "10"))
    
    # 最大处理的论文数量
    MAX_PAPERS_TO_ANALYZE = int(os.getenv("MAX_PAPERS_TO_ANALYZE", "50"))
    
    # 批处理大小（每次处理多少篇论文后写入文件）
    PAPER_BATCH_SIZE = int(os.getenv("PAPER_BATCH_SIZE", "5"))
    
    # ========== 输出配置 ==========
    OUTPUT_DIR = "outputs"
    RESEARCH_REPORT_FILENAME = "research_report.md"
    RESEARCH_DETAILS_FILENAME = "research_details.md"
    PROPOSAL_FILENAME = "proposal.md"
    FEASIBILITY_FILENAME = "feasibility_analysis.md"
    
    # ========== 请求配置 ==========
    # HTTP请求超时时间（秒）
    REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "30"))
    
    # API请求重试次数
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
    
    # 请求间隔（秒，避免API限流）
    REQUEST_DELAY = float(os.getenv("REQUEST_DELAY", "1.0"))
    
    # ========== 日志配置 ==========
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "research_assistant.log")

    # ========== 高级配置 ==========
    # 是否启用详细日志
    VERBOSE_LOGGING = os.getenv("VERBOSE_LOGGING", "false").lower() == "true"

    # 是否保存中间结果
    SAVE_INTERMEDIATE_RESULTS = os.getenv("SAVE_INTERMEDIATE_RESULTS", "true").lower() == "true"

    # 论文去重相似度阈值 (0-1)
    PAPER_SIMILARITY_THRESHOLD = float(os.getenv("PAPER_SIMILARITY_THRESHOLD", "0.8"))

    # 网络搜索结果过滤关键词
    SEARCH_FILTER_KEYWORDS = os.getenv("SEARCH_FILTER_KEYWORDS", "").split(",") if os.getenv("SEARCH_FILTER_KEYWORDS") else []

    # 是否启用缓存
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "false").lower() == "true"
    CACHE_DIR = os.getenv("CACHE_DIR", "cache")

    # 并发处理配置
    MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", "3"))

    # 输出格式配置
    OUTPUT_LANGUAGE = os.getenv("OUTPUT_LANGUAGE", "zh")  # zh, en
    INCLUDE_CITATIONS = os.getenv("INCLUDE_CITATIONS", "true").lower() == "true"
    
    # ========== 验证配置 ==========
    @classmethod
    def validate_config(cls):
        """验证必要的配置项是否已设置"""
        errors = []
        
        # 检查LLM配置
        if cls.LLM_PROVIDER == "openai" and not cls.OPENAI_API_KEY:
            errors.append("OPENAI_API_KEY is required when using OpenAI")
        elif cls.LLM_PROVIDER == "anthropic" and not cls.ANTHROPIC_API_KEY:
            errors.append("ANTHROPIC_API_KEY is required when using Anthropic")
        
        # 检查ADS API配置
        if not cls.ADS_API_TOKEN:
            errors.append("ADS_API_TOKEN is required for academic paper search")
        
        # 检查搜索API配置（至少需要一个）
        if cls.SEARCH_PROVIDER == "tavily" and not cls.TAVILY_API_KEY:
            if not (cls.GOOGLE_SEARCH_API_KEY and cls.GOOGLE_CSE_ID):
                errors.append("Either TAVILY_API_KEY or (GOOGLE_SEARCH_API_KEY + GOOGLE_CSE_ID) is required")
        
        # 检查数值范围
        if not (0 <= cls.PAPER_SIMILARITY_THRESHOLD <= 1):
            errors.append("PAPER_SIMILARITY_THRESHOLD must be between 0 and 1")

        if cls.MAX_CONCURRENT_REQUESTS < 1:
            errors.append("MAX_CONCURRENT_REQUESTS must be at least 1")

        if cls.PAPER_BATCH_SIZE < 1:
            errors.append("PAPER_BATCH_SIZE must be at least 1")

        if errors:
            raise ValueError("Configuration errors:\n" + "\n".join(f"- {error}" for error in errors))

        return True

    @classmethod
    def get_config_summary(cls) -> dict:
        """获取配置摘要"""
        return {
            "execution_mode": cls.EXECUTION_MODE,
            "llm_provider": cls.LLM_PROVIDER,
            "search_provider": cls.SEARCH_PROVIDER,
            "max_papers": cls.MAX_PAPERS_TO_ANALYZE,
            "batch_size": cls.PAPER_BATCH_SIZE,
            "output_dir": cls.OUTPUT_DIR,
            "log_level": cls.LOG_LEVEL,
            "api_keys_configured": {
                "ads": bool(cls.ADS_API_TOKEN),
                "openai": bool(cls.OPENAI_API_KEY),
                "anthropic": bool(cls.ANTHROPIC_API_KEY),
                "tavily": bool(cls.TAVILY_API_KEY),
                "google": bool(cls.GOOGLE_SEARCH_API_KEY and cls.GOOGLE_CSE_ID)
            }
        }

    @classmethod
    def is_development_mode(cls) -> bool:
        """检查是否为开发模式"""
        return cls.LOG_LEVEL.upper() == "DEBUG" or cls.VERBOSE_LOGGING

    @classmethod
    def get_available_providers(cls) -> dict:
        """获取可用的服务提供商"""
        return {
            "llm": {
                "openai": bool(cls.OPENAI_API_KEY),
                "anthropic": bool(cls.ANTHROPIC_API_KEY)
            },
            "search": {
                "tavily": bool(cls.TAVILY_API_KEY),
                "google": bool(cls.GOOGLE_SEARCH_API_KEY and cls.GOOGLE_CSE_ID)
            }
        }

# 创建全局配置实例
config = Config()
