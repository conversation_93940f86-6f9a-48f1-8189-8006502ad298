# config.py
# 
# 项目配置文件 - 集中管理所有配置项
# 敏感信息（API密钥）通过环境变量获取，其他配置在此文件中定义

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """项目配置类"""
    
    # ========== 执行模式配置 ==========
    # 可选值: "deep_research" 或 "full_analysis"
    EXECUTION_MODE = os.getenv("EXECUTION_MODE", "deep_research")
    
    # ========== LLM配置 ==========
    # 默认LLM提供商: "openai", "openai-compatible", "anthropic", "gemini"
    DEFAULT_LLM_PROVIDER = os.getenv("DEFAULT_LLM_PROVIDER", "openai")

    # API密钥 (从环境变量获取)
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

    # OpenAI兼容模式配置
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL")  # 自定义OpenAI API端点
    OPENAI_COMPATIBLE_BASE_URL = os.getenv("OPENAI_COMPATIBLE_BASE_URL")  # OpenAI兼容API端点
    OPENAI_COMPATIBLE_API_KEY = os.getenv("OPENAI_COMPATIBLE_API_KEY", "dummy-key")  # 兼容API密钥
    OPENAI_COMPATIBLE_MODEL = os.getenv("OPENAI_COMPATIBLE_MODEL", "gpt-3.5-turbo")  # 兼容模型名称

    # 模型配置
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229")
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-1.5-pro")

    # 多模型代理配置 - 允许不同代理使用不同模型
    PLANNER_AGENT_PROVIDER = os.getenv("PLANNER_AGENT_PROVIDER", DEFAULT_LLM_PROVIDER)
    SYNTHESIZER_AGENT_PROVIDER = os.getenv("SYNTHESIZER_AGENT_PROVIDER", DEFAULT_LLM_PROVIDER)
    WRITER_AGENT_PROVIDER = os.getenv("WRITER_AGENT_PROVIDER", DEFAULT_LLM_PROVIDER)
    PAPER_RANKING_AGENT_PROVIDER = os.getenv("PAPER_RANKING_AGENT_PROVIDER", DEFAULT_LLM_PROVIDER)

    # 代理特定模型配置
    PLANNER_AGENT_MODEL = os.getenv("PLANNER_AGENT_MODEL", "")  # 空字符串表示使用默认
    SYNTHESIZER_AGENT_MODEL = os.getenv("SYNTHESIZER_AGENT_MODEL", "")
    WRITER_AGENT_MODEL = os.getenv("WRITER_AGENT_MODEL", "")
    PAPER_RANKING_AGENT_MODEL = os.getenv("PAPER_RANKING_AGENT_MODEL", "")

    # LLM请求参数 - 全局默认值
    DEFAULT_LLM_TEMPERATURE = float(os.getenv("DEFAULT_LLM_TEMPERATURE", "0.7"))
    DEFAULT_LLM_MAX_TOKENS = int(os.getenv("DEFAULT_LLM_MAX_TOKENS", "8000"))  # 增加默认token限制

    # 代理特定参数
    PLANNER_TEMPERATURE = float(os.getenv("PLANNER_TEMPERATURE", str(DEFAULT_LLM_TEMPERATURE)))
    PLANNER_MAX_TOKENS = int(os.getenv("PLANNER_MAX_TOKENS", "6000"))

    SYNTHESIZER_TEMPERATURE = float(os.getenv("SYNTHESIZER_TEMPERATURE", str(DEFAULT_LLM_TEMPERATURE)))
    SYNTHESIZER_MAX_TOKENS = int(os.getenv("SYNTHESIZER_MAX_TOKENS", "12000"))  # 更高的token限制用于批量分析

    WRITER_TEMPERATURE = float(os.getenv("WRITER_TEMPERATURE", str(DEFAULT_LLM_TEMPERATURE)))
    WRITER_MAX_TOKENS = int(os.getenv("WRITER_MAX_TOKENS", "16000"))  # 最高的token限制用于报告生成

    PAPER_RANKING_TEMPERATURE = float(os.getenv("PAPER_RANKING_TEMPERATURE", str(DEFAULT_LLM_TEMPERATURE)))
    PAPER_RANKING_MAX_TOKENS = int(os.getenv("PAPER_RANKING_MAX_TOKENS", "8000"))  # 论文排序专用token限制
    
    # ========== 搜索API配置 ==========
    # NASA ADS API
    ADS_API_TOKEN = os.getenv("ADS_API_TOKEN")
    ADS_BASE_URL = "https://api.adsabs.harvard.edu/v1"
    
    # 网络搜索配置 (优先使用Tavily，备选Google)
    SEARCH_PROVIDER = os.getenv("SEARCH_PROVIDER", "tavily")  # "tavily" 或 "google"
    
    # Tavily Search API
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
    
    # Google Custom Search API (备选)
    GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
    GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID")
    
    # ========== 搜索参数配置 ==========
    # 每个子问题的搜索结果数量
    WEB_SEARCH_RESULTS_PER_QUERY = int(os.getenv("WEB_SEARCH_RESULTS_PER_QUERY", "5"))

    # 增强的ADS搜索配置
    ADS_PAPERS_PER_SUBQUESTION = int(os.getenv("ADS_PAPERS_PER_SUBQUESTION", "100"))  # 每个子问题检索的论文数量
    ADS_TOP_CITED_COUNT = int(os.getenv("ADS_TOP_CITED_COUNT", "30"))  # 按引用数排序选择的论文数量
    ADS_RECENT_PAPERS_COUNT = int(os.getenv("ADS_RECENT_PAPERS_COUNT", "30"))  # 按时间排序选择的论文数量

    # 论文分析配置
    PAPERS_PER_ANALYSIS_BATCH = int(os.getenv("PAPERS_PER_ANALYSIS_BATCH", "5"))  # 每批分析的论文数量
    MAX_PAPERS_TO_ANALYZE = int(os.getenv("MAX_PAPERS_TO_ANALYZE", "50"))  # 保持向后兼容

    # 批处理大小（每次处理多少篇论文后写入文件）
    PAPER_BATCH_SIZE = int(os.getenv("PAPER_BATCH_SIZE", "5"))  # 保持向后兼容

    # ========== 引用配置 ==========
    CITATION_STYLE = os.getenv("CITATION_STYLE", "APA")  # APA, IEEE, Nature
    INLINE_CITATION_FORMAT = os.getenv("INLINE_CITATION_FORMAT", "numeric")  # numeric, author-year
    BIBLIOGRAPHY_SORT = os.getenv("BIBLIOGRAPHY_SORT", "alphabetical")  # alphabetical, chronological
    MAX_AUTHORS_INLINE = int(os.getenv("MAX_AUTHORS_INLINE", "2"))  # 内联引用最大作者数
    MAX_AUTHORS_BIBLIOGRAPHY = int(os.getenv("MAX_AUTHORS_BIBLIOGRAPHY", "10"))  # 参考文献最大作者数
    ENABLE_CITATIONS = os.getenv("ENABLE_CITATIONS", "true").lower() == "true"  # 是否启用引用功能

    # ========== 输出配置 ==========
    OUTPUT_DIR = "outputs"
    RESEARCH_REPORT_FILENAME = "research_report.md"
    RESEARCH_DETAILS_FILENAME = "research_details.md"
    PROPOSAL_FILENAME = "proposal.md"
    FEASIBILITY_FILENAME = "feasibility_analysis.md"
    
    # ========== 请求配置 ==========
    # HTTP请求超时时间（秒）
    REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "30"))
    
    # API请求重试次数
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
    
    # 请求间隔（秒，避免API限流）
    REQUEST_DELAY = float(os.getenv("REQUEST_DELAY", "1.0"))
    
    # ========== 日志配置 ==========
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "research_assistant.log")

    # ========== 高级配置 ==========
    # 是否启用详细日志
    VERBOSE_LOGGING = os.getenv("VERBOSE_LOGGING", "false").lower() == "true"

    # 是否保存中间结果
    SAVE_INTERMEDIATE_RESULTS = os.getenv("SAVE_INTERMEDIATE_RESULTS", "true").lower() == "true"

    # 论文去重相似度阈值 (0-1)
    PAPER_SIMILARITY_THRESHOLD = float(os.getenv("PAPER_SIMILARITY_THRESHOLD", "0.8"))

    # 网络搜索结果过滤关键词
    SEARCH_FILTER_KEYWORDS = os.getenv("SEARCH_FILTER_KEYWORDS", "").split(",") if os.getenv("SEARCH_FILTER_KEYWORDS") else []

    # 是否启用缓存
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "false").lower() == "true"
    CACHE_DIR = os.getenv("CACHE_DIR", "cache")

    # 并发处理配置
    MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", "3"))

    # 输出格式配置
    OUTPUT_LANGUAGE = os.getenv("OUTPUT_LANGUAGE", "zh")  # zh, en
    INCLUDE_CITATIONS = os.getenv("INCLUDE_CITATIONS", "true").lower() == "true"
    
    # ========== 验证配置 ==========
    @classmethod
    def validate_config(cls):
        """验证必要的配置项是否已设置"""
        errors = []
        
        # 检查LLM配置 - 更新为支持多模型配置
        providers_to_check = {cls.DEFAULT_LLM_PROVIDER, cls.PLANNER_AGENT_PROVIDER,
                             cls.SYNTHESIZER_AGENT_PROVIDER, cls.WRITER_AGENT_PROVIDER}

        for provider in providers_to_check:
            if provider == "openai" and not cls.OPENAI_API_KEY:
                errors.append("OPENAI_API_KEY is required when using OpenAI")
            elif provider == "openai-compatible" and not cls.OPENAI_COMPATIBLE_BASE_URL:
                errors.append("OPENAI_COMPATIBLE_BASE_URL is required when using OpenAI-compatible mode")
            elif provider == "anthropic" and not cls.ANTHROPIC_API_KEY:
                errors.append("ANTHROPIC_API_KEY is required when using Anthropic")
            elif provider == "gemini" and not cls.GEMINI_API_KEY:
                errors.append("GEMINI_API_KEY is required when using Gemini")
        
        # 检查ADS API配置
        if not cls.ADS_API_TOKEN:
            errors.append("ADS_API_TOKEN is required for academic paper search")
        
        # 检查搜索API配置（至少需要一个）
        if cls.SEARCH_PROVIDER == "tavily" and not cls.TAVILY_API_KEY:
            if not (cls.GOOGLE_SEARCH_API_KEY and cls.GOOGLE_CSE_ID):
                errors.append("Either TAVILY_API_KEY or (GOOGLE_SEARCH_API_KEY + GOOGLE_CSE_ID) is required")
        
        # 检查数值范围
        if not (0 <= cls.PAPER_SIMILARITY_THRESHOLD <= 1):
            errors.append("PAPER_SIMILARITY_THRESHOLD must be between 0 and 1")

        if cls.MAX_CONCURRENT_REQUESTS < 1:
            errors.append("MAX_CONCURRENT_REQUESTS must be at least 1")

        if cls.PAPER_BATCH_SIZE < 1:
            errors.append("PAPER_BATCH_SIZE must be at least 1")

        if cls.PAPERS_PER_ANALYSIS_BATCH < 1:
            errors.append("PAPERS_PER_ANALYSIS_BATCH must be at least 1")

        if cls.ADS_PAPERS_PER_SUBQUESTION < 1:
            errors.append("ADS_PAPERS_PER_SUBQUESTION must be at least 1")

        if cls.ADS_TOP_CITED_COUNT < 1:
            errors.append("ADS_TOP_CITED_COUNT must be at least 1")

        if cls.ADS_RECENT_PAPERS_COUNT < 1:
            errors.append("ADS_RECENT_PAPERS_COUNT must be at least 1")

        if errors:
            raise ValueError("Configuration errors:\n" + "\n".join(f"- {error}" for error in errors))

        return True

    @classmethod
    def get_config_summary(cls) -> dict:
        """获取配置摘要"""
        return {
            "execution_mode": cls.EXECUTION_MODE,
            "llm_provider": cls.DEFAULT_LLM_PROVIDER,
            "search_provider": cls.SEARCH_PROVIDER,
            "max_papers": cls.MAX_PAPERS_TO_ANALYZE,
            "batch_size": cls.PAPER_BATCH_SIZE,
            "output_dir": cls.OUTPUT_DIR,
            "log_level": cls.LOG_LEVEL,
            "api_keys_configured": {
                "ads": bool(cls.ADS_API_TOKEN),
                "openai": bool(cls.OPENAI_API_KEY),
                "anthropic": bool(cls.ANTHROPIC_API_KEY),
                "tavily": bool(cls.TAVILY_API_KEY),
                "google": bool(cls.GOOGLE_SEARCH_API_KEY and cls.GOOGLE_CSE_ID)
            }
        }

    @classmethod
    def is_development_mode(cls) -> bool:
        """检查是否为开发模式"""
        return cls.LOG_LEVEL.upper() == "DEBUG" or cls.VERBOSE_LOGGING

    @classmethod
    def get_available_providers(cls) -> dict:
        """获取可用的服务提供商"""
        return {
            "llm": {
                "openai": bool(cls.OPENAI_API_KEY),
                "openai-compatible": bool(cls.OPENAI_COMPATIBLE_BASE_URL),
                "anthropic": bool(cls.ANTHROPIC_API_KEY),
                "gemini": bool(cls.GEMINI_API_KEY)
            },
            "search": {
                "tavily": bool(cls.TAVILY_API_KEY),
                "google": bool(cls.GOOGLE_SEARCH_API_KEY and cls.GOOGLE_CSE_ID)
            }
        }

    @classmethod
    def get_agent_config(cls, agent_type: str) -> dict:
        """获取特定代理的配置"""
        agent_configs = {
            "planner": {
                "provider": cls.PLANNER_AGENT_PROVIDER,
                "model": cls.PLANNER_AGENT_MODEL,
                "temperature": cls.PLANNER_TEMPERATURE,
                "max_tokens": cls.PLANNER_MAX_TOKENS
            },
            "synthesizer": {
                "provider": cls.SYNTHESIZER_AGENT_PROVIDER,
                "model": cls.SYNTHESIZER_AGENT_MODEL,
                "temperature": cls.SYNTHESIZER_TEMPERATURE,
                "max_tokens": cls.SYNTHESIZER_MAX_TOKENS
            },
            "writer": {
                "provider": cls.WRITER_AGENT_PROVIDER,
                "model": cls.WRITER_AGENT_MODEL,
                "temperature": cls.WRITER_TEMPERATURE,
                "max_tokens": cls.WRITER_MAX_TOKENS
            },
            "paper_ranking": {
                "provider": cls.PAPER_RANKING_AGENT_PROVIDER,
                "model": cls.PAPER_RANKING_AGENT_MODEL,
                "temperature": cls.PAPER_RANKING_TEMPERATURE,
                "max_tokens": cls.PAPER_RANKING_MAX_TOKENS
            }
        }

        config = agent_configs.get(agent_type, {})

        # 只返回配置文件中明确指定的agent模型，不使用默认模型回退
        # 如果agent模型为空，将保持为空，由调用方决定如何处理

        return config

    @classmethod
    def get_model_for_provider(cls, provider: str) -> str:
        """根据提供商获取默认模型"""
        model_mapping = {
            "openai": cls.OPENAI_MODEL,
            "openai-compatible": cls.OPENAI_COMPATIBLE_MODEL,
            "anthropic": cls.ANTHROPIC_MODEL,
            "gemini": cls.GEMINI_MODEL
        }
        return model_mapping.get(provider, "")

# 创建全局配置实例
config = Config()
