#!/usr/bin/env python3
# debug_model_passing.py
#
# 调试模型传递逻辑

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def debug_config_loading():
    """调试配置加载"""
    print("🔍 Debug: Configuration Loading")
    print("=" * 60)
    
    try:
        from config import config
        
        print("📋 Environment Variables:")
        print(f"   OPENAI_COMPATIBLE_MODEL: {config.OPENAI_COMPATIBLE_MODEL}")
        print(f"   PLANNER_AGENT_MODEL: {config.PLANNER_AGENT_MODEL}")
        print(f"   SYNTHESIZER_AGENT_MODEL: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WRITER_AGENT_MODEL: {config.WRITER_AGENT_MODEL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False


def debug_get_agent_config():
    """调试get_agent_config方法"""
    print("\n🔍 Debug: get_agent_config Method")
    print("=" * 60)
    
    try:
        from config import config
        
        agents = ["planner", "synthesizer", "writer"]
        
        for agent in agents:
            print(f"\n📋 get_agent_config('{agent}'):")
            agent_config = config.get_agent_config(agent)
            
            print(f"   Raw config: {agent_config}")
            print(f"   Provider: {agent_config.get('provider')}")
            print(f"   Model: '{agent_config.get('model')}'")
            print(f"   Model type: {type(agent_config.get('model'))}")
            print(f"   Model bool: {bool(agent_config.get('model'))}")
        
        return True
        
    except Exception as e:
        print(f"❌ get_agent_config debug failed: {e}")
        return False


def debug_create_client_for_agent():
    """调试create_client_for_agent方法"""
    print("\n🔍 Debug: create_client_for_agent Method")
    print("=" * 60)
    
    try:
        from clients.llm_client import LLMClient
        from config import config
        
        base_client = LLMClient()
        
        print("📋 Testing PlannerAgent client creation:")
        
        # 手动执行create_client_for_agent的逻辑
        agent_config = config.get_agent_config("planner")
        provider = agent_config.get("provider", config.DEFAULT_LLM_PROVIDER)
        model = agent_config.get("model")
        
        print(f"   Step 1 - agent_config: {agent_config}")
        print(f"   Step 2 - provider: {provider}")
        print(f"   Step 3 - model from config: '{model}'")
        print(f"   Step 4 - model type: {type(model)}")
        print(f"   Step 5 - model bool: {bool(model)}")
        
        # 检查我们的严格验证
        if not model:
            print(f"   Step 6 - Model is empty, should raise ValueError")
            try:
                client = base_client.create_client_for_agent("planner")
                print(f"   ❌ No error raised, client created: {type(client)}")
            except ValueError as e:
                print(f"   ✅ Correctly raised ValueError: {e}")
        else:
            print(f"   Step 6 - Model is not empty, creating client...")
            try:
                client = base_client.create_client_for_agent("planner")
                actual_model = getattr(client, 'model', None)
                print(f"   Client created: {type(client).__name__}")
                print(f"   Client model: '{actual_model}'")
                
                if actual_model == model:
                    print(f"   ✅ Model passed correctly")
                else:
                    print(f"   ❌ Model mismatch!")
                    print(f"       Expected: '{model}'")
                    print(f"       Got: '{actual_model}'")
            except Exception as e:
                print(f"   ❌ Error creating client: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ create_client_for_agent debug failed: {e}")
        return False


def debug_openai_compatible_client():
    """调试OpenAICompatibleClient初始化"""
    print("\n🔍 Debug: OpenAICompatibleClient Initialization")
    print("=" * 60)
    
    try:
        from clients.llm_client import OpenAICompatibleClient
        from config import config
        
        print("📋 Testing OpenAICompatibleClient with different model values:")
        
        test_cases = [
            ("gemini-2.5-pro", "Explicit model"),
            ("", "Empty string"),
            (None, "None value"),
        ]
        
        for model_value, description in test_cases:
            print(f"\n   Testing {description}: '{model_value}'")
            try:
                client = OpenAICompatibleClient(model=model_value)
                actual_model = getattr(client, 'model', None)
                print(f"     Created client with model: '{actual_model}'")
                
                if model_value and actual_model == model_value:
                    print(f"     ✅ Model passed correctly")
                elif not model_value and actual_model == config.OPENAI_COMPATIBLE_MODEL:
                    print(f"     ⚠️  Used fallback model: {config.OPENAI_COMPATIBLE_MODEL}")
                else:
                    print(f"     ❌ Unexpected model result")
                    
            except Exception as e:
                print(f"     ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAICompatibleClient debug failed: {e}")
        return False


def debug_planner_agent():
    """调试PlannerAgent实际使用的模型"""
    print("\n🔍 Debug: PlannerAgent Model Usage")
    print("=" * 60)
    
    try:
        from agents.planner_agent import PlannerAgent
        from config import config
        
        expected_model = config.PLANNER_AGENT_MODEL
        print(f"📋 Expected PlannerAgent model: '{expected_model}'")
        
        print(f"📋 Creating PlannerAgent instance...")
        planner = PlannerAgent()
        
        if hasattr(planner, 'llm_client') and planner.llm_client:
            actual_model = getattr(planner.llm_client, 'model', None)
            client_type = type(planner.llm_client).__name__
            
            print(f"   Client type: {client_type}")
            print(f"   Actual model: '{actual_model}'")
            
            if actual_model == expected_model:
                print(f"   ✅ PlannerAgent using correct model")
            else:
                print(f"   ❌ PlannerAgent using wrong model!")
                print(f"       Expected: '{expected_model}'")
                print(f"       Got: '{actual_model}'")
                print(f"       This explains the log showing gemini-2.0-flash!")
        else:
            print(f"   ❌ PlannerAgent has no LLM client")
        
        return True
        
    except Exception as e:
        print(f"❌ PlannerAgent debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有调试测试"""
    print("🚀 Debug Model Passing Logic")
    print("=" * 80)
    print("Investigating why PlannerAgent is using gemini-2.0-flash instead of gemini-2.5-pro")
    
    debug_functions = [
        ("Configuration Loading", debug_config_loading),
        ("get_agent_config Method", debug_get_agent_config),
        ("create_client_for_agent Method", debug_create_client_for_agent),
        ("OpenAICompatibleClient Initialization", debug_openai_compatible_client),
        ("PlannerAgent Model Usage", debug_planner_agent),
    ]
    
    results = []
    
    for debug_name, debug_func in debug_functions:
        try:
            result = debug_func()
            results.append((debug_name, result))
        except Exception as e:
            print(f"❌ {debug_name} crashed: {e}")
            results.append((debug_name, False))
    
    # Summary
    print("\n🎉 Debug Results Summary")
    print("=" * 80)
    
    for debug_name, result in results:
        status = "✅ COMPLETED" if result else "❌ FAILED"
        print(f"{status}: {debug_name}")
    
    print("\n💡 Key Findings:")
    print("🔍 Check if OpenAICompatibleClient is overriding the model parameter")
    print("🔍 Verify the model passing chain from config to client")
    print("🔍 Look for any fallback logic that might be interfering")


if __name__ == "__main__":
    main()
