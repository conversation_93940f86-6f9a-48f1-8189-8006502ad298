#!/usr/bin/env python3
# demo_optimized_gui.py
#
# 演示优化后的GUI功能

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def demo_optimized_planner_review():
    """演示优化后的PlannerAgent结果预览对话框"""
    print("🎬 Demo: Optimized Planner Review Dialog")
    print("=" * 70)
    
    try:
        from gui.planner_review_dialog import show_planner_review_dialog
        
        # 创建真实的测试数据
        topic = "Deep Learning Applications in Astrophysics"
        
        sub_questions = [
            "What are the current applications of deep learning in astrophysics?",
            "How do neural networks improve astronomical data analysis and processing?",
            "What are the main challenges and limitations in applying AI to astrophysical research?",
            "What are the future prospects and emerging trends for AI in astronomy?",
            "How can machine learning enhance the discovery of exoplanets and other celestial objects?"
        ]
        
        keywords = [
            "deep learning", "astrophysics", "neural networks", 
            "data analysis", "machine learning", "astronomy",
            "artificial intelligence", "cosmic data", "image processing",
            "pattern recognition", "big data", "computational astronomy"
        ]
        
        sub_question_keywords = {
            "sub_question_1": ["deep learning applications", "astrophysics", "current research", "neural networks"],
            "sub_question_2": ["neural networks", "data analysis", "astronomical processing", "image enhancement"],
            "sub_question_3": ["AI challenges", "limitations", "astrophysical research", "computational constraints"],
            "sub_question_4": ["future prospects", "emerging trends", "AI astronomy", "technological advancement"],
            "sub_question_5": ["machine learning", "exoplanet discovery", "celestial objects", "detection algorithms"]
        }
        
        print("📋 Demo Data:")
        print(f"   Research Topic: {topic}")
        print(f"   Sub-questions: {len(sub_questions)}")
        print(f"   General Keywords: {len(keywords)}")
        print(f"   Sub-question Keywords: {len(sub_question_keywords)} mappings")
        
        print("\n🚀 Opening Optimized GUI Dialog...")
        print("   Features to observe:")
        print("   ✅ Structured display format")
        print("   ✅ Research topic at the top")
        print("   ✅ General keywords section")
        print("   ✅ Sub-questions with specific keywords")
        print("   ✅ Improved text wrapping")
        print("   ✅ Enhanced editing capabilities")
        print("   ✅ Real-time plan updates")
        
        # 显示对话框
        result = show_planner_review_dialog(
            topic=topic,
            sub_questions=sub_questions,
            keywords=keywords,
            estimated_papers=240,
            estimated_time="30-40 minutes",
            sub_question_keywords=sub_question_keywords
        )
        
        if result:
            action, final_questions, final_keywords, final_sub_keywords = result
            
            print(f"\n✅ User Action: {action}")
            print(f"📊 Final Results:")
            print(f"   Questions: {len(final_questions)}")
            print(f"   Keywords: {len(final_keywords)}")
            print(f"   Sub-question Keywords: {len(final_sub_keywords)}")
            
            if action == 'confirm':
                print("\n📋 Final Research Plan:")
                print(f"研究课题：{topic}")
                print(f"总体关键词：{', '.join(final_keywords)}")
                print()
                
                for i, question in enumerate(final_questions):
                    print(f"子问题{i+1}：{question}")
                    key = f"sub_question_{i+1}"
                    if key in final_sub_keywords and final_sub_keywords[key]:
                        print(f"相关关键词：{', '.join(final_sub_keywords[key])}")
                    else:
                        print("相关关键词：暂无特定关键词")
                    print()
                    
            elif action == 'regenerate':
                print("   → User requested plan regeneration")
                
            return True
        else:
            print("\n❌ User cancelled the dialog")
            return False
            
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_console_confirmation():
    """演示控制台确认功能"""
    print("\n🎬 Demo: Console Confirmation (Fallback)")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from models import ResearchQuery
        
        # 创建模拟的ResearchQuery
        query = ResearchQuery(main_topic="Machine Learning in Cosmology")
        query.sub_questions = [
            "How is machine learning used in cosmological simulations?",
            "What are the applications of AI in dark matter research?",
            "How can neural networks improve cosmic microwave background analysis?"
        ]
        query.keywords = [
            "machine learning", "cosmology", "simulations", 
            "dark matter", "neural networks", "CMB analysis"
        ]
        query.sub_question_keywords = {
            "sub_question_1": ["machine learning", "cosmological simulations", "N-body simulations"],
            "sub_question_2": ["AI applications", "dark matter", "particle physics"],
            "sub_question_3": ["neural networks", "CMB analysis", "cosmic microwave background"]
        }
        
        planner = PlannerAgent()
        
        print("📋 Console Confirmation Preview:")
        print("   This shows how the console fallback displays the research plan")
        print("   with the new structured format including sub-question keywords.")
        print()
        
        # 显示控制台确认（但不实际等待用户输入）
        print("=" * 80)
        print("RESEARCH PLAN REVIEW")
        print("=" * 80)
        print(f"Topic: {query.main_topic}")
        print(f"\nSub-questions ({len(query.sub_questions)}):")
        for i, question in enumerate(query.sub_questions, 1):
            print(f"  {i}. {question}")
            # 显示子问题关键词
            sub_keywords = query.get_keywords_for_subquestion(i-1)
            if sub_keywords:
                print(f"     Keywords: {', '.join(sub_keywords)}")
        
        print(f"\nGeneral Keywords ({len(query.keywords)}):")
        for i, keyword in enumerate(query.keywords, 1):
            print(f"  {i}. {keyword}")
        
        estimated_papers = len(query.keywords) * 20
        print(f"\nEstimated papers: {estimated_papers}")
        print(f"Estimated time: {estimated_papers // 10}-{estimated_papers // 8} minutes")
        
        print("\nOptions:")
        print("1. Confirm and continue")
        print("2. Regenerate plan")
        print("3. Cancel")
        print("=" * 80)
        
        print("\n✅ Console confirmation format updated successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Console demo error: {e}")
        return False


def main():
    """运行演示"""
    print("🎬 AI Research Assistant - Optimized GUI Demo")
    print("=" * 90)
    
    print("📋 This demo showcases the optimized GUI and Agent functionality:")
    print("   🔧 AI model configuration per agent")
    print("   📊 Structured research plan display")
    print("   ✏️  Enhanced editing capabilities")
    print("   🔄 Real-time plan updates")
    print("   📱 Improved user experience")
    
    try:
        # Demo 1: Optimized GUI
        success1 = demo_optimized_planner_review()
        
        # Demo 2: Console fallback
        success2 = demo_console_confirmation()
        
        print("\n🎉 Demo Summary")
        print("=" * 90)
        
        if success1 and success2:
            print("✅ All demos completed successfully!")
        elif success1 or success2:
            print("✅ Partial success - some demos completed")
        else:
            print("❌ Demo encountered issues")
        
        print("\n🔧 Key Optimizations Demonstrated:")
        print("✅ Agent-specific AI model configuration")
        print("   - Planner: gemini-2.5-pro (20K tokens)")
        print("   - Synthesizer: gemini-2.5-flash (50K tokens)")
        print("   - Writer: gemini-2.5-pro (60K tokens)")
        
        print("\n✅ GUI Display Optimizations:")
        print("   - Structured research plan layout")
        print("   - Research topic + general keywords")
        print("   - Sub-questions with specific keywords")
        print("   - Improved text wrapping and scrolling")
        print("   - Enhanced editing with real-time updates")
        
        print("\n✅ PlannerAgent Return Structure:")
        print("   - main_topic: Research topic title")
        print("   - keywords: General keyword list")
        print("   - sub_questions: List of research questions")
        print("   - sub_question_keywords: Mapping of questions to keywords")
        
        print("\n✅ User Experience Improvements:")
        print("   - Click-to-edit functionality")
        print("   - Visual feedback and validation")
        print("   - Structured display format")
        print("   - Better error handling")
        
        print("\n🚀 Usage Instructions:")
        print("1. Run: python main.py")
        print("2. Use GUI to input research topic")
        print("3. Review structured research plan")
        print("4. Edit questions and keywords as needed")
        print("5. Confirm to continue with research")
        
    except KeyboardInterrupt:
        print("\n\n❌ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
