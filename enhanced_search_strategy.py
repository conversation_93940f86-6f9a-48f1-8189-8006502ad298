# enhanced_search_strategy.py
#
# 增强的搜索策略实现 - 智能关键词优化和自适应搜索

import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class SearchPlatform(Enum):
    ADS = "ads"
    TAVILY = "tavily"
    HYBRID = "hybrid"

class SearchIntent(Enum):
    BROAD = "broad"
    SPECIFIC = "specific"
    METHODOLOGICAL = "methodological"
    RECENT = "recent"
    REVIEW = "review"
    APPLICATION = "application"
    CHALLENGES = "challenges"

@dataclass
class SearchResult:
    title: str
    content: str
    url: str
    source: str
    timestamp: Optional[str] = None
    relevance_score: float = 0.0
    quality_score: float = 0.0
    authority_score: float = 0.0

@dataclass
class ScoredResult:
    result: SearchResult
    composite_score: float
    score_breakdown: Dict[str, float]

class EnhancedKeywordOptimizer:
    """增强的关键词优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.platform_templates = {
            SearchPlatform.ADS: {
                SearchIntent.BROAD: 'title:"{keyword}" OR abstract:"{keyword}"',
                SearchIntent.SPECIFIC: '(title:"{keyword1}" AND abstract:"{keyword2}")',
                SearchIntent.METHODOLOGICAL: 'title:"{keyword}" AND (abstract:"method" OR abstract:"approach" OR abstract:"technique")',
                SearchIntent.RECENT: 'title:"{keyword}" AND year:2020-2024',
                SearchIntent.REVIEW: 'title:"{keyword}" AND (title:"review" OR title:"survey" OR abstract:"comprehensive")'
            },
            SearchPlatform.TAVILY: {
                SearchIntent.BROAD: '{keyword} overview introduction basics',
                SearchIntent.APPLICATION: '{keyword} applications uses practical implementation real-world',
                SearchIntent.RECENT: '{keyword} latest recent developments 2023 2024 new advances',
                SearchIntent.CHALLENGES: '{keyword} challenges problems limitations issues difficulties',
                SearchIntent.METHODOLOGICAL: '{keyword} methods techniques approaches how to'
            }
        }
    
    def optimize_keywords_for_platform(self, keywords: List[str], platform: SearchPlatform, 
                                     intent: SearchIntent, domain: str = None) -> List[str]:
        """根据平台和意图优化关键词"""
        try:
            templates = self.platform_templates.get(platform, {})
            template = templates.get(intent, '{keyword}')
            
            optimized_queries = []
            
            for keyword in keywords:
                if isinstance(keyword, list):
                    # 多关键词组合
                    query = self._build_multi_keyword_query(keyword, template, platform)
                else:
                    # 单关键词
                    query = template.format(keyword=keyword, keyword1=keyword, keyword2=keyword)
                
                optimized_queries.append(query)
            
            # 根据领域添加特定优化
            if domain:
                optimized_queries = self._apply_domain_optimization(optimized_queries, domain, platform)
            
            self.logger.info(f"Optimized {len(keywords)} keywords for {platform.value} with {intent.value} intent")
            return optimized_queries[:10]  # 限制查询数量
            
        except Exception as e:
            self.logger.error(f"Error optimizing keywords: {e}")
            return keywords  # 返回原始关键词作为备用
    
    def _build_multi_keyword_query(self, keywords: List[str], template: str, platform: SearchPlatform) -> str:
        """构建多关键词查询"""
        if platform == SearchPlatform.ADS:
            # 学术数据库使用布尔逻辑
            if len(keywords) >= 2:
                return template.format(keyword1=keywords[0], keyword2=keywords[1])
            else:
                return template.format(keyword=keywords[0])
        else:
            # 网络搜索使用自然语言组合
            combined_keywords = " ".join(keywords)
            return template.format(keyword=combined_keywords)
    
    def _apply_domain_optimization(self, queries: List[str], domain: str, platform: SearchPlatform) -> List[str]:
        """应用领域特定优化"""
        domain_modifiers = {
            'astrophysics': ['astronomy', 'cosmology', 'stellar', 'galactic'],
            'machine_learning': ['AI', 'neural networks', 'deep learning', 'algorithms'],
            'biology': ['molecular', 'cellular', 'genomics', 'proteomics'],
            'physics': ['quantum', 'particle', 'theoretical', 'experimental']
        }
        
        modifiers = domain_modifiers.get(domain.lower(), [])
        if not modifiers:
            return queries
        
        enhanced_queries = queries.copy()
        
        # 为前几个查询添加领域修饰符
        for i, query in enumerate(queries[:3]):
            if i < len(modifiers):
                if platform == SearchPlatform.ADS:
                    enhanced_query = f'({query}) AND (title:"{modifiers[i]}" OR abstract:"{modifiers[i]}")'
                else:
                    enhanced_query = f'{query} {modifiers[i]}'
                enhanced_queries.append(enhanced_query)
        
        return enhanced_queries

class SearchResultEvaluator:
    """搜索结果评估器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def evaluate_results(self, results: List[SearchResult], query_context: Dict[str, Any]) -> List[ScoredResult]:
        """评估搜索结果并返回评分结果"""
        scored_results = []
        
        for result in results:
            scores = self._calculate_all_scores(result, query_context)
            composite_score = self._calculate_composite_score(scores)
            
            scored_result = ScoredResult(
                result=result,
                composite_score=composite_score,
                score_breakdown=scores
            )
            scored_results.append(scored_result)
        
        # 按综合分数排序
        scored_results.sort(key=lambda x: x.composite_score, reverse=True)
        
        self.logger.info(f"Evaluated {len(results)} search results")
        return scored_results
    
    def _calculate_all_scores(self, result: SearchResult, context: Dict[str, Any]) -> Dict[str, float]:
        """计算所有评分维度"""
        return {
            'relevance': self._calculate_relevance_score(result, context.get('main_topic', '')),
            'quality': self._calculate_quality_score(result),
            'recency': self._calculate_recency_score(result),
            'authority': self._calculate_authority_score(result)
        }
    
    def _calculate_relevance_score(self, result: SearchResult, main_topic: str) -> float:
        """计算相关性分数"""
        if not main_topic:
            return 0.5
        
        topic_words = set(main_topic.lower().split())
        title_words = set(result.title.lower().split())
        content_words = set(result.content.lower().split())
        
        # 标题匹配权重更高
        title_overlap = len(topic_words.intersection(title_words)) / max(len(topic_words), 1)
        content_overlap = len(topic_words.intersection(content_words)) / max(len(topic_words), 1)
        
        relevance_score = (title_overlap * 0.7 + content_overlap * 0.3)
        return min(relevance_score, 1.0)
    
    def _calculate_quality_score(self, result: SearchResult) -> float:
        """计算质量分数"""
        quality_indicators = {
            'length': min(len(result.content) / 1000, 1.0),  # 内容长度
            'structure': 1.0 if any(marker in result.content.lower() 
                                  for marker in ['abstract', 'introduction', 'conclusion', 'method']) else 0.5,
            'completeness': 1.0 if len(result.content) > 200 else 0.5
        }
        
        return sum(quality_indicators.values()) / len(quality_indicators)
    
    def _calculate_recency_score(self, result: SearchResult) -> float:
        """计算时效性分数"""
        if not result.timestamp:
            return 0.5  # 默认中等分数
        
        try:
            # 简化的时效性计算
            current_year = 2024
            if '2024' in result.timestamp or '2023' in result.timestamp:
                return 1.0
            elif '2022' in result.timestamp or '2021' in result.timestamp:
                return 0.8
            elif '2020' in result.timestamp or '2019' in result.timestamp:
                return 0.6
            else:
                return 0.4
        except:
            return 0.5
    
    def _calculate_authority_score(self, result: SearchResult) -> float:
        """计算权威性分数"""
        authority_domains = [
            'edu', 'gov', 'org', 'nature.com', 'science.org', 'ieee.org',
            'arxiv.org', 'pubmed', 'scholar.google', 'researchgate'
        ]
        
        url_lower = result.url.lower()
        for domain in authority_domains:
            if domain in url_lower:
                return 1.0
        
        # 检查其他质量指标
        if any(indicator in result.title.lower() 
               for indicator in ['research', 'study', 'analysis', 'review']):
            return 0.8
        
        return 0.6  # 默认分数
    
    def _calculate_composite_score(self, scores: Dict[str, float]) -> float:
        """计算综合分数"""
        weights = {
            'relevance': 0.4,
            'quality': 0.3,
            'recency': 0.2,
            'authority': 0.1
        }
        
        composite = sum(scores[key] * weights[key] for key in weights if key in scores)
        return min(composite, 1.0)

class AdaptiveSearchManager:
    """自适应搜索管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.keyword_optimizer = EnhancedKeywordOptimizer()
        self.result_evaluator = SearchResultEvaluator()
        self.search_history = []
    
    def execute_adaptive_search(self, research_query, search_clients: Dict) -> Dict[str, List[ScoredResult]]:
        """执行自适应搜索策略"""
        self.logger.info(f"Starting adaptive search for: {research_query.main_topic}")
        
        search_results = {
            'web_results': [],
            'academic_results': [],
            'combined_results': []
        }
        
        try:
            # 第一阶段：广泛搜索建立基础
            foundation_results = self._execute_foundation_search(research_query, search_clients)
            search_results['web_results'].extend(foundation_results.get('web', []))
            search_results['academic_results'].extend(foundation_results.get('academic', []))
            
            # 分析第一阶段结果
            coverage_analysis = self._analyze_coverage(foundation_results, research_query)
            
            # 第二阶段：针对性深入搜索
            if coverage_analysis['needs_deep_dive']:
                deep_results = self._execute_deep_search(research_query, search_clients, coverage_analysis)
                search_results['academic_results'].extend(deep_results.get('academic', []))
            
            # 第三阶段：补充搜索填补空白
            if coverage_analysis['coverage_gaps']:
                gap_results = self._execute_gap_filling_search(research_query, search_clients, coverage_analysis)
                search_results['web_results'].extend(gap_results.get('web', []))
            
            # 合并和最终评估
            search_results['combined_results'] = self._consolidate_results(search_results)
            
            self.logger.info(f"Adaptive search completed. Total results: {len(search_results['combined_results'])}")
            return search_results
            
        except Exception as e:
            self.logger.error(f"Error in adaptive search: {e}")
            return search_results
    
    def _execute_foundation_search(self, query, clients: Dict) -> Dict[str, List[ScoredResult]]:
        """执行基础搜索阶段"""
        results = {'web': [], 'academic': []}
        
        # 使用通用关键词进行广泛搜索
        broad_keywords = query.keywords[:5]  # 使用前5个关键词
        
        # Web搜索
        if 'web' in clients:
            web_queries = self.keyword_optimizer.optimize_keywords_for_platform(
                broad_keywords, SearchPlatform.TAVILY, SearchIntent.BROAD
            )
            for web_query in web_queries[:3]:  # 限制查询数量
                try:
                    response = clients['web'].search(web_query, max_results=5)
                    if response.is_successful():
                        web_results = [SearchResult(
                            title=r.get('title', ''),
                            content=r.get('snippet', ''),
                            url=r.get('url', ''),
                            source='web'
                        ) for r in response.results]
                        
                        scored_results = self.result_evaluator.evaluate_results(
                            web_results, {'main_topic': query.main_topic}
                        )
                        results['web'].extend(scored_results)
                except Exception as e:
                    self.logger.warning(f"Web search failed for query '{web_query}': {e}")
        
        # 学术搜索
        if 'academic' in clients:
            academic_queries = self.keyword_optimizer.optimize_keywords_for_platform(
                broad_keywords, SearchPlatform.ADS, SearchIntent.BROAD
            )
            for academic_query in academic_queries[:3]:
                try:
                    response = clients['academic'].search_papers_enhanced(academic_query)
                    if response.is_successful():
                        academic_results = [SearchResult(
                            title=r.get('title', ''),
                            content=r.get('abstract', ''),
                            url=r.get('url', ''),
                            source='academic',
                            timestamp=r.get('publication_date', '')
                        ) for r in response.results]
                        
                        scored_results = self.result_evaluator.evaluate_results(
                            academic_results, {'main_topic': query.main_topic}
                        )
                        results['academic'].extend(scored_results)
                except Exception as e:
                    self.logger.warning(f"Academic search failed for query '{academic_query}': {e}")
        
        return results
    
    def _analyze_coverage(self, results: Dict, query) -> Dict[str, Any]:
        """分析搜索覆盖度"""
        analysis = {
            'needs_deep_dive': False,
            'coverage_gaps': [],
            'quality_assessment': 'medium'
        }
        
        total_results = len(results.get('web', [])) + len(results.get('academic', []))
        
        if total_results < 10:
            analysis['needs_deep_dive'] = True
            analysis['coverage_gaps'].append('insufficient_results')
        
        # 检查高质量结果比例
        high_quality_count = sum(1 for result_list in results.values() 
                               for result in result_list 
                               if result.composite_score > 0.7)
        
        if high_quality_count / max(total_results, 1) < 0.3:
            analysis['needs_deep_dive'] = True
            analysis['quality_assessment'] = 'low'
        
        return analysis
    
    def _execute_deep_search(self, query, clients: Dict, analysis: Dict) -> Dict[str, List[ScoredResult]]:
        """执行深度搜索"""
        results = {'academic': []}
        
        if 'academic' in clients:
            # 使用更具体的关键词和方法论导向的搜索
            specific_keywords = query.keywords[5:10] if len(query.keywords) > 5 else query.keywords
            
            methodological_queries = self.keyword_optimizer.optimize_keywords_for_platform(
                specific_keywords, SearchPlatform.ADS, SearchIntent.METHODOLOGICAL
            )
            
            for method_query in methodological_queries[:2]:
                try:
                    response = clients['academic'].search_papers_enhanced(method_query)
                    if response.is_successful():
                        academic_results = [SearchResult(
                            title=r.get('title', ''),
                            content=r.get('abstract', ''),
                            url=r.get('url', ''),
                            source='academic_deep',
                            timestamp=r.get('publication_date', '')
                        ) for r in response.results]
                        
                        scored_results = self.result_evaluator.evaluate_results(
                            academic_results, {'main_topic': query.main_topic}
                        )
                        results['academic'].extend(scored_results)
                except Exception as e:
                    self.logger.warning(f"Deep academic search failed: {e}")
        
        return results
    
    def _execute_gap_filling_search(self, query, clients: Dict, analysis: Dict) -> Dict[str, List[ScoredResult]]:
        """执行补充搜索"""
        results = {'web': []}
        
        if 'web' in clients:
            # 使用应用和挑战导向的搜索填补空白
            gap_keywords = query.keywords[:3]
            
            application_queries = self.keyword_optimizer.optimize_keywords_for_platform(
                gap_keywords, SearchPlatform.TAVILY, SearchIntent.APPLICATION
            )
            
            challenge_queries = self.keyword_optimizer.optimize_keywords_for_platform(
                gap_keywords, SearchPlatform.TAVILY, SearchIntent.CHALLENGES
            )
            
            all_gap_queries = application_queries[:2] + challenge_queries[:2]
            
            for gap_query in all_gap_queries:
                try:
                    response = clients['web'].search(gap_query, max_results=3)
                    if response.is_successful():
                        web_results = [SearchResult(
                            title=r.get('title', ''),
                            content=r.get('snippet', ''),
                            url=r.get('url', ''),
                            source='web_gap'
                        ) for r in response.results]
                        
                        scored_results = self.result_evaluator.evaluate_results(
                            web_results, {'main_topic': query.main_topic}
                        )
                        results['web'].extend(scored_results)
                except Exception as e:
                    self.logger.warning(f"Gap filling search failed: {e}")
        
        return results
    
    def _consolidate_results(self, search_results: Dict) -> List[ScoredResult]:
        """合并所有搜索结果"""
        all_results = []
        
        for result_type, results in search_results.items():
            if result_type != 'combined_results':
                all_results.extend(results)
        
        # 去重（基于URL）
        seen_urls = set()
        unique_results = []
        
        for result in all_results:
            if result.result.url not in seen_urls:
                seen_urls.add(result.result.url)
                unique_results.append(result)
        
        # 按综合分数排序
        unique_results.sort(key=lambda x: x.composite_score, reverse=True)
        
        return unique_results[:50]  # 返回前50个最佳结果
