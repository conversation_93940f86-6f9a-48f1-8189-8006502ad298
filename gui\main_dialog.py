# gui/main_dialog.py
#
# 主对话框 - 用户输入研究课题和选择执行模式

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Tuple, Dict, Any
import threading
import queue
import logging

# 导入研究计划存储管理器
from utils.research_plan_storage import ResearchPlanStorage


class MainDialog:
    """主对话框 - 用户输入界面"""
    
    def __init__(self):
        self.root = None
        self.result = None
        self.cancelled = False

        # 研究计划存储管理器
        self.storage = ResearchPlanStorage()
        self.logger = logging.getLogger(__name__)

        # 界面组件
        self.topic_entry = None
        self.mode_var = None
        self.history_listbox = None
        self.use_history_var = None
        self.selected_plan = None
        
    def show_dialog(self) -> Optional[Tuple[str, str, Optional[Dict[str, Any]]]]:
        """
        显示主对话框

        Returns:
            Optional[Tuple[str, str, Optional[Dict]]]: (topic, execution_mode, selected_plan) 或 None (如果取消)
        """
        self.root = tk.Tk()
        self.root.title("AI Research Assistant")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 设置默认焦点
        self.topic_entry.focus()
        
        # 运行对话框
        self.root.mainloop()
        
        if self.cancelled:
            return None
        return self.result
    
    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="AI Research Assistant", 
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 研究课题输入
        topic_label = ttk.Label(main_frame, text="Research Topic:")
        topic_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        self.topic_entry = tk.Text(
            main_frame, 
            height=4, 
            width=60,
            wrap=tk.WORD,
            font=("Arial", 10)
        )
        self.topic_entry.grid(row=2, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # 添加滚动条
        topic_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.topic_entry.yview)
        self.topic_entry.configure(yscrollcommand=topic_scrollbar.set)
        topic_scrollbar.grid(row=2, column=2, sticky=(tk.N, tk.S))

        # 历史研究计划选择
        self._create_history_section(main_frame, row=3)

        # 执行模式选择
        mode_label = ttk.Label(main_frame, text="Execution Mode:")
        mode_label.grid(row=5, column=0, sticky=tk.W, pady=(0, 5))
        
        self.mode_var = tk.StringVar(value="deep_research")
        
        # 模式选择框架
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Deep Research 选项
        deep_radio = ttk.Radiobutton(
            mode_frame,
            text="Deep Research",
            variable=self.mode_var,
            value="deep_research"
        )
        deep_radio.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        deep_desc = ttk.Label(
            mode_frame,
            text="Standard literature review and analysis",
            font=("Arial", 9),
            foreground="gray"
        )
        deep_desc.grid(row=1, column=0, sticky=tk.W, padx=(20, 0))
        
        # Full Analysis 选项
        full_radio = ttk.Radiobutton(
            mode_frame,
            text="Full Analysis",
            variable=self.mode_var,
            value="full_analysis"
        )
        full_radio.grid(row=0, column=1, sticky=tk.W)
        
        full_desc = ttk.Label(
            mode_frame,
            text="Complete analysis with innovation proposals",
            font=("Arial", 9),
            foreground="gray"
        )
        full_desc.grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=2, pady=(20, 0))
        
        # 确认按钮
        confirm_button = ttk.Button(
            button_frame,
            text="Start Research",
            command=self._on_confirm,
            width=15
        )
        confirm_button.grid(row=0, column=0, padx=(0, 10))
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_cancel,
            width=15
        )
        cancel_button.grid(row=0, column=1)
        
        # 绑定回车键
        self.root.bind('<Return>', lambda e: self._on_confirm())
        self.root.bind('<Escape>', lambda e: self._on_cancel())
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

    def _create_history_section(self, parent, row):
        """创建历史研究计划选择部分"""
        # 历史计划选择框架
        history_frame = ttk.LabelFrame(parent, text="Historical Research Plans", padding="10")
        history_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # 使用历史计划选项
        self.use_history_var = tk.BooleanVar()
        use_history_check = ttk.Checkbutton(
            history_frame,
            text="Use existing research plan",
            variable=self.use_history_var,
            command=self._on_use_history_changed
        )
        use_history_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # 历史计划列表框架
        list_frame = ttk.Frame(history_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 历史计划列表
        self.history_listbox = tk.Listbox(
            list_frame,
            height=4,
            width=80,
            font=("Arial", 9)
        )
        self.history_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 列表滚动条
        history_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.history_listbox.yview)
        self.history_listbox.configure(yscrollcommand=history_scrollbar.set)
        history_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 绑定选择事件
        self.history_listbox.bind('<<ListboxSelect>>', self._on_history_select)

        # 操作按钮框架
        history_button_frame = ttk.Frame(history_frame)
        history_button_frame.grid(row=2, column=0, sticky=tk.W)

        # 刷新按钮
        refresh_button = ttk.Button(
            history_button_frame,
            text="Refresh",
            command=self._refresh_history_plans,
            width=10
        )
        refresh_button.grid(row=0, column=0, padx=(0, 5))

        # 删除按钮
        delete_button = ttk.Button(
            history_button_frame,
            text="Delete",
            command=self._delete_selected_plan,
            width=10
        )
        delete_button.grid(row=0, column=1, padx=(0, 5))

        # 预览按钮
        preview_button = ttk.Button(
            history_button_frame,
            text="Preview",
            command=self._preview_selected_plan,
            width=10
        )
        preview_button.grid(row=0, column=2)

        # 配置网格权重
        history_frame.columnconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)

        # 初始加载历史计划
        self._refresh_history_plans()

        # 初始状态设置
        self._on_use_history_changed()

    def _on_use_history_changed(self):
        """使用历史计划选项变化事件"""
        use_history = self.use_history_var.get()

        # 启用/禁用历史计划列表
        if use_history:
            self.history_listbox.configure(state='normal')
            self.topic_entry.configure(state='disabled')
        else:
            self.history_listbox.configure(state='normal')  # 保持可选择以便预览
            self.topic_entry.configure(state='normal')
            self.selected_plan = None

    def _on_history_select(self, event):
        """历史计划选择事件"""
        selection = self.history_listbox.curselection()
        if selection:
            index = selection[0]
            plans = self.storage.load_research_plans()
            if 0 <= index < len(plans):
                self.selected_plan = plans[index]

                # 如果启用了使用历史计划，自动填充主题
                if self.use_history_var.get():
                    self.topic_entry.configure(state='normal')
                    self.topic_entry.delete("1.0", tk.END)
                    self.topic_entry.insert("1.0", self.selected_plan['main_topic'])
                    self.topic_entry.configure(state='disabled')

    def _refresh_history_plans(self):
        """刷新历史研究计划列表"""
        try:
            plans = self.storage.load_research_plans()

            # 清空列表
            self.history_listbox.delete(0, tk.END)

            # 添加计划到列表
            for plan in plans:
                summary = self.storage.get_plan_summary(plan)
                self.history_listbox.insert(tk.END, summary)

            if not plans:
                self.history_listbox.insert(tk.END, "No historical research plans found.")

        except Exception as e:
            self.logger.error(f"Error refreshing history plans: {e}")
            messagebox.showerror("Error", f"Failed to load historical plans: {e}")

    def _delete_selected_plan(self):
        """删除选中的研究计划"""
        selection = self.history_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a plan to delete.")
            return

        index = selection[0]
        plans = self.storage.load_research_plans()
        if 0 <= index < len(plans):
            plan = plans[index]

            # 确认删除
            result = messagebox.askyesno(
                "Confirm Delete",
                f"Are you sure you want to delete the research plan:\n'{plan['main_topic']}'?"
            )

            if result:
                try:
                    success = self.storage.delete_research_plan(plan['plan_id'])
                    if success:
                        messagebox.showinfo("Success", "Research plan deleted successfully.")
                        self._refresh_history_plans()
                        self.selected_plan = None
                    else:
                        messagebox.showerror("Error", "Failed to delete research plan.")
                except Exception as e:
                    self.logger.error(f"Error deleting plan: {e}")
                    messagebox.showerror("Error", f"Failed to delete research plan: {e}")

    def _preview_selected_plan(self):
        """预览选中的研究计划"""
        selection = self.history_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a plan to preview.")
            return

        index = selection[0]
        plans = self.storage.load_research_plans()
        if 0 <= index < len(plans):
            plan = plans[index]
            self._show_plan_preview(plan)

    def _show_plan_preview(self, plan: Dict[str, Any]):
        """显示研究计划预览窗口"""
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Research Plan Preview")
        preview_window.geometry("600x500")
        preview_window.transient(self.root)
        preview_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(preview_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(
            main_frame,
            text=f"Research Plan: {plan['main_topic']}",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # 创建文本框显示计划详情
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=("Arial", 10),
            state='disabled'
        )
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充计划内容
        content = self._format_plan_content(plan)
        text_widget.configure(state='normal')
        text_widget.insert("1.0", content)
        text_widget.configure(state='disabled')

        # 关闭按钮
        close_button = ttk.Button(
            main_frame,
            text="Close",
            command=preview_window.destroy
        )
        close_button.pack(pady=(20, 0))

    def _format_plan_content(self, plan: Dict[str, Any]) -> str:
        """格式化研究计划内容"""
        content = f"Research Topic: {plan['main_topic']}\n\n"

        content += f"Created: {plan.get('created_at', 'Unknown')}\n"
        content += f"Last Used: {plan.get('last_used', 'Unknown')}\n"
        content += f"Usage Count: {plan.get('usage_count', 0)}\n\n"

        content += "Sub-questions:\n"
        for i, question in enumerate(plan.get('sub_questions', []), 1):
            content += f"{i}. {question}\n"

        content += "\nGeneral Keywords:\n"
        keywords = plan.get('general_keywords', [])
        content += ", ".join(keywords) + "\n\n"

        content += "Sub-question Specific Keywords:\n"
        sub_keywords = plan.get('sub_question_keywords', {})
        for key, kw_list in sub_keywords.items():
            content += f"{key}: {', '.join(kw_list)}\n"

        return content

    def _on_confirm(self):
        """确认按钮点击事件"""
        # 检查是否使用历史计划
        if self.use_history_var.get():
            if not self.selected_plan:
                messagebox.showerror("Error", "Please select a historical research plan.")
                return

            topic = self.selected_plan['main_topic']
            plan_info = f" (Using historical plan: {self.selected_plan.get('usage_count', 0)} previous uses)"
        else:
            topic = self.topic_entry.get("1.0", tk.END).strip()
            if not topic:
                messagebox.showerror("Error", "Please enter a research topic.")
                return

            if len(topic) < 10:
                messagebox.showerror("Error", "Research topic should be at least 10 characters long.")
                return

            plan_info = " (New research plan)"

        execution_mode = self.mode_var.get()

        # 确认对话框
        confirm_msg = f"Research Topic: {topic[:100]}{'...' if len(topic) > 100 else ''}{plan_info}\n\n"
        confirm_msg += f"Execution Mode: {execution_mode.replace('_', ' ').title()}\n\n"
        confirm_msg += "Do you want to start the research?"

        if messagebox.askyesno("Confirm Research", confirm_msg):
            self.result = (topic, execution_mode, self.selected_plan if self.use_history_var.get() else None)
            self.root.quit()
            self.root.destroy()
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.cancelled = True
        self.root.quit()
        self.root.destroy()


def show_main_dialog() -> Optional[Tuple[str, str, Optional[Dict[str, Any]]]]:
    """
    显示主对话框的便捷函数

    Returns:
        Optional[Tuple[str, str, Optional[Dict]]]: (topic, execution_mode, selected_plan) 或 None (如果取消)
    """
    dialog = MainDialog()
    return dialog.show_dialog()


if __name__ == "__main__":
    # 测试对话框
    result = show_main_dialog()
    if result:
        topic, mode = result
        print(f"Topic: {topic}")
        print(f"Mode: {mode}")
    else:
        print("Cancelled")
