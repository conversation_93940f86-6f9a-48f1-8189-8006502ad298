# gui/main_dialog.py
#
# 主对话框 - 用户输入研究课题和选择执行模式

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Tuple
import threading
import queue


class MainDialog:
    """主对话框 - 用户输入界面"""
    
    def __init__(self):
        self.root = None
        self.result = None
        self.cancelled = False
        
    def show_dialog(self) -> Optional[Tuple[str, str]]:
        """
        显示主对话框
        
        Returns:
            Optional[Tuple[str, str]]: (topic, execution_mode) 或 None (如果取消)
        """
        self.root = tk.Tk()
        self.root.title("AI Research Assistant")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 设置默认焦点
        self.topic_entry.focus()
        
        # 运行对话框
        self.root.mainloop()
        
        if self.cancelled:
            return None
        return self.result
    
    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="AI Research Assistant", 
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 研究课题输入
        topic_label = ttk.Label(main_frame, text="Research Topic:")
        topic_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        self.topic_entry = tk.Text(
            main_frame, 
            height=4, 
            width=60,
            wrap=tk.WORD,
            font=("Arial", 10)
        )
        self.topic_entry.grid(row=2, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # 添加滚动条
        topic_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.topic_entry.yview)
        self.topic_entry.configure(yscrollcommand=topic_scrollbar.set)
        topic_scrollbar.grid(row=2, column=2, sticky=(tk.N, tk.S))
        
        # 执行模式选择
        mode_label = ttk.Label(main_frame, text="Execution Mode:")
        mode_label.grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        self.mode_var = tk.StringVar(value="deep_research")
        
        # 模式选择框架
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Deep Research 选项
        deep_radio = ttk.Radiobutton(
            mode_frame,
            text="Deep Research",
            variable=self.mode_var,
            value="deep_research"
        )
        deep_radio.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        deep_desc = ttk.Label(
            mode_frame,
            text="Standard literature review and analysis",
            font=("Arial", 9),
            foreground="gray"
        )
        deep_desc.grid(row=1, column=0, sticky=tk.W, padx=(20, 0))
        
        # Full Analysis 选项
        full_radio = ttk.Radiobutton(
            mode_frame,
            text="Full Analysis",
            variable=self.mode_var,
            value="full_analysis"
        )
        full_radio.grid(row=0, column=1, sticky=tk.W)
        
        full_desc = ttk.Label(
            mode_frame,
            text="Complete analysis with innovation proposals",
            font=("Arial", 9),
            foreground="gray"
        )
        full_desc.grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(20, 0))
        
        # 确认按钮
        confirm_button = ttk.Button(
            button_frame,
            text="Start Research",
            command=self._on_confirm,
            width=15
        )
        confirm_button.grid(row=0, column=0, padx=(0, 10))
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_cancel,
            width=15
        )
        cancel_button.grid(row=0, column=1)
        
        # 绑定回车键
        self.root.bind('<Return>', lambda e: self._on_confirm())
        self.root.bind('<Escape>', lambda e: self._on_cancel())
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
    def _on_confirm(self):
        """确认按钮点击事件"""
        topic = self.topic_entry.get("1.0", tk.END).strip()
        
        if not topic:
            messagebox.showerror("Error", "Please enter a research topic.")
            return
        
        if len(topic) < 10:
            messagebox.showerror("Error", "Research topic should be at least 10 characters long.")
            return
        
        execution_mode = self.mode_var.get()
        
        # 确认对话框
        confirm_msg = f"Research Topic: {topic[:100]}{'...' if len(topic) > 100 else ''}\n\n"
        confirm_msg += f"Execution Mode: {execution_mode.replace('_', ' ').title()}\n\n"
        confirm_msg += "Do you want to start the research?"
        
        if messagebox.askyesno("Confirm Research", confirm_msg):
            self.result = (topic, execution_mode)
            self.root.quit()
            self.root.destroy()
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.cancelled = True
        self.root.quit()
        self.root.destroy()


def show_main_dialog() -> Optional[Tuple[str, str]]:
    """
    显示主对话框的便捷函数
    
    Returns:
        Optional[Tuple[str, str]]: (topic, execution_mode) 或 None (如果取消)
    """
    dialog = MainDialog()
    return dialog.show_dialog()


if __name__ == "__main__":
    # 测试对话框
    result = show_main_dialog()
    if result:
        topic, mode = result
        print(f"Topic: {topic}")
        print(f"Mode: {mode}")
    else:
        print("Cancelled")
