# gui/planner_review_dialog.py
#
# PlannerAgent结果预览和确认对话框

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Optional, Tuple
import threading
import queue


class PlannerReviewDialog:
    """PlannerAgent结果预览对话框"""
    
    def __init__(self, topic: str, sub_questions: List[str], keywords: List[str], 
                 estimated_papers: int = 0, estimated_time: str = ""):
        self.topic = topic
        self.sub_questions = sub_questions.copy()
        self.keywords = keywords.copy()
        self.estimated_papers = estimated_papers
        self.estimated_time = estimated_time
        
        self.root = None
        self.result = None
        self.action = None  # 'confirm', 'modify', 'regenerate', 'cancel'
        
        # 界面组件
        self.questions_listbox = None
        self.keywords_listbox = None
        self.question_entry = None
        self.keyword_entry = None
        
    def show_dialog(self) -> Optional[Tuple[str, List[str], List[str]]]:
        """
        显示预览对话框
        
        Returns:
            Optional[Tuple[str, List[str], List[str]]]: (action, sub_questions, keywords) 或 None
        """
        self.root = tk.Tk()
        self.root.title("Research Plan Review")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置窗口居中
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 运行对话框
        self.root.mainloop()
        
        if self.action == 'cancel':
            return None
        
        return (self.action, self.sub_questions, self.keywords)
    
    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="Research Plan Review", 
            font=("Arial", 14, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 15))
        
        # 研究主题显示
        topic_frame = ttk.LabelFrame(main_frame, text="Research Topic", padding="10")
        topic_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        
        topic_text = tk.Text(topic_frame, height=2, wrap=tk.WORD, font=("Arial", 10))
        topic_text.insert("1.0", self.topic)
        topic_text.config(state=tk.DISABLED)
        topic_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 预估信息
        if self.estimated_papers > 0 or self.estimated_time:
            estimate_frame = ttk.LabelFrame(main_frame, text="Estimates", padding="10")
            estimate_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
            
            if self.estimated_papers > 0:
                papers_label = ttk.Label(estimate_frame, text=f"Estimated Papers: {self.estimated_papers}")
                papers_label.grid(row=0, column=0, sticky=tk.W)
            
            if self.estimated_time:
                time_label = ttk.Label(estimate_frame, text=f"Estimated Time: {self.estimated_time}")
                time_label.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 子问题部分
        questions_frame = ttk.LabelFrame(main_frame, text="Sub-Questions", padding="10")
        questions_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        
        # 子问题列表
        questions_list_frame = ttk.Frame(questions_frame)
        questions_list_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.questions_listbox = tk.Listbox(
            questions_list_frame, 
            height=8,
            font=("Arial", 9),
            selectmode=tk.SINGLE
        )
        self.questions_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        questions_scrollbar = ttk.Scrollbar(questions_list_frame, orient="vertical", command=self.questions_listbox.yview)
        self.questions_listbox.configure(yscrollcommand=questions_scrollbar.set)
        questions_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 填充子问题
        for i, question in enumerate(self.sub_questions):
            self.questions_listbox.insert(tk.END, f"{i+1}. {question}")
        
        # 子问题编辑
        question_edit_frame = ttk.Frame(questions_frame)
        question_edit_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.question_entry = tk.Text(question_edit_frame, height=2, wrap=tk.WORD, font=("Arial", 9))
        self.question_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        question_buttons_frame = ttk.Frame(question_edit_frame)
        question_buttons_frame.grid(row=1, column=0, pady=(5, 0))
        
        ttk.Button(question_buttons_frame, text="Edit Selected", command=self._edit_question).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(question_buttons_frame, text="Add New", command=self._add_question).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(question_buttons_frame, text="Delete Selected", command=self._delete_question).grid(row=0, column=2)
        
        # 关键词部分
        keywords_frame = ttk.LabelFrame(main_frame, text="Keywords", padding="10")
        keywords_frame.grid(row=3, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15), padx=(15, 0))
        
        # 关键词列表
        keywords_list_frame = ttk.Frame(keywords_frame)
        keywords_list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.keywords_listbox = tk.Listbox(
            keywords_list_frame, 
            height=8,
            font=("Arial", 9),
            selectmode=tk.SINGLE
        )
        self.keywords_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        keywords_scrollbar = ttk.Scrollbar(keywords_list_frame, orient="vertical", command=self.keywords_listbox.yview)
        self.keywords_listbox.configure(yscrollcommand=keywords_scrollbar.set)
        keywords_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 填充关键词
        for keyword in self.keywords:
            self.keywords_listbox.insert(tk.END, keyword)
        
        # 关键词编辑
        keyword_edit_frame = ttk.Frame(keywords_frame)
        keyword_edit_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.keyword_entry = ttk.Entry(keyword_edit_frame, font=("Arial", 9))
        self.keyword_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        keyword_buttons_frame = ttk.Frame(keyword_edit_frame)
        keyword_buttons_frame.grid(row=1, column=0, pady=(5, 0))
        
        ttk.Button(keyword_buttons_frame, text="Add", command=self._add_keyword).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(keyword_buttons_frame, text="Delete Selected", command=self._delete_keyword).grid(row=0, column=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(20, 0))
        
        # 主要按钮
        ttk.Button(button_frame, text="Confirm & Continue", command=self._on_confirm, width=18).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="Regenerate Plan", command=self._on_regenerate, width=18).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel, width=18).grid(row=0, column=2)
        
        # 绑定事件
        self.questions_listbox.bind('<<ListboxSelect>>', self._on_question_select)
        self.keywords_listbox.bind('<<ListboxSelect>>', self._on_keyword_select)
        self.root.bind('<Escape>', lambda e: self._on_cancel())
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        questions_frame.columnconfigure(0, weight=1)
        questions_frame.rowconfigure(0, weight=1)
        questions_list_frame.columnconfigure(0, weight=1)
        questions_list_frame.rowconfigure(0, weight=1)
        question_edit_frame.columnconfigure(0, weight=1)
        
        keywords_frame.columnconfigure(0, weight=1)
        keywords_frame.rowconfigure(0, weight=1)
        keywords_list_frame.columnconfigure(0, weight=1)
        keywords_list_frame.rowconfigure(0, weight=1)
        keyword_edit_frame.columnconfigure(0, weight=1)
        
        topic_frame.columnconfigure(0, weight=1)
        if hasattr(self, 'estimate_frame'):
            estimate_frame.columnconfigure(0, weight=1)
    
    def _on_question_select(self, event):
        """子问题选择事件"""
        selection = self.questions_listbox.curselection()
        if selection:
            index = selection[0]
            question = self.sub_questions[index]
            self.question_entry.delete("1.0", tk.END)
            self.question_entry.insert("1.0", question)
    
    def _on_keyword_select(self, event):
        """关键词选择事件"""
        selection = self.keywords_listbox.curselection()
        if selection:
            index = selection[0]
            keyword = self.keywords[index]
            self.keyword_entry.delete(0, tk.END)
            self.keyword_entry.insert(0, keyword)
    
    def _edit_question(self):
        """编辑选中的子问题"""
        selection = self.questions_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a question to edit.")
            return
        
        index = selection[0]
        new_question = self.question_entry.get("1.0", tk.END).strip()
        
        if not new_question:
            messagebox.showerror("Error", "Question cannot be empty.")
            return
        
        self.sub_questions[index] = new_question
        self.questions_listbox.delete(index)
        self.questions_listbox.insert(index, f"{index+1}. {new_question}")
        self.questions_listbox.selection_set(index)
    
    def _add_question(self):
        """添加新的子问题"""
        new_question = self.question_entry.get("1.0", tk.END).strip()
        
        if not new_question:
            messagebox.showerror("Error", "Question cannot be empty.")
            return
        
        self.sub_questions.append(new_question)
        index = len(self.sub_questions) - 1
        self.questions_listbox.insert(tk.END, f"{index+1}. {new_question}")
        self.question_entry.delete("1.0", tk.END)
    
    def _delete_question(self):
        """删除选中的子问题"""
        selection = self.questions_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a question to delete.")
            return
        
        if len(self.sub_questions) <= 1:
            messagebox.showerror("Error", "At least one question is required.")
            return
        
        index = selection[0]
        del self.sub_questions[index]
        self.questions_listbox.delete(index)
        
        # 重新编号
        self.questions_listbox.delete(0, tk.END)
        for i, question in enumerate(self.sub_questions):
            self.questions_listbox.insert(tk.END, f"{i+1}. {question}")
        
        self.question_entry.delete("1.0", tk.END)
    
    def _add_keyword(self):
        """添加新关键词"""
        new_keyword = self.keyword_entry.get().strip()
        
        if not new_keyword:
            messagebox.showerror("Error", "Keyword cannot be empty.")
            return
        
        if new_keyword in self.keywords:
            messagebox.showwarning("Warning", "Keyword already exists.")
            return
        
        self.keywords.append(new_keyword)
        self.keywords_listbox.insert(tk.END, new_keyword)
        self.keyword_entry.delete(0, tk.END)
    
    def _delete_keyword(self):
        """删除选中的关键词"""
        selection = self.keywords_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a keyword to delete.")
            return
        
        index = selection[0]
        del self.keywords[index]
        self.keywords_listbox.delete(index)
        self.keyword_entry.delete(0, tk.END)
    
    def _on_confirm(self):
        """确认继续"""
        if not self.sub_questions:
            messagebox.showerror("Error", "At least one sub-question is required.")
            return
        
        if not self.keywords:
            messagebox.showerror("Error", "At least one keyword is required.")
            return
        
        self.action = 'confirm'
        self.root.quit()
        self.root.destroy()
    
    def _on_regenerate(self):
        """重新生成计划"""
        if messagebox.askyesno("Confirm", "This will regenerate the entire research plan. Continue?"):
            self.action = 'regenerate'
            self.root.quit()
            self.root.destroy()
    
    def _on_cancel(self):
        """取消"""
        self.action = 'cancel'
        self.root.quit()
        self.root.destroy()


def show_planner_review_dialog(topic: str, sub_questions: List[str], keywords: List[str], 
                              estimated_papers: int = 0, estimated_time: str = "") -> Optional[Tuple[str, List[str], List[str]]]:
    """
    显示PlannerAgent结果预览对话框的便捷函数
    
    Returns:
        Optional[Tuple[str, List[str], List[str]]]: (action, sub_questions, keywords) 或 None
    """
    dialog = PlannerReviewDialog(topic, sub_questions, keywords, estimated_papers, estimated_time)
    return dialog.show_dialog()


if __name__ == "__main__":
    # 测试对话框
    test_questions = [
        "What are the current applications of deep learning in astrophysics?",
        "How do neural networks improve astronomical data analysis?",
        "What are the challenges in applying AI to astrophysical research?"
    ]
    test_keywords = ["deep learning", "astrophysics", "neural networks", "data analysis", "machine learning"]
    
    result = show_planner_review_dialog(
        "Deep Learning Applications in Astrophysics",
        test_questions,
        test_keywords,
        150,
        "25-35 minutes"
    )
    
    if result:
        action, questions, keywords = result
        print(f"Action: {action}")
        print(f"Questions: {len(questions)}")
        print(f"Keywords: {len(keywords)}")
    else:
        print("Cancelled")
