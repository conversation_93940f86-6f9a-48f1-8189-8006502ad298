# gui/planner_review_dialog.py
#
# PlannerAgent结果预览和确认对话框

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Optional, Tuple
import threading
import queue


class PlannerReviewDialog:
    """PlannerAgent结果预览对话框"""

    def __init__(self, topic: str, sub_questions: List[str], keywords: List[str],
                 estimated_papers: int = 0, estimated_time: str = "",
                 sub_question_keywords: Optional[Dict[str, List[str]]] = None):
        self.topic = topic
        self.sub_questions = sub_questions.copy()
        self.keywords = keywords.copy()
        self.estimated_papers = estimated_papers
        self.estimated_time = estimated_time
        self.sub_question_keywords = sub_question_keywords or {}

        self.root = None
        self.result = None
        self.action = None  # 'confirm', 'modify', 'regenerate', 'cancel'

        # 界面组件
        self.plan_display = None
        self.question_entry = None
        self.keyword_entry = None
        
    def show_dialog(self) -> Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]:
        """
        显示预览对话框

        Returns:
            Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]: (action, sub_questions, keywords, sub_question_keywords) 或 None
        """
        self.root = tk.Tk()
        self.root.title("Research Plan Review")
        self.root.geometry("900x800")
        self.root.resizable(True, True)

        # 设置窗口居中
        self._center_window()

        # 创建界面
        self._create_widgets()

        # 运行对话框
        self.root.mainloop()

        if self.action == 'cancel':
            return None

        return (self.action, self.sub_questions, self.keywords, self.sub_question_keywords)
    
    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(
            main_frame,
            text="Research Plan Review",
            font=("Arial", 14, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))

        # 预估信息
        if self.estimated_papers > 0 or self.estimated_time:
            estimate_frame = ttk.LabelFrame(main_frame, text="Estimates", padding="10")
            estimate_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

            if self.estimated_papers > 0:
                papers_label = ttk.Label(estimate_frame, text=f"Estimated Papers: {self.estimated_papers}")
                papers_label.grid(row=0, column=0, sticky=tk.W)

            if self.estimated_time:
                time_label = ttk.Label(estimate_frame, text=f"Estimated Time: {self.estimated_time}")
                time_label.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        # 研究计划显示区域
        plan_frame = ttk.LabelFrame(main_frame, text="Research Plan", padding="10")
        plan_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 创建带滚动条的文本显示区域
        plan_display_frame = ttk.Frame(plan_frame)
        plan_display_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.plan_display = tk.Text(
            plan_display_frame,
            height=20,
            wrap=tk.WORD,
            font=("Arial", 10),
            state=tk.DISABLED,
            bg="#f8f9fa",
            relief=tk.FLAT,
            padx=10,
            pady=10
        )
        self.plan_display.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 添加滚动条
        plan_scrollbar = ttk.Scrollbar(plan_display_frame, orient="vertical", command=self.plan_display.yview)
        self.plan_display.configure(yscrollcommand=plan_scrollbar.set)
        plan_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 填充研究计划内容
        self._populate_plan_display()

        # 编辑区域
        edit_frame = ttk.LabelFrame(main_frame, text="Edit Plan", padding="10")
        edit_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # 编辑说明
        edit_label = ttk.Label(edit_frame, text="Click on the plan above to edit. Changes will be reflected in real-time.",
                              font=("Arial", 9), foreground="gray")
        edit_label.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # 子问题编辑
        ttk.Label(edit_frame, text="Edit Sub-question:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W)
        self.question_entry = tk.Text(edit_frame, height=3, wrap=tk.WORD, font=("Arial", 9))
        self.question_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 10))

        question_buttons_frame = ttk.Frame(edit_frame)
        question_buttons_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))

        ttk.Button(question_buttons_frame, text="Update Question", command=self._update_question).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(question_buttons_frame, text="Add New Question", command=self._add_question).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(question_buttons_frame, text="Delete Question", command=self._delete_question).grid(row=0, column=2)

        # 关键词编辑
        ttk.Label(edit_frame, text="Edit Keywords (comma-separated):", font=("Arial", 9, "bold")).grid(row=4, column=0, sticky=tk.W)
        self.keyword_entry = tk.Text(edit_frame, height=2, wrap=tk.WORD, font=("Arial", 9))
        self.keyword_entry.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 10))

        keyword_buttons_frame = ttk.Frame(edit_frame)
        keyword_buttons_frame.grid(row=6, column=0, columnspan=2)

        ttk.Button(keyword_buttons_frame, text="Update Keywords", command=self._update_keywords).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(keyword_buttons_frame, text="Reset Keywords", command=self._reset_keywords).grid(row=0, column=1)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(20, 0))

        # 主要按钮
        ttk.Button(button_frame, text="Confirm & Continue", command=self._on_confirm, width=18).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="Regenerate Plan", command=self._on_regenerate, width=18).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel, width=18).grid(row=0, column=2)

        # 绑定事件
        self.plan_display.bind('<Button-1>', self._on_plan_click)
        self.root.bind('<Escape>', lambda e: self._on_cancel())

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)  # 研究计划显示区域

        plan_frame.columnconfigure(0, weight=1)
        plan_frame.rowconfigure(0, weight=1)
        plan_display_frame.columnconfigure(0, weight=1)
        plan_display_frame.rowconfigure(0, weight=1)

        edit_frame.columnconfigure(0, weight=1)
        edit_frame.columnconfigure(1, weight=1)

    def _populate_plan_display(self):
        """填充研究计划显示内容"""
        self.plan_display.config(state=tk.NORMAL)
        self.plan_display.delete("1.0", tk.END)

        # 研究课题
        self.plan_display.insert(tk.END, "研究课题：", "title")
        self.plan_display.insert(tk.END, f"{self.topic}\n\n", "topic")

        # 总体关键词
        self.plan_display.insert(tk.END, "总体关键词：", "subtitle")
        keywords_text = ", ".join(self.keywords)
        self.plan_display.insert(tk.END, f"{keywords_text}\n\n", "keywords")

        # 子问题和相关关键词
        for i, question in enumerate(self.sub_questions):
            # 子问题标题
            self.plan_display.insert(tk.END, f"子问题{i+1}：", "subtitle")
            self.plan_display.insert(tk.END, f"{question}\n", "question")

            # 相关关键词
            sub_keywords = self._get_keywords_for_question(i)
            if sub_keywords:
                self.plan_display.insert(tk.END, "相关关键词：", "keyword_label")
                keywords_text = ", ".join(sub_keywords)
                self.plan_display.insert(tk.END, f"{keywords_text}\n\n", "sub_keywords")
            else:
                self.plan_display.insert(tk.END, "相关关键词：", "keyword_label")
                self.plan_display.insert(tk.END, "暂无特定关键词\n\n", "sub_keywords")

        # 配置文本样式
        self._configure_text_styles()
        self.plan_display.config(state=tk.DISABLED)

    def _configure_text_styles(self):
        """配置文本样式"""
        self.plan_display.tag_configure("title", font=("Arial", 12, "bold"), foreground="#2c3e50")
        self.plan_display.tag_configure("topic", font=("Arial", 11), foreground="#34495e", lmargin1=20, lmargin2=20)
        self.plan_display.tag_configure("subtitle", font=("Arial", 10, "bold"), foreground="#3498db")
        self.plan_display.tag_configure("keywords", font=("Arial", 10), foreground="#27ae60", lmargin1=20, lmargin2=20)
        self.plan_display.tag_configure("question", font=("Arial", 10), foreground="#2c3e50", lmargin1=20, lmargin2=20)
        self.plan_display.tag_configure("keyword_label", font=("Arial", 9, "bold"), foreground="#8e44ad", lmargin1=20)
        self.plan_display.tag_configure("sub_keywords", font=("Arial", 9), foreground="#9b59b6", lmargin1=40, lmargin2=40)

    def _get_keywords_for_question(self, question_index: int) -> List[str]:
        """获取特定子问题的关键词"""
        if self.sub_question_keywords:
            key = f"sub_question_{question_index + 1}"
            return self.sub_question_keywords.get(key, [])
        return []

    def _on_plan_click(self, event):
        """点击研究计划区域的事件处理"""
        # 获取点击位置的行号
        index = self.plan_display.index(f"@{event.x},{event.y}")
        line_start = self.plan_display.index(f"{index} linestart")
        line_end = self.plan_display.index(f"{index} lineend")
        line_text = self.plan_display.get(line_start, line_end)

        # 检查是否点击了子问题
        if line_text.startswith("子问题"):
            question_num = int(line_text.split("：")[0].replace("子问题", "")) - 1
            if 0 <= question_num < len(self.sub_questions):
                self._select_question_for_edit(question_num)

    def _select_question_for_edit(self, question_index: int):
        """选择子问题进行编辑"""
        self.selected_question_index = question_index
        question = self.sub_questions[question_index]

        # 填充编辑区域
        self.question_entry.delete("1.0", tk.END)
        self.question_entry.insert("1.0", question)

        # 填充关键词编辑区域
        keywords = self._get_keywords_for_question(question_index)
        self.keyword_entry.delete("1.0", tk.END)
        self.keyword_entry.insert("1.0", ", ".join(keywords))

    def _update_question(self):
        """更新选中的子问题"""
        if not hasattr(self, 'selected_question_index'):
            messagebox.showwarning("Warning", "Please click on a question in the plan above to select it for editing.")
            return

        new_question = self.question_entry.get("1.0", tk.END).strip()

        if not new_question:
            messagebox.showerror("Error", "Question cannot be empty.")
            return

        # 确保问题以问号结尾
        if not new_question.endswith('?'):
            new_question += '?'

        self.sub_questions[self.selected_question_index] = new_question
        self._populate_plan_display()
        messagebox.showinfo("Success", "Question updated successfully!")

    def _add_question(self):
        """添加新的子问题"""
        new_question = self.question_entry.get("1.0", tk.END).strip()

        if not new_question:
            messagebox.showerror("Error", "Question cannot be empty.")
            return

        # 确保问题以问号结尾
        if not new_question.endswith('?'):
            new_question += '?'

        self.sub_questions.append(new_question)
        self.question_entry.delete("1.0", tk.END)
        self._populate_plan_display()
        messagebox.showinfo("Success", "New question added successfully!")

    def _delete_question(self):
        """删除选中的子问题"""
        if not hasattr(self, 'selected_question_index'):
            messagebox.showwarning("Warning", "Please click on a question in the plan above to select it for deletion.")
            return

        if len(self.sub_questions) <= 1:
            messagebox.showerror("Error", "At least one question is required.")
            return

        question_to_delete = self.sub_questions[self.selected_question_index]
        confirm = messagebox.askyesno("Confirm Deletion", f"Are you sure you want to delete this question?\n\n{question_to_delete}")

        if confirm:
            del self.sub_questions[self.selected_question_index]

            # 删除对应的子问题关键词映射
            key_to_delete = f"sub_question_{self.selected_question_index + 1}"
            if key_to_delete in self.sub_question_keywords:
                del self.sub_question_keywords[key_to_delete]

            # 重新编号子问题关键词映射
            self._renumber_sub_question_keywords()

            self.question_entry.delete("1.0", tk.END)
            self.keyword_entry.delete("1.0", tk.END)
            delattr(self, 'selected_question_index')
            self._populate_plan_display()
            messagebox.showinfo("Success", "Question deleted successfully!")

    def _update_keywords(self):
        """更新关键词"""
        if not hasattr(self, 'selected_question_index'):
            # 更新总体关键词
            keywords_text = self.keyword_entry.get("1.0", tk.END).strip()
            if keywords_text:
                new_keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
                self.keywords = new_keywords
                self._populate_plan_display()
                messagebox.showinfo("Success", "General keywords updated successfully!")
            else:
                messagebox.showerror("Error", "Keywords cannot be empty.")
        else:
            # 更新特定子问题的关键词
            keywords_text = self.keyword_entry.get("1.0", tk.END).strip()
            if keywords_text:
                new_keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
                key = f"sub_question_{self.selected_question_index + 1}"
                self.sub_question_keywords[key] = new_keywords
                self._populate_plan_display()
                messagebox.showinfo("Success", "Sub-question keywords updated successfully!")
            else:
                # 清空该子问题的关键词
                key = f"sub_question_{self.selected_question_index + 1}"
                self.sub_question_keywords[key] = []
                self._populate_plan_display()
                messagebox.showinfo("Success", "Sub-question keywords cleared!")

    def _reset_keywords(self):
        """重置关键词到原始状态"""
        if hasattr(self, 'selected_question_index'):
            key = f"sub_question_{self.selected_question_index + 1}"
            original_keywords = self._get_keywords_for_question(self.selected_question_index)
            self.keyword_entry.delete("1.0", tk.END)
            self.keyword_entry.insert("1.0", ", ".join(original_keywords))
        else:
            # 重置总体关键词
            self.keyword_entry.delete("1.0", tk.END)
            self.keyword_entry.insert("1.0", ", ".join(self.keywords))

    def _renumber_sub_question_keywords(self):
        """重新编号子问题关键词映射"""
        new_mapping = {}
        old_keys = sorted([k for k in self.sub_question_keywords.keys() if k.startswith("sub_question_")])

        for i, old_key in enumerate(old_keys):
            new_key = f"sub_question_{i + 1}"
            if i < len(self.sub_questions):  # 确保不超出子问题数量
                new_mapping[new_key] = self.sub_question_keywords[old_key]

        self.sub_question_keywords = new_mapping
    
    def _on_confirm(self):
        """确认继续"""
        if not self.sub_questions:
            messagebox.showerror("Error", "At least one sub-question is required.")
            return

        if not self.keywords:
            messagebox.showerror("Error", "At least one keyword is required.")
            return

        self.action = 'confirm'
        self.root.quit()
        self.root.destroy()
    
    def _on_regenerate(self):
        """重新生成计划"""
        if messagebox.askyesno("Confirm", "This will regenerate the entire research plan. Continue?"):
            self.action = 'regenerate'
            self.root.quit()
            self.root.destroy()
    
    def _on_cancel(self):
        """取消"""
        self.action = 'cancel'
        self.root.quit()
        self.root.destroy()


def show_planner_review_dialog(topic: str, sub_questions: List[str], keywords: List[str],
                              estimated_papers: int = 0, estimated_time: str = "",
                              sub_question_keywords: Optional[Dict[str, List[str]]] = None) -> Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]:
    """
    显示PlannerAgent结果预览对话框的便捷函数

    Returns:
        Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]: (action, sub_questions, keywords, sub_question_keywords) 或 None
    """
    dialog = PlannerReviewDialog(topic, sub_questions, keywords, estimated_papers, estimated_time, sub_question_keywords)
    return dialog.show_dialog()


if __name__ == "__main__":
    # 测试对话框
    test_questions = [
        "What are the current applications of deep learning in astrophysics?",
        "How do neural networks improve astronomical data analysis?",
        "What are the challenges in applying AI to astrophysical research?"
    ]
    test_keywords = ["deep learning", "astrophysics", "neural networks", "data analysis", "machine learning"]
    
    result = show_planner_review_dialog(
        "Deep Learning Applications in Astrophysics",
        test_questions,
        test_keywords,
        150,
        "25-35 minutes"
    )
    
    if result:
        action, questions, keywords = result
        print(f"Action: {action}")
        print(f"Questions: {len(questions)}")
        print(f"Keywords: {len(keywords)}")
    else:
        print("Cancelled")
