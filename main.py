# main.py
#
# AI驱动的科研助理 - 主程序入口
# 负责调度和编排整个研究流程

import sys
import logging
from typing import List, Dict, Any
from datetime import datetime

# 导入配置和工具
from config import config
from utils import setup_logging, get_logger, ProgressLogger, ReportGenerator

# 导入客户端
from clients import ADSClient, WebSearchClient, LLMClient

# 导入代理
from agents import PlannerAgent, SynthesizerAgent, WriterAgent

# 导入模型
from models import ResearchSession, Paper, WebSearchResult


class ResearchAssistant:
    """AI驱动的科研助理主类"""
    
    def __init__(self):
        """初始化科研助理"""
        # 设置日志
        setup_logging()
        self.logger = get_logger(__name__)
        
        self.logger.info("初始化AI驱动的科研助理...")
        self.logger.info(f"执行模式: {config.EXECUTION_MODE}")
        
        # 初始化组件
        try:
            self._initialize_components()
            self.logger.info("所有组件初始化成功")
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            raise
    
    def _initialize_components(self):
        """初始化所有组件"""
        # 验证配置
        config.validate_config()
        
        # 初始化客户端
        self.ads_client = ADSClient()
        self.web_client = WebSearchClient()
        self.llm_client = LLMClient()
        
        # 初始化代理
        self.planner = PlannerAgent(self.llm_client)
        self.synthesizer = SynthesizerAgent(self.llm_client)
        self.writer = WriterAgent(self.llm_client)
        
        # 初始化报告生成器
        self.report_generator = ReportGenerator()
        
        # 测试连接
        self._test_connections()
    
    def _test_connections(self):
        """测试所有外部连接"""
        self.logger.info("测试外部API连接...")
        
        # 测试ADS连接
        if not self.ads_client.test_connection():
            self.logger.warning("ADS API连接测试失败")
        else:
            self.logger.info("ADS API连接正常")
        
        # 测试网络搜索连接
        if not self.web_client.test_connection():
            self.logger.warning("网络搜索API连接测试失败")
        else:
            self.logger.info("网络搜索API连接正常")
        
        # 测试LLM连接
        if not self.llm_client.test_connection():
            self.logger.error("LLM API连接测试失败")
            raise ConnectionError("LLM API连接失败，无法继续执行")
        else:
            self.logger.info("LLM API连接正常")
    
    def run_research(self, topic: str) -> ResearchSession:
        """
        执行完整的研究流程
        
        Args:
            topic: 研究课题
            
        Returns:
            ResearchSession: 研究会话对象
        """
        self.logger.info(f"开始研究课题: {topic}")
        
        # 创建研究会话
        session = ResearchSession(
            query=self.planner.generate_research_plan(topic)
        )
        session.update_status("planning_completed")
        
        try:
            # 步骤1: 双轨信息采集
            self._collect_information(session)
            session.update_status("information_collected")
            
            # 步骤2: 论文精读分析
            self._analyze_papers(session)
            session.update_status("papers_analyzed")
            
            # 步骤3: 生成深度研究报告
            self._generate_research_report(session)
            session.update_status("report_generated")
            
            # 步骤4: 如果是full_analysis模式，生成创新方案和可行性分析
            if config.EXECUTION_MODE == "full_analysis":
                self._generate_innovation_analysis(session)
                session.update_status("innovation_completed")
            
            session.update_status("completed")
            self.logger.info("研究流程完成!")
            
            return session
        
        except Exception as e:
            self.logger.error(f"研究流程执行失败: {e}")
            session.update_status("failed")
            raise
    
    def _collect_information(self, session: ResearchSession):
        """双轨信息采集"""
        self.logger.info("开始双轨信息采集...")
        
        query = session.query
        progress = ProgressLogger(len(query.sub_questions), "信息采集")
        
        # 为每个子问题并行进行网络搜索和学术搜索
        for i, sub_question in enumerate(query.sub_questions):
            progress.update(message=f"处理子问题: {sub_question}")
            
            # 网络搜索
            if self.web_client.is_available():
                web_response = self.web_client.search(sub_question)
                if web_response.is_successful():
                    for result_data in web_response.results:
                        web_result = WebSearchResult(**result_data)
                        session.add_web_result(web_result)
            
            # 学术搜索 (使用关键词)
            search_query = self.ads_client.build_search_query(query.keywords[:3])  # 使用前3个关键词
            ads_response = self.ads_client.search_papers(search_query)
            if ads_response.is_successful():
                for paper_data in ads_response.results:
                    paper = Paper(**paper_data)
                    session.add_paper(paper)
        
        progress.complete()
        
        # 去重论文
        unique_papers = self._deduplicate_papers(session.papers)
        session.papers = unique_papers[:config.MAX_PAPERS_TO_ANALYZE]
        
        self.logger.info(f"信息采集完成: {len(session.web_results)}个网络结果, {len(session.papers)}篇论文")
    
    def _analyze_papers(self, session: ResearchSession):
        """论文精读分析"""
        self.logger.info("开始论文精读分析...")
        
        papers = session.papers
        if not papers:
            self.logger.warning("没有论文需要分析")
            return
        
        progress = ProgressLogger(len(papers), "论文分析")
        
        # 批量处理论文
        batch_size = config.PAPER_BATCH_SIZE
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            batch_number = i // batch_size + 1
            
            self.logger.info(f"处理第 {batch_number} 批论文 ({len(batch)} 篇)")
            
            # 分析这批论文
            analyzed_batch = self.synthesizer.batch_analyze_papers(batch, session.query.main_topic)
            
            # 更新会话中的论文
            for j, analyzed_paper in enumerate(analyzed_batch):
                papers[i + j] = analyzed_paper
            
            # 生成并保存这批论文的笔记
            batch_notes = self.report_generator.create_batch_paper_notes(analyzed_batch, batch_number)
            if batch_number == 1:
                # 第一批创建新文件
                self.report_generator.save_research_details(batch_notes)
            else:
                # 后续批次追加到文件
                self.report_generator.append_to_research_details(batch_notes)
            
            progress.update(len(batch), f"完成第 {batch_number} 批")
        
        progress.complete()
        
        analyzed_count = len([p for p in papers if p.has_analysis()])
        self.logger.info(f"论文分析完成: {analyzed_count}/{len(papers)} 篇成功分析")
    
    def _generate_research_report(self, session: ResearchSession):
        """生成深度研究报告"""
        self.logger.info("生成深度研究报告...")
        
        # 准备三位一体情报
        # 情报一: 网络搜索摘要
        web_summaries = self._synthesize_web_information(session)
        
        # 情报二: 结构化论文分析
        structured_analyses = self.synthesizer.get_structured_paper_data(session.papers)
        
        # 情报三: 时间线
        timeline = self.synthesizer.create_timeline_summary(session.papers)
        
        # 生成报告
        report_content = self.writer.write_research_report(
            topic=session.query.main_topic,
            web_summaries=web_summaries,
            structured_paper_analyses=structured_analyses,
            timeline=timeline
        )
        
        # 保存报告
        self.report_generator.save_research_report(report_content)
        
        self.logger.info("深度研究报告生成完成")
    
    def _generate_innovation_analysis(self, session: ResearchSession):
        """生成创新方案和可行性分析 (仅限full_analysis模式)"""
        self.logger.info("生成创新方案和可行性分析...")
        
        # 读取已生成的研究报告
        report_path = self.report_generator.output_dir / config.RESEARCH_REPORT_FILENAME
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                research_report = f.read()
        except Exception as e:
            self.logger.error(f"无法读取研究报告: {e}")
            return
        
        # 生成创新方案
        proposal_content = self.writer.write_innovation_proposal(research_report)
        self.report_generator.save_innovation_proposal(proposal_content)
        
        # 生成可行性分析
        feasibility_content = self.writer.write_feasibility_analysis(research_report, proposal_content)
        self.report_generator.save_feasibility_analysis(feasibility_content)
        
        self.logger.info("创新方案和可行性分析生成完成")
    
    def _synthesize_web_information(self, session: ResearchSession) -> str:
        """综合网络搜索信息"""
        if not session.web_results:
            return "未获取到网络搜索信息。"
        
        # 按子问题分组网络结果
        summaries = []
        for sub_question in session.query.sub_questions:
            # 简单匹配相关结果 (实际应用中可以使用更复杂的匹配算法)
            relevant_results = session.web_results[:5]  # 简化处理，使用前5个结果
            
            summary = self.synthesizer.synthesize_web_results(sub_question, relevant_results)
            summaries.append(f"## {sub_question}\n\n{summary}\n")
        
        return "\n".join(summaries)
    
    def _deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """去重论文"""
        seen_titles = set()
        unique_papers = []
        
        for paper in papers:
            title_key = paper.title.lower().strip()
            if title_key not in seen_titles and title_key:
                seen_titles.add(title_key)
                unique_papers.append(paper)
        
        return unique_papers


def main():
    """主函数"""
    print("=" * 60)
    print("🤖 AI驱动的科研助理")
    print("=" * 60)
    print("📖 三位一体情报融合机制:")
    print("   🌐 宏观探索 - 网络搜索获取前沿资讯")
    print("   🔬 微观精读 - 学术文献深度分析")
    print("   📈 时序叙事 - 历史发展脉络梳理")
    print("=" * 60)

    try:
        # 显示配置信息
        print(f"\n⚙️  当前配置:")
        print(f"   - 执行模式: {config.EXECUTION_MODE}")
        print(f"   - LLM提供商: {config.LLM_PROVIDER}")
        print(f"   - 搜索提供商: {config.SEARCH_PROVIDER}")
        print(f"   - 最大论文数: {config.MAX_PAPERS_TO_ANALYZE}")

        # 获取用户输入
        topic = input("\n请输入您想要研究的课题: ").strip()

        if not topic:
            print("❌ 请输入有效的研究课题")
            return

        # 确认执行
        print(f"\n🔍 准备研究课题: {topic}")
        if config.EXECUTION_MODE == "full_analysis":
            print("📋 将执行完整分析模式 (包含创新方案和可行性分析)")
        else:
            print("📋 将执行深度研究模式")

        confirm = input("\n是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 用户取消执行")
            return

        print(f"\n🚀 开始执行研究流程...")
        start_time = datetime.now()

        # 创建研究助理并执行研究
        assistant = ResearchAssistant()
        session = assistant.run_research(topic)

        # 保存会话摘要
        assistant.report_generator.save_session_summary(session)

        # 计算总耗时
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()

        # 显示结果
        print("\n" + "=" * 60)
        print("✅ 研究完成!")
        print("=" * 60)

        stats = session.get_summary_stats()
        print(f"📊 统计信息:")
        print(f"   - 分析论文: {stats['analyzed_papers']}/{stats['total_papers']} 篇")
        print(f"   - 网络结果: {stats['web_results']} 个")
        print(f"   - 子问题: {stats['sub_questions']} 个")
        print(f"   - 总耗时: {total_time:.1f} 秒")

        print(f"\n📁 输出文件:")
        output_files = assistant.report_generator.get_output_files()
        for file_path in output_files:
            print(f"   - {file_path}")

        print(f"\n🎉 研究报告已生成完成!")
        print("💡 提示: 您可以在 outputs/ 目录中查看所有生成的报告")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logging.error(f"Main execution failed: {e}", exc_info=True)
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    print("""
AI驱动的科研助理 - 使用帮助

基本用法:
    python main.py                    # 交互式运行
    python main.py --help            # 显示帮助信息
    python main.py --config          # 显示配置信息

环境变量配置:
    请参考 .env.example 文件配置必要的API密钥

支持的执行模式:
    deep_research    - 深度研究模式 (默认)
    full_analysis    - 全面分析模式 (包含创新方案)

输出文件:
    research_report.md      - 主研究报告
    research_details.md     - 详细研究笔记
    proposal.md            - 创新方案 (仅full_analysis模式)
    feasibility_analysis.md - 可行性分析 (仅full_analysis模式)
    session_summary.json   - 会话摘要

更多信息请参考 README.md 文件
""")


def show_config():
    """显示当前配置"""
    print("当前配置信息:")
    print("=" * 40)
    print(f"执行模式: {config.EXECUTION_MODE}")
    print(f"LLM提供商: {config.LLM_PROVIDER}")
    print(f"搜索提供商: {config.SEARCH_PROVIDER}")
    print(f"最大论文数: {config.MAX_PAPERS_TO_ANALYZE}")
    print(f"批处理大小: {config.PAPER_BATCH_SIZE}")
    print(f"输出目录: {config.OUTPUT_DIR}")
    print(f"日志级别: {config.LOG_LEVEL}")

    # 检查API密钥状态
    print("\nAPI密钥状态:")
    print(f"ADS API: {'✅ 已配置' if config.ADS_API_TOKEN else '❌ 未配置'}")
    print(f"OpenAI API: {'✅ 已配置' if config.OPENAI_API_KEY else '❌ 未配置'}")
    print(f"Anthropic API: {'✅ 已配置' if config.ANTHROPIC_API_KEY else '❌ 未配置'}")
    print(f"Tavily API: {'✅ 已配置' if config.TAVILY_API_KEY else '❌ 未配置'}")
    print(f"Google Search API: {'✅ 已配置' if config.GOOGLE_SEARCH_API_KEY else '❌ 未配置'}")


if __name__ == "__main__":
    # 处理命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            show_help()
        elif sys.argv[1] in ['--config', '-c']:
            show_config()
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
    else:
        main()


if __name__ == "__main__":
    main()
