# main.py
#
# AI驱动的科研助理 - 主程序入口
# 负责调度和编排整个研究流程

import sys
import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

# 导入配置和工具
from config import config
from utils import (
    setup_logging, get_logger, ProgressLogger, ReportGenerator,
    handle_exceptions, ErrorCollector, system_monitor, health_checker,
    ConfigurationError, APIConnectionError
)
from utils.enhanced_logger import create_enhanced_logger, ProgressTracker

# 导入客户端
from clients import ADSClient, WebSearchClient, LLMClient

# 导入代理
from agents import PlannerAgent, SynthesizerAgent, WriterAgent

# 导入模型
from models import ResearchSession, Paper, WebSearchResult, ResponseStatus


class ResearchAssistant:
    """AI驱动的科研助理主类"""
    
    def __init__(self):
        """初始化科研助理"""
        # 设置增强日志
        self.enhanced_logger = create_enhanced_logger("ResearchAssistant")

        # 保持原有日志兼容性
        setup_logging()
        self.logger = get_logger(__name__)

        self.enhanced_logger.step("初始化AI驱动的科研助理")

        # 显示配置摘要
        config_info = {
            "执行模式": config.EXECUTION_MODE,
            "LLM提供商": config.DEFAULT_LLM_PROVIDER,
            "搜索提供商": config.SEARCH_PROVIDER,
            "最大论文数": config.MAX_PAPERS_TO_ANALYZE,
            "ADS论文数/子问题": getattr(config, 'ADS_PAPERS_PER_SUBQUESTION', 100)
        }
        self.enhanced_logger.config_summary(config_info)

        # 初始化组件
        try:
            self._initialize_components()
            self.enhanced_logger.success("所有组件初始化成功")
        except Exception as e:
            self.enhanced_logger.error(f"组件初始化失败: {e}")
            raise
    
    def _initialize_components(self):
        """初始化所有组件"""
        # 验证配置
        config.validate_config()
        
        # 初始化客户端
        self.ads_client = ADSClient()
        self.web_client = WebSearchClient()
        self.llm_client = LLMClient()
        
        # 初始化代理 - 让每个agent创建自己的专用LLM客户端
        self.planner = PlannerAgent()  # 不传递共享客户端，让agent创建专用客户端
        self.synthesizer = SynthesizerAgent()  # 不传递共享客户端，让agent创建专用客户端
        self.writer = WriterAgent()  # 不传递共享客户端，让agent创建专用客户端
        
        # 初始化报告生成器
        self.report_generator = ReportGenerator()
        
        # 测试连接
        self._test_connections()
    
    def _test_connections(self):
        """测试所有外部连接"""
        self.logger.info("执行系统健康检查...")

        # 执行完整的健康检查
        health_summary = health_checker.get_health_summary()

        # 记录健康检查结果
        self.logger.info(f"健康检查完成: {health_summary['overall_status']}")
        self.logger.info(f"服务状态: {health_summary['healthy_services']}健康, "
                        f"{health_summary['warning_services']}警告, "
                        f"{health_summary['unhealthy_services']}异常")

        # 检查关键服务
        critical_services = ["Configuration", "LLM API"]
        for service_info in health_summary['services']:
            if service_info['service'] in critical_services and service_info['status'] == 'unhealthy':
                raise APIConnectionError(
                    f"关键服务 {service_info['service']} 不可用: {service_info['message']}",
                    error_code="CRITICAL_SERVICE_DOWN"
                )

            # 记录各服务状态
            status_emoji = {"healthy": "✅", "warning": "⚠️", "unhealthy": "❌"}
            emoji = status_emoji.get(service_info['status'], "❓")
            self.logger.info(f"{emoji} {service_info['service']}: {service_info['message']}")

        # 如果有警告，记录但不阻止执行
        if health_summary['warning_services'] > 0:
            self.logger.warning("系统存在警告，但可以继续执行")

    def _check_existing_research_report(self) -> bool:
        """检查是否存在现有的研究报告"""
        report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
        return report_path.exists() and report_path.stat().st_size > 0

    def _show_full_analysis_options(self, topic: str) -> str:
        """
        显示full_analysis模式的交互选项

        Args:
            topic: 研究主题

        Returns:
            str: 用户选择 ('restart' 或 'continue')
        """
        print("\n" + "=" * 70)
        print("🔬 Full Analysis 模式 - 执行选项")
        print("=" * 70)

        has_existing_report = self._check_existing_research_report()

        if has_existing_report:
            report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
            report_size = report_path.stat().st_size / 1024  # KB

            print(f"📋 检测到现有研究报告:")
            print(f"   文件: {report_path}")
            print(f"   大小: {report_size:.1f} KB")
            print(f"   修改时间: {datetime.fromtimestamp(report_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
            print()

            print("请选择执行方式:")
            print()
            print("1️⃣  重新开始完整研究")
            print("   - 从头开始执行完整的 full_analysis 流程")
            print("   - 包括: 研究规划 → 信息采集 → 论文分析 → 报告生成 → 创新方案 → 可行性分析")
            print("   - ⚠️  将覆盖现有的所有报告文件")
            print()
            print("2️⃣  基于现有报告继续")
            print("   - 跳过前面的研究步骤，直接基于现有报告执行后续任务")
            print("   - 包括: 创新方案设计 → 可行性分析")
            print("   - ✅ 保留现有的研究报告和详细笔记")
            print()

            while True:
                choice = input("请选择 (1 或 2): ").strip()
                if choice == "1":
                    print("\n✅ 已选择: 重新开始完整研究")
                    print("📋 执行计划:")
                    print("   1. 研究规划 (PlannerAgent)")
                    print("   2. 双轨信息采集 (网络搜索 + 学术搜索)")
                    print("   3. 批量论文分析 (SynthesizerAgent)")
                    print("   4. 深度报告生成 (WriterAgent)")
                    print("   5. 创新方案设计 (WriterAgent)")
                    print("   6. 可行性分析 (WriterAgent)")

                    confirm = input("\n⚠️  这将覆盖现有报告，确认继续? (y/N): ").strip().lower()
                    if confirm in ['y', 'yes', '是']:
                        return 'restart'
                    else:
                        print("❌ 用户取消执行")
                        continue

                elif choice == "2":
                    print("\n✅ 已选择: 基于现有报告继续")
                    print("📋 执行计划:")
                    print("   1. 读取现有研究报告")
                    print("   2. 创新方案设计 (WriterAgent)")
                    print("   3. 可行性分析 (WriterAgent)")

                    confirm = input("\n确认继续? (y/N): ").strip().lower()
                    if confirm in ['y', 'yes', '是']:
                        return 'continue'
                    else:
                        print("❌ 用户取消执行")
                        continue

                else:
                    print("❌ 无效选择，请输入 1 或 2")
                    continue
        else:
            print("📋 未检测到现有研究报告，将执行完整的 full_analysis 流程")
            print()
            print("📋 执行计划:")
            print("   1. 研究规划 (PlannerAgent)")
            print("   2. 双轨信息采集 (网络搜索 + 学术搜索)")
            print("   3. 批量论文分析 (SynthesizerAgent)")
            print("   4. 深度报告生成 (WriterAgent)")
            print("   5. 创新方案设计 (WriterAgent)")
            print("   6. 可行性分析 (WriterAgent)")

            confirm = input("\n确认开始完整研究? (y/N): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                return 'restart'
            else:
                print("❌ 用户取消执行")
                return 'cancel'
    
    def run_research(self, topic: str, selected_plan: Optional[Dict[str, Any]] = None) -> ResearchSession:
        """
        执行完整的研究流程

        Args:
            topic: 研究课题
            selected_plan: 可选的历史研究计划

        Returns:
            ResearchSession: 研究会话对象
        """
        self.enhanced_logger.phase_start("研究流程开始", f"课题: {topic}")
        start_time = datetime.now()

        # 如果是full_analysis模式，显示交互选项
        if config.EXECUTION_MODE == "full_analysis":
            execution_choice = self._show_full_analysis_options(topic)

            if execution_choice == 'cancel':
                self.enhanced_logger.warning("用户取消执行")
                raise KeyboardInterrupt("用户取消执行")
            elif execution_choice == 'continue':
                # 基于现有报告继续，只执行创新分析
                return self._run_innovation_only(topic)
            # execution_choice == 'restart' 继续执行完整流程

        # 步骤1: 研究规划
        if selected_plan:
            # 使用历史研究计划
            self.enhanced_logger.step("步骤1: 加载历史研究计划")
            from models import ResearchQuery

            # 从历史计划创建ResearchQuery对象
            query = ResearchQuery(main_topic=topic)
            query.sub_questions = selected_plan['sub_questions']
            query.keywords = selected_plan['general_keywords']
            query.sub_question_keywords = selected_plan['sub_question_keywords']

            session = ResearchSession(query=query)
            session.update_status("planning_completed")

            self.enhanced_logger.success(f"已加载历史研究计划: {len(query.sub_questions)}个子问题, {len(query.keywords)}个关键词")
        else:
            # 使用PlannerAgent生成新的研究计划
            self.enhanced_logger.step("步骤1: 生成研究计划")
            session = ResearchSession(
                query=self.planner.generate_research_plan_with_confirmation(topic, use_gui=True)
            )
            session.update_status("planning_completed")
            self.enhanced_logger.success("研究计划生成完成")

        try:
            # 步骤2: 双轨信息采集
            self.enhanced_logger.step("步骤2: 双轨信息采集")
            self._collect_information(session)
            session.update_status("information_collected")
            self.enhanced_logger.success("信息采集完成")

            # 步骤3: 论文精读分析
            self.enhanced_logger.step("步骤3: 论文精读分析")
            self._analyze_papers(session)
            session.update_status("papers_analyzed")
            self.enhanced_logger.success("论文分析完成")

            # 步骤4: 生成深度研究报告
            self.enhanced_logger.step("步骤4: 生成研究报告")
            self._generate_research_report(session)
            session.update_status("report_generated")
            self.enhanced_logger.success("研究报告生成完成")

            # 步骤5: 如果是full_analysis模式，生成创新方案和可行性分析
            if config.EXECUTION_MODE == "full_analysis":
                self.enhanced_logger.step("步骤5: 创新方案分析")
                self._generate_innovation_analysis(session)
                session.update_status("innovation_completed")
                self.enhanced_logger.success("创新分析完成")

            session.update_status("completed")

            # 计算总耗时
            total_time = (datetime.now() - start_time).total_seconds()
            self.enhanced_logger.phase_end("研究流程", total_time)

            return session

        except Exception as e:
            self.enhanced_logger.error(f"研究流程执行失败: {e}")
            session.update_status("failed")
            raise

    def _run_innovation_only(self, topic: str) -> ResearchSession:
        """
        仅执行创新分析部分（基于现有研究报告）

        Args:
            topic: 研究课题

        Returns:
            ResearchSession: 研究会话对象
        """
        self.logger.info(f"基于现有报告执行创新分析: {topic}")

        # 创建简化的研究会话（不需要完整的规划和数据采集）
        from models import ResearchQuery

        # 创建基础的研究查询对象
        basic_query = ResearchQuery(main_topic=topic)
        session = ResearchSession(query=basic_query)

        # 设置状态为已完成基础研究
        session.update_status("report_generated")

        try:
            print("\n🚀 开始执行创新分析...")

            # 验证现有报告文件
            report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
            if not report_path.exists():
                raise FileNotFoundError(f"未找到现有研究报告: {report_path}")

            # 执行创新方案和可行性分析
            self._generate_innovation_analysis(session)
            session.update_status("innovation_completed")

            session.update_status("completed")
            self.logger.info("创新分析完成!")

            return session

        except Exception as e:
            self.logger.error(f"创新分析执行失败: {e}")
            session.update_status("failed")
            raise
    
    def _collect_information(self, session: ResearchSession):
        """增强的双轨信息采集 - 使用专业化ADS检索和即时排序"""
        from utils.paper_processing_pipeline import PaperProcessingPipeline

        self.enhanced_logger.step("开始增强的双轨信息采集")

        query = session.query

        # 创建论文处理流水线
        paper_pipeline = PaperProcessingPipeline(self.paper_ranking_agent)

        # 存储所有子问题的排序结果
        all_ranked_papers = []

        # 为每个子问题进行专业化搜索和即时排序
        for i, sub_question in enumerate(query.sub_questions):
            self.enhanced_logger.progress(f"处理子问题 {i+1}/{len(query.sub_questions)}: {sub_question}")

            # 网络搜索
            if self.web_client.is_available():
                web_response = self.web_client.search(sub_question)
                if web_response.is_successful():
                    for result_data in web_response.results:
                        web_result = WebSearchResult(**result_data)
                        session.add_web_result(web_result)

            # 专业化天体物理学ADS搜索
            if self.ads_client.is_available():
                # 构建查询字符串，结合通用关键词和子问题特定关键词
                keyword_combo = " ".join(query.keywords[:3])  # 使用前3个通用关键词

                # 添加子问题特定关键词
                sub_keywords = query.get_keywords_for_subquestion(i)
                if sub_keywords:
                    keyword_combo += " " + " ".join(sub_keywords[:2])  # 添加前2个特定关键词

                # 获取ADS搜索建议
                ads_suggestion = query.get_ads_suggestion_for_subquestion(i)

                # 使用专业化天体物理学搜索方法
                ads_response = self.ads_client.search_astrophysics_papers_by_subquestion(
                    keyword_combo, i, ads_suggestion
                )

                if ads_response.status == ResponseStatus.SUCCESS:
                    # 转换为Paper对象
                    papers = []
                    for paper_data in ads_response.results:
                        try:
                            paper = Paper(**paper_data)
                            papers.append(paper)
                        except Exception as e:
                            self.enhanced_logger.warning(f"Error creating paper object: {e}")
                            continue

                    # 立即进行排序处理
                    ranked_papers = paper_pipeline.process_subquestion_papers(
                        papers, sub_question, i
                    )

                    # 存储排序结果
                    all_ranked_papers.append((i, ranked_papers))

                    # 添加到会话
                    for paper in ranked_papers:
                        session.add_paper(paper)

        # 执行全局去重合并
        if all_ranked_papers:
            final_papers = paper_pipeline.merge_and_deduplicate_papers(
                all_ranked_papers, query.main_topic
            )

            # 更新会话中的论文列表
            session.papers = final_papers[:config.MAX_PAPERS_TO_ANALYZE]

            self.enhanced_logger.success(f"信息采集完成: {len(session.web_results)}个网络结果, {len(session.papers)}篇最终论文")
        else:
            self.enhanced_logger.warning("未获取到任何论文数据")
    
    def _analyze_papers(self, session: ResearchSession):
        """增强的论文精读分析 - 生成中文精读笔记"""
        self.enhanced_logger.step("开始论文精读分析")

        papers = session.papers
        if not papers:
            self.enhanced_logger.warning("没有论文需要分析")
            return

        # 使用增强的批量分析方法
        try:
            self.enhanced_logger.progress(f"批量分析 {len(papers)} 篇论文...")
            analyzed_papers = self.synthesizer.batch_analyze_papers_enhanced(papers, session.query.main_topic)

            # 更新会话中的论文
            session.papers = analyzed_papers

            # 生成中文精读笔记
            notes_file = self.synthesizer.generate_chinese_reading_notes(
                analyzed_papers, session.query.main_topic
            )

            if notes_file:
                self.enhanced_logger.file_info("生成中文精读笔记", notes_file)

            self.enhanced_logger.success("批量分析完成")

        except Exception as e:
            self.logger.error(f"Enhanced batch analysis failed, falling back to traditional method: {e}")

            # 备用方案：使用传统的批量处理方法
            batch_size = config.PAPER_BATCH_SIZE
            for i in range(0, len(papers), batch_size):
                batch = papers[i:i + batch_size]
                batch_number = i // batch_size + 1

                self.logger.info(f"处理第 {batch_number} 批论文 ({len(batch)} 篇)")

                # 分析这批论文
                analyzed_batch = self.synthesizer.batch_analyze_papers(batch, session.query.main_topic)

                # 更新会话中的论文
                for j, analyzed_paper in enumerate(analyzed_batch):
                    papers[i + j] = analyzed_paper

                # 生成并保存这批论文的笔记
                batch_notes = self.report_generator.create_batch_paper_notes(analyzed_batch, batch_number)
                if batch_number == 1:
                    # 第一批创建新文件
                    self.report_generator.save_research_details(batch_notes)
                else:
                    # 后续批次追加到文件
                    self.report_generator.append_to_research_details(batch_notes)

                progress.update(len(batch), f"完成第 {batch_number} 批")

        progress.complete()

        analyzed_count = len([p for p in papers if p.has_analysis()])
        self.logger.info(f"增强论文分析完成: {analyzed_count}/{len(papers)} 篇成功分析")
    
    def _generate_research_report(self, session: ResearchSession):
        """生成中文深度研究报告"""
        self.enhanced_logger.step("生成研究报告")

        # 准备三位一体情报
        # 情报一: 网络搜索摘要
        web_summaries = self._synthesize_web_information(session)

        # 情报二: 结构化论文分析
        structured_analyses = self.synthesizer.get_structured_paper_data(session.papers)

        # 情报三: 时间线
        timeline = self.synthesizer.create_timeline_summary(session.papers)

        # 生成中文研究报告
        report_file = self.writer.generate_chinese_research_report(
            topic=session.query.main_topic,
            papers=session.papers,
            web_summaries=web_summaries,
            timeline=timeline
        )

        if report_file:
            self.enhanced_logger.file_info("生成中文研究报告", report_file)

        self.enhanced_logger.success("研究报告生成完成")
    
    def _generate_innovation_analysis(self, session: ResearchSession):
        """生成创新方案和可行性分析 (仅限full_analysis模式)"""
        self.logger.info("生成创新方案和可行性分析...")
        
        # 读取已生成的研究报告
        report_path = self.report_generator.output_dir / config.RESEARCH_REPORT_FILENAME
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                research_report = f.read()
        except Exception as e:
            self.logger.error(f"无法读取研究报告: {e}")
            return
        
        # 生成创新方案
        proposal_content = self.writer.write_innovation_proposal(research_report)
        self.report_generator.save_innovation_proposal(proposal_content)
        
        # 生成可行性分析
        feasibility_content = self.writer.write_feasibility_analysis(research_report, proposal_content)
        self.report_generator.save_feasibility_analysis(feasibility_content)
        
        self.logger.info("创新方案和可行性分析生成完成")
    
    def _synthesize_web_information(self, session: ResearchSession) -> str:
        """综合网络搜索信息"""
        if not session.web_results:
            return "未获取到网络搜索信息。"
        
        # 按子问题分组网络结果
        summaries = []
        for sub_question in session.query.sub_questions:
            # 简单匹配相关结果 (实际应用中可以使用更复杂的匹配算法)
            relevant_results = session.web_results[:5]  # 简化处理，使用前5个结果
            
            summary = self.synthesizer.synthesize_web_results(sub_question, relevant_results)
            summaries.append(f"## {sub_question}\n\n{summary}\n")
        
        return "\n".join(summaries)
    
    def _deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """去重论文"""
        seen_titles = set()
        unique_papers = []
        
        for paper in papers:
            title_key = paper.title.lower().strip()
            if title_key not in seen_titles and title_key:
                seen_titles.add(title_key)
                unique_papers.append(paper)
        
        return unique_papers


def main():
    """主函数"""
    # 创建主程序增强日志器
    main_logger = create_enhanced_logger("Main")

    main_logger.step("AI驱动的科研助理启动")
    main_logger.progress("三位一体情报融合机制:")
    main_logger.progress("  🌐 宏观探索 - 网络搜索获取前沿资讯")
    main_logger.progress("  🔬 微观精读 - 学术文献深度分析")
    main_logger.progress("  📈 时序叙事 - 历史发展脉络梳理")

    try:
        # 使用GUI获取用户输入
        try:
            from gui.main_dialog import show_main_dialog

            main_logger.progress("打开GUI对话框...")
            result = show_main_dialog()

            if result is None:
                main_logger.warning("用户取消操作")
                return

            topic, execution_mode, selected_plan = result

            # 设置执行模式（如果用户在GUI中选择了不同的模式）
            config.EXECUTION_MODE = execution_mode

            main_logger.success(f"研究课题: {topic}")
            main_logger.progress(f"执行模式: {execution_mode.replace('_', ' ').title()}")

            if selected_plan:
                main_logger.progress("使用历史研究计划")

        except ImportError as e:
            main_logger.warning(f"GUI不可用，回退到控制台输入: {e}")

            # 回退到控制台输入
            topic = input("\n请输入您想要研究的课题: ").strip()

            if not topic:
                main_logger.error("请输入有效的研究课题")
                return

            main_logger.success(f"研究课题: {topic}")

            # 对于deep_research模式，显示简单确认
            if config.EXECUTION_MODE == "deep_research":
                main_logger.progress("将执行深度研究模式")
                confirm = input("\n是否继续? (y/N): ").strip().lower()
                if confirm not in ['y', 'yes', '是']:
                    main_logger.warning("用户取消执行")
                    return
            # full_analysis模式的交互将在run_research中处理

            selected_plan = None

        # 创建研究助理并执行研究
        assistant = ResearchAssistant()
        session = assistant.run_research(topic, selected_plan)

        # 保存会话摘要
        assistant.report_generator.save_session_summary(session)

        # 显示最终结果
        stats = session.get_summary_stats()
        main_logger.success("研究流程全部完成!")
        main_logger.progress("统计信息:")
        main_logger.progress(f"  - 分析论文: {stats['analyzed_papers']}/{stats['total_papers']} 篇")
        main_logger.progress(f"  - 网络结果: {stats['web_results']} 个")
        main_logger.progress(f"  - 子问题: {stats['sub_questions']} 个")

        main_logger.progress("输出文件:")
        output_files = assistant.report_generator.get_output_files()
        for file_path in output_files:
            file_size = Path(file_path).stat().st_size if Path(file_path).exists() else 0
            main_logger.file_info("生成", file_path, file_size)

        main_logger.success("所有报告已生成完成!")
        main_logger.progress(f"详细日志: {assistant.enhanced_logger.get_log_file_path()}")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logging.error(f"Main execution failed: {e}", exc_info=True)
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    print("""
AI驱动的科研助理 - 使用帮助

基本用法:
    python main.py                    # 交互式运行
    python main.py --help            # 显示帮助信息
    python main.py --config          # 显示配置信息
    python main.py --health          # 显示系统健康状态

环境变量配置:
    请参考 .env.example 文件配置必要的API密钥

支持的执行模式:
    deep_research    - 深度研究模式 (默认)
    full_analysis    - 全面分析模式 (包含创新方案)

输出文件:
    research_report.md      - 主研究报告
    research_details.md     - 详细研究笔记
    proposal.md            - 创新方案 (仅full_analysis模式)
    feasibility_analysis.md - 可行性分析 (仅full_analysis模式)
    session_summary.json   - 会话摘要

更多信息请参考 README.md 文件
""")


def show_config():
    """显示当前配置"""
    print("当前配置信息:")
    print("=" * 40)
    print(f"执行模式: {config.EXECUTION_MODE}")
    print(f"LLM提供商: {config.DEFAULT_LLM_PROVIDER}")
    print(f"搜索提供商: {config.SEARCH_PROVIDER}")
    print(f"最大论文数: {config.MAX_PAPERS_TO_ANALYZE}")
    print(f"批处理大小: {config.PAPER_BATCH_SIZE}")
    print(f"输出目录: {config.OUTPUT_DIR}")
    print(f"日志级别: {config.LOG_LEVEL}")

    # 检查API密钥状态
    print("\nAPI密钥状态:")
    print(f"ADS API: {'✅ 已配置' if config.ADS_API_TOKEN else '❌ 未配置'}")
    print(f"OpenAI API: {'✅ 已配置' if config.OPENAI_API_KEY else '❌ 未配置'}")
    print(f"Anthropic API: {'✅ 已配置' if config.ANTHROPIC_API_KEY else '❌ 未配置'}")
    print(f"Tavily API: {'✅ 已配置' if config.TAVILY_API_KEY else '❌ 未配置'}")
    print(f"Google Search API: {'✅ 已配置' if config.GOOGLE_SEARCH_API_KEY else '❌ 未配置'}")


def show_health():
    """显示系统健康状态"""
    print("系统健康检查:")
    print("=" * 40)

    try:
        # 设置基本日志以避免初始化错误
        setup_logging()

        health_summary = health_checker.get_health_summary()

        # 显示总体状态
        status_emoji = {"healthy": "✅", "warning": "⚠️", "unhealthy": "❌"}
        overall_emoji = status_emoji.get(health_summary['overall_status'], "❓")
        print(f"总体状态: {overall_emoji} {health_summary['overall_status'].upper()}")
        print(f"服务统计: {health_summary['healthy_services']}健康, "
              f"{health_summary['warning_services']}警告, "
              f"{health_summary['unhealthy_services']}异常")
        print()

        # 显示各服务详情
        print("服务详情:")
        for service in health_summary['services']:
            emoji = status_emoji.get(service['status'], "❓")
            print(f"{emoji} {service['service']}: {service['message']}")
            if service['response_time']:
                print(f"   响应时间: {service['response_time']:.2f}秒")

        # 显示系统指标
        print("\n系统指标:")
        metrics = system_monitor.get_system_metrics()
        if 'error' not in metrics:
            print(f"CPU使用率: {metrics.get('cpu_percent', 0):.1f}%")
            print(f"内存使用率: {metrics.get('memory_percent', 0):.1f}%")
            print(f"可用内存: {metrics.get('memory_available_gb', 0):.1f}GB")
            print(f"磁盘使用率: {metrics.get('disk_percent', 0):.1f}%")
            print(f"可用磁盘: {metrics.get('disk_free_gb', 0):.1f}GB")

        # 检查资源警告
        warnings = system_monitor.check_resource_usage()
        if warnings:
            print("\n⚠️  资源警告:")
            for warning in warnings:
                print(f"   - {warning}")

    except Exception as e:
        print(f"❌ 健康检查失败: {e}")


if __name__ == "__main__":
    # 处理命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            show_help()
        elif sys.argv[1] in ['--config', '-c']:
            show_config()
        elif sys.argv[1] in ['--health', '--check']:
            show_health()
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
    else:
        main()


if __name__ == "__main__":
    main()
