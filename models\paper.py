# models/paper.py
#
# 定义论文对象的数据模型

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
import json


class PaperAnalysis(BaseModel):
    """论文分析结果的数据模型"""
    
    short_summary: str = Field(..., description="论文的简短概括")
    relevance_to_topic: str = Field(..., description="与研究课题的相关性")
    research_subject: str = Field(..., description="研究对象")
    methodology: str = Field(..., description="研究方法")
    data_used: str = Field(..., description="使用的数据")
    key_findings_or_results: List[str] = Field(..., description="关键发现或结果")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict()
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return self.json(ensure_ascii=False, indent=2)


class Paper(BaseModel):
    """论文对象的数据模型"""
    
    # 基本信息
    title: str = Field(..., description="论文标题")
    authors: List[str] = Field(default_factory=list, description="作者列表")
    abstract: Optional[str] = Field(None, description="论文摘要")
    
    # 发表信息
    publication_date: Optional[datetime] = Field(None, description="发表日期")
    journal: Optional[str] = Field(None, description="期刊名称")
    volume: Optional[str] = Field(None, description="卷号")
    issue: Optional[str] = Field(None, description="期号")
    pages: Optional[str] = Field(None, description="页码")
    doi: Optional[str] = Field(None, description="DOI")
    
    # 外部标识符
    ads_bibcode: Optional[str] = Field(None, description="ADS bibcode")
    arxiv_id: Optional[str] = Field(None, description="arXiv ID")
    
    # 引用信息
    citation_count: Optional[int] = Field(None, description="引用次数")
    
    # AI分析结果
    analysis: Optional[PaperAnalysis] = Field(None, description="AI分析结果")
    
    # 元数据
    source: Optional[str] = Field(None, description="数据来源 (ads, arxiv, etc.)")
    retrieved_at: datetime = Field(default_factory=datetime.now, description="检索时间")

    # 引用相关字段
    citation_key: Optional[str] = Field(None, description="引用键")
    is_cited: bool = Field(False, description="是否在报告中被引用")
    citation_count_in_report: int = Field(0, description="在报告中被引用次数")
    
    class Config:
        # 允许使用datetime对象
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
    
    @validator('publication_date', pre=True)
    def parse_publication_date(cls, v):
        """解析发表日期"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                # 尝试多种日期格式
                for fmt in ['%Y-%m-%d', '%Y-%m', '%Y', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        return datetime.strptime(v, fmt)
                    except ValueError:
                        continue
                # 如果都不匹配，返回None
                return None
            except:
                return None
        return v
    
    def set_analysis(self, analysis_data: Dict[str, Any]) -> None:
        """设置AI分析结果"""
        try:
            self.analysis = PaperAnalysis(**analysis_data)
        except Exception as e:
            print(f"Error setting analysis for paper '{self.title}': {e}")
            self.analysis = None
    
    def get_year(self) -> Optional[int]:
        """获取发表年份"""
        if self.publication_date:
            return self.publication_date.year
        return None
    
    def get_short_citation(self) -> str:
        """获取简短引用格式"""
        if not self.authors:
            author_part = "Unknown"
        elif len(self.authors) == 1:
            author_part = self.authors[0].split(',')[0]  # 取姓氏
        else:
            author_part = f"{self.authors[0].split(',')[0]} et al."

        year = self.get_year() or "Unknown"
        return f"{author_part} ({year})"

    def get_apa_citation(self) -> str:
        """获取APA格式完整引用"""
        from utils.citation_manager import CitationFormatter
        return CitationFormatter.format_apa(self)['full']

    def get_ieee_citation(self) -> str:
        """获取IEEE格式完整引用"""
        from utils.citation_manager import CitationFormatter
        return CitationFormatter.format_ieee(self)['full']

    def get_nature_citation(self) -> str:
        """获取Nature格式完整引用"""
        from utils.citation_manager import CitationFormatter
        return CitationFormatter.format_nature(self)['full']

    def get_bibtex_entry(self) -> str:
        """获取BibTeX条目"""
        # 生成BibTeX键
        if self.authors:
            first_author_last = self.authors[0].split(',')[0].strip() if ',' in self.authors[0] else self.authors[0].split()[-1]
        else:
            first_author_last = "unknown"

        year = self.get_year() or "unknown"
        title_words = (self.title or "untitled").split()[:3]
        key = f"{first_author_last.lower()}{year}{''.join(title_words).lower()}"

        # 构建BibTeX条目
        bibtex_lines = [f"@article{{{key},"]

        if self.title:
            bibtex_lines.append(f'  title = {{{self.title}}},')

        if self.authors:
            authors_str = " and ".join(self.authors)
            bibtex_lines.append(f'  author = {{{authors_str}}},')

        if self.journal:
            bibtex_lines.append(f'  journal = {{{self.journal}}},')

        if self.volume:
            bibtex_lines.append(f'  volume = {{{self.volume}}},')

        if self.issue:
            bibtex_lines.append(f'  number = {{{self.issue}}},')

        if self.pages:
            bibtex_lines.append(f'  pages = {{{self.pages}}},')

        if self.get_year():
            bibtex_lines.append(f'  year = {{{self.get_year()}}},')

        if self.doi:
            bibtex_lines.append(f'  doi = {{{self.doi}}},')

        bibtex_lines.append("}")

        return "\n".join(bibtex_lines)

    def generate_citation_key(self) -> str:
        """生成引用键"""
        if self.citation_key:
            return self.citation_key

        # 自动生成引用键
        if self.authors:
            first_author = self.authors[0]
            if ',' in first_author:
                last_name = first_author.split(',')[0].strip()
            else:
                last_name = first_author.split()[-1]
        else:
            last_name = "unknown"

        year = self.get_year() or "unknown"

        # 如果有多个作者，添加"etal"
        if len(self.authors or []) > 1:
            self.citation_key = f"{last_name.lower()}etal{year}"
        else:
            self.citation_key = f"{last_name.lower()}{year}"

        return self.citation_key
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = self.dict()
        # 处理datetime对象
        if data.get('publication_date'):
            data['publication_date'] = data['publication_date'].isoformat()
        if data.get('retrieved_at'):
            data['retrieved_at'] = data['retrieved_at'].isoformat()
        return data
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return self.json(ensure_ascii=False, indent=2)
    
    def has_analysis(self) -> bool:
        """检查是否有AI分析结果"""
        return self.analysis is not None
    
    def get_timeline_entry(self) -> Optional[Dict[str, str]]:
        """获取时间线条目"""
        if not self.has_analysis() or not self.publication_date:
            return None
        
        return {
            "date": self.publication_date.strftime("%Y-%m"),
            "title": self.title,
            "summary": self.analysis.short_summary,
            "citation": self.get_short_citation()
        }


class ResearchQuery(BaseModel):
    """研究查询的数据模型"""

    main_topic: str = Field(..., description="主要研究课题")
    sub_questions: List[str] = Field(default_factory=list, description="子问题列表")
    keywords: List[str] = Field(default_factory=list, description="关键词列表")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

    # 新增：子问题到关键词的映射
    sub_question_keywords: Optional[Dict[str, List[str]]] = Field(default_factory=dict, description="子问题关键词映射")

    def add_sub_question(self, question: str) -> None:
        """添加子问题"""
        if question not in self.sub_questions:
            self.sub_questions.append(question)

    def add_keyword(self, keyword: str) -> None:
        """添加关键词"""
        if keyword not in self.keywords:
            self.keywords.append(keyword)

    def get_keywords_for_subquestion(self, sub_question_index: int) -> List[str]:
        """获取特定子问题的关键词"""
        if self.sub_question_keywords:
            key = f"sub_question_{sub_question_index + 1}"
            return self.sub_question_keywords.get(key, [])
        return []

    def get_all_unique_keywords(self) -> List[str]:
        """获取所有唯一的关键词"""
        all_keywords = set(self.keywords)
        if self.sub_question_keywords:
            for keyword_list in self.sub_question_keywords.values():
                all_keywords.update(keyword_list)
        return list(all_keywords)


class WebSearchResult(BaseModel):
    """网络搜索结果的数据模型"""
    
    title: str = Field(..., description="标题")
    url: str = Field(..., description="URL")
    snippet: str = Field(..., description="摘要片段")
    source: Optional[str] = Field(None, description="来源")
    retrieved_at: datetime = Field(default_factory=datetime.now, description="检索时间")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict()


class ResearchSession(BaseModel):
    """研究会话的数据模型"""
    
    query: ResearchQuery = Field(..., description="研究查询")
    papers: List[Paper] = Field(default_factory=list, description="论文列表")
    web_results: List[WebSearchResult] = Field(default_factory=list, description="网络搜索结果")
    
    # 处理状态
    status: str = Field(default="initialized", description="处理状态")
    progress: Dict[str, Any] = Field(default_factory=dict, description="进度信息")
    
    # 时间戳
    started_at: datetime = Field(default_factory=datetime.now, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    def add_paper(self, paper: Paper) -> None:
        """添加论文"""
        self.papers.append(paper)
    
    def add_web_result(self, result: WebSearchResult) -> None:
        """添加网络搜索结果"""
        self.web_results.append(result)
    
    def get_analyzed_papers(self) -> List[Paper]:
        """获取已分析的论文"""
        return [paper for paper in self.papers if paper.has_analysis()]
    
    def get_timeline_data(self) -> List[Dict[str, str]]:
        """获取时间线数据"""
        timeline_entries = []
        for paper in self.get_analyzed_papers():
            entry = paper.get_timeline_entry()
            if entry:
                timeline_entries.append(entry)
        
        # 按日期排序
        timeline_entries.sort(key=lambda x: x['date'])
        return timeline_entries
    
    def update_status(self, status: str) -> None:
        """更新状态"""
        self.status = status
        if status == "completed":
            self.completed_at = datetime.now()
    
    def get_summary_stats(self) -> Dict[str, int]:
        """获取统计摘要"""
        return {
            "total_papers": len(self.papers),
            "analyzed_papers": len(self.get_analyzed_papers()),
            "web_results": len(self.web_results),
            "sub_questions": len(self.query.sub_questions)
        }
