# models/response.py
#
# 定义API响应和处理结果的数据模型

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class ResponseStatus(str, Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"
    TIMEOUT = "timeout"


class APIResponse(BaseModel):
    """通用API响应模型"""
    
    status: ResponseStatus = Field(..., description="响应状态")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    
    @classmethod
    def success(cls, data: Optional[Dict[str, Any]] = None, message: str = "Success"):
        """创建成功响应"""
        return cls(status=ResponseStatus.SUCCESS, data=data, message=message)
    
    @classmethod
    def error(cls, error: str, message: str = "Error occurred"):
        """创建错误响应"""
        return cls(status=ResponseStatus.ERROR, error=error, message=message)
    
    @classmethod
    def partial(cls, data: Optional[Dict[str, Any]] = None, message: str = "Partial success"):
        """创建部分成功响应"""
        return cls(status=ResponseStatus.PARTIAL, data=data, message=message)


class SearchResponse(BaseModel):
    """搜索响应模型"""
    
    query: str = Field(..., description="搜索查询")
    total_results: int = Field(default=0, description="总结果数")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="搜索结果")
    source: str = Field(..., description="搜索来源")
    status: ResponseStatus = Field(..., description="搜索状态")
    error: Optional[str] = Field(None, description="错误信息")
    search_time: float = Field(default=0.0, description="搜索耗时(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="搜索时间")
    
    def is_successful(self) -> bool:
        """检查搜索是否成功"""
        return self.status == ResponseStatus.SUCCESS
    
    def has_results(self) -> bool:
        """检查是否有结果"""
        return len(self.results) > 0


class LLMResponse(BaseModel):
    """LLM响应模型"""
    
    prompt: str = Field(..., description="输入提示")
    response: Optional[str] = Field(None, description="LLM响应")
    model: str = Field(..., description="使用的模型")
    provider: str = Field(..., description="LLM提供商")
    status: ResponseStatus = Field(..., description="响应状态")
    error: Optional[str] = Field(None, description="错误信息")
    
    # 使用统计
    prompt_tokens: Optional[int] = Field(None, description="输入token数")
    completion_tokens: Optional[int] = Field(None, description="输出token数")
    total_tokens: Optional[int] = Field(None, description="总token数")
    
    # 时间统计
    response_time: float = Field(default=0.0, description="响应时间(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    
    def is_successful(self) -> bool:
        """检查响应是否成功"""
        return self.status == ResponseStatus.SUCCESS and self.response is not None
    
    def get_usage_info(self) -> Dict[str, Optional[int]]:
        """获取使用统计信息"""
        return {
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens
        }


class ProcessingResult(BaseModel):
    """处理结果模型"""
    
    task_name: str = Field(..., description="任务名称")
    status: ResponseStatus = Field(..., description="处理状态")
    result: Optional[Dict[str, Any]] = Field(None, description="处理结果")
    error: Optional[str] = Field(None, description="错误信息")
    
    # 时间统计
    started_at: datetime = Field(default_factory=datetime.now, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration: Optional[float] = Field(None, description="处理时长(秒)")
    
    # 进度信息
    progress: Dict[str, Any] = Field(default_factory=dict, description="进度信息")
    
    def mark_completed(self, result: Optional[Dict[str, Any]] = None):
        """标记为完成"""
        self.completed_at = datetime.now()
        self.duration = (self.completed_at - self.started_at).total_seconds()
        self.status = ResponseStatus.SUCCESS
        if result:
            self.result = result
    
    def mark_error(self, error: str):
        """标记为错误"""
        self.completed_at = datetime.now()
        self.duration = (self.completed_at - self.started_at).total_seconds()
        self.status = ResponseStatus.ERROR
        self.error = error
    
    def update_progress(self, progress_data: Dict[str, Any]):
        """更新进度"""
        self.progress.update(progress_data)


class BatchProcessingResult(BaseModel):
    """批处理结果模型"""
    
    batch_name: str = Field(..., description="批处理名称")
    total_items: int = Field(..., description="总项目数")
    processed_items: int = Field(default=0, description="已处理项目数")
    successful_items: int = Field(default=0, description="成功项目数")
    failed_items: int = Field(default=0, description="失败项目数")
    
    results: List[ProcessingResult] = Field(default_factory=list, description="处理结果列表")
    
    started_at: datetime = Field(default_factory=datetime.now, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    def add_result(self, result: ProcessingResult):
        """添加处理结果"""
        self.results.append(result)
        self.processed_items += 1
        
        if result.status == ResponseStatus.SUCCESS:
            self.successful_items += 1
        else:
            self.failed_items += 1
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100
    
    def is_completed(self) -> bool:
        """检查是否完成"""
        return self.processed_items >= self.total_items
    
    def mark_completed(self):
        """标记为完成"""
        self.completed_at = datetime.now()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        duration = None
        if self.completed_at:
            duration = (self.completed_at - self.started_at).total_seconds()
        
        return {
            "batch_name": self.batch_name,
            "total_items": self.total_items,
            "processed_items": self.processed_items,
            "successful_items": self.successful_items,
            "failed_items": self.failed_items,
            "success_rate": (self.successful_items / self.total_items * 100) if self.total_items > 0 else 0,
            "progress_percentage": self.get_progress_percentage(),
            "duration_seconds": duration,
            "is_completed": self.is_completed()
        }
