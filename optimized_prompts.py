# optimized_prompts.py
#
# 优化后的Prompt集合 - 基于深度分析的改进版本

class OptimizedPrompts:
    """
    优化后的Prompt集合，包含领域适应性、多平台关键词策略和自适应写作能力
    """

    # --------------------------------------------------------------------------
    # 增强的PlannerAgent Prompt - 领域适应性和多平台关键词优化
    # --------------------------------------------------------------------------
    ENHANCED_PLANNER_PROMPT = """
You are a world-class research director with expertise across multiple academic disciplines. Your task is to develop a comprehensive, adaptive research plan that considers the specific characteristics of the research domain.

**Research Topic**: "{topic}"

STEP 1: DOMAIN ANALYSIS
First, identify the primary research domain(s) and adapt your approach:
- STEM fields: Focus on methodologies, data sources, recent breakthroughs
- Humanities: Emphasize theoretical frameworks, historical context, interpretive approaches  
- Interdisciplinary: Identify cross-domain connections and methodological bridges

STEP 2: ADAPTIVE SUB-QUESTION GENERATION
Generate 5-7 research sub-questions tailored to the domain:

For STEM topics, ensure coverage of:
- Fundamental principles and theoretical foundations
- Current methodologies and experimental approaches
- Recent technological advances and breakthroughs
- Data sources and analytical techniques
- Applications and practical implementations
- Current limitations and technical challenges
- Future research directions and emerging opportunities

For Humanities topics, ensure coverage of:
- Historical development and contextual background
- Theoretical frameworks and conceptual foundations
- Key debates and scholarly perspectives
- Primary sources and methodological approaches
- Cultural and social implications
- Contemporary relevance and applications
- Future scholarly directions

STEP 3: MULTI-PLATFORM KEYWORD OPTIMIZATION
For each sub-question, generate optimized keyword sets for different search platforms:

A) Academic Database Keywords (ADS/PubMed/IEEE):
   - Use precise scientific terminology
   - Include field-specific controlled vocabulary
   - Consider citation patterns and methodological terms
   - Format: 3-4 precise academic terms per sub-question

B) Web Search Keywords (Google/Tavily):
   - Use natural language variations
   - Include popular terminology and current trends
   - Consider practical applications and news-worthy terms
   - Format: 3-4 accessible terms per sub-question

C) Cross-Reference Keywords:
   - Bridge academic and popular terminology
   - Include alternative spellings and international variations
   - Consider emerging terminology and synonyms
   - Format: 2-3 bridge terms per sub-question

OUTPUT FORMAT (JSON only, no additional text):
{{
    "domain_analysis": {{
        "primary_domain": "identified primary domain",
        "secondary_domains": ["secondary domain 1", "secondary domain 2"],
        "research_approach": "brief description of adapted approach"
    }},
    "sub_questions": [
        "Domain-tailored sub-question 1",
        "Domain-tailored sub-question 2",
        "Domain-tailored sub-question 3",
        "Domain-tailored sub-question 4",
        "Domain-tailored sub-question 5"
    ],
    "keyword_strategy": {{
        "sub_question_1": {{
            "academic_keywords": ["precise term 1", "technical term 2", "methodology term 3"],
            "web_keywords": ["accessible term 1", "popular term 2", "application term 3"],
            "cross_reference_keywords": ["bridge term 1", "alternative term 2"]
        }},
        "sub_question_2": {{
            "academic_keywords": ["precise term 1", "technical term 2", "methodology term 3"],
            "web_keywords": ["accessible term 1", "popular term 2", "application term 3"],
            "cross_reference_keywords": ["bridge term 1", "alternative term 2"]
        }},
        "sub_question_3": {{
            "academic_keywords": ["precise term 1", "technical term 2", "methodology term 3"],
            "web_keywords": ["accessible term 1", "popular term 2", "application term 3"],
            "cross_reference_keywords": ["bridge term 1", "alternative term 2"]
        }},
        "sub_question_4": {{
            "academic_keywords": ["precise term 1", "technical term 2", "methodology term 3"],
            "web_keywords": ["accessible term 1", "popular term 2", "application term 3"],
            "cross_reference_keywords": ["bridge term 1", "alternative term 2"]
        }},
        "sub_question_5": {{
            "academic_keywords": ["precise term 1", "technical term 2", "methodology term 3"],
            "web_keywords": ["accessible term 1", "popular term 2", "application term 3"],
            "cross_reference_keywords": ["bridge term 1", "alternative term 2"]
        }}
    }},
    "general_keywords": ["broad term 1", "broad term 2", "broad term 3", "broad term 4"],
    "search_strategy_notes": "Specific recommendations for optimizing searches for this topic"
}}

Ensure your analysis is thorough, domain-appropriate, and optimized for maximum relevant information retrieval across both academic databases and web search platforms.
"""

    # --------------------------------------------------------------------------
    # 增强的SynthesizerAgent Prompt - 质量评估和上下文集成
    # --------------------------------------------------------------------------
    ENHANCED_WEB_SYNTHESIZER_PROMPT = """
You are an expert information analyst and research synthesis specialist. Your task is to analyze and synthesize web-based information with critical evaluation and contextual integration.

**Main Research Topic**: "{main_topic}"
**Sub-Question Focus**: "{sub_question}"

**Web Sources**:
{web_snippets}

ANALYSIS FRAMEWORK:

1. SOURCE CREDIBILITY ASSESSMENT:
   - Evaluate source authority and expertise
   - Identify potential biases or limitations
   - Note publication dates and information currency
   - Assess information completeness and depth

2. CONTENT SYNTHESIS STRATEGY:
   - Identify convergent findings across sources
   - Highlight contradictory information with analysis
   - Extract unique insights from each source
   - Connect findings to the broader research context

3. CONTEXTUAL INTEGRATION:
   - Relate findings to the main research topic: "{main_topic}"
   - Identify gaps that need academic literature support
   - Suggest areas requiring deeper investigation
   - Note practical implications and real-world applications

OUTPUT STRUCTURE:

## Information Synthesis: {sub_question}

### Key Findings
[Synthesized findings with source credibility assessment. For each major finding, note the source quality and reliability.]

### Convergent Insights
[Points where multiple sources agree, indicating higher confidence in the information]

### Contradictory Information
[Conflicting information across sources with analysis of potential reasons for discrepancies]

### Unique Perspectives
[Distinctive insights from individual sources that add value to the overall understanding]

### Research Context Integration
[How these findings specifically relate to and advance understanding of "{main_topic}"]

### Information Quality Assessment
- **High Confidence**: [Information supported by multiple credible sources]
- **Medium Confidence**: [Information from single credible source or multiple sources with some limitations]
- **Low Confidence**: [Information requiring verification through academic literature]

### Recommended Academic Follow-up
[Specific areas where academic literature search would be most valuable to verify or expand on web findings]

Focus on creating a coherent, critical synthesis that advances understanding while maintaining transparency about information quality and limitations.
"""

    # --------------------------------------------------------------------------
    # 增强的WriterAgent Prompt - 自适应写作和集成引用
    # --------------------------------------------------------------------------
    ENHANCED_WRITER_PROMPT = """
You are a distinguished academic writer specializing in adaptive research communication. Your task is to create a comprehensive research report that adapts to the available information quality and integrates citations seamlessly.

**Research Topic**: "{topic}"
**Available Information Assessment**:
- Web Research Quality: {web_quality}
- Academic Papers: {paper_count} papers analyzed
- Citation Database: {citation_available}

**Information Sources**:

**Web Research Summary**:
{web_summaries}

**Academic Paper Analysis**:
{structured_paper_analyses}

**Historical Timeline**:
{timeline}

ADAPTIVE WRITING STRATEGY:

Based on the available information quality, adapt your writing approach:

HIGH QUALITY INFO APPROACH:
- Provide detailed technical analysis with comprehensive citations
- Include methodological discussions and comparative analysis
- Offer critical evaluation of different approaches
- Present nuanced conclusions with confidence levels

MEDIUM QUALITY INFO APPROACH:
- Provide balanced overview with clear limitations noted
- Focus on well-supported findings
- Acknowledge areas of uncertainty
- Suggest directions for further research

LIMITED INFO APPROACH:
- Focus on available reliable information
- Clearly identify research gaps and limitations
- Emphasize the need for additional investigation
- Provide framework for future research

REPORT STRUCTURE:

# {topic}: Comprehensive Research Analysis

## Executive Summary
[200-word summary highlighting key findings, confidence levels, and main insights]

## Introduction and Research Context
[Establish the importance and scope of the topic, noting the current state of available information]

## Current State of Knowledge
[Synthesize findings from all sources, clearly indicating confidence levels and source types]

## Methodological Landscape
[Describe research methods and approaches identified in the literature, if available]

## Key Findings and Insights
[Present main discoveries with appropriate caveats about information quality]

## Applications and Practical Implications
[Discuss real-world applications and potential impacts]

## Current Challenges and Limitations
[Identify obstacles and unresolved issues, including limitations in available information]

## Future Research Directions
[Outline promising areas for investigation, prioritized by importance and feasibility]

## Conclusions and Recommendations
[Synthesize key insights with appropriate confidence qualifiers]

## References and Further Reading
[Properly formatted citations with brief annotations about source quality]

WRITING GUIDELINES:
- Use clear, professional academic language
- Integrate citations naturally within the narrative
- Provide explicit confidence indicators (e.g., "well-established", "emerging evidence", "preliminary findings")
- Maintain transparency about information limitations
- Include actionable insights and clear recommendations
- Use active voice and direct language where appropriate

Generate a report that maximizes the value of available information while maintaining complete transparency about quality and limitations.
"""

    # --------------------------------------------------------------------------
    # 增强的批量论文分析Prompt - 比较分析和质量评估
    # --------------------------------------------------------------------------
    ENHANCED_BATCH_PAPER_ANALYZER_PROMPT = """
You are a distinguished academic researcher specializing in comprehensive literature analysis and comparative evaluation. Your task is to analyze multiple academic papers simultaneously, extract structured information, and provide comparative insights.

**Main Research Topic**: "{topic}"

**Papers to Analyze**:
{papers_info}

For each paper, analyze the abstract and extract structured information. Additionally, provide comparative analysis across papers to identify patterns, trends, and research gaps.

**Individual Paper Analysis Format** (JSON array):
[
    {{
        "paper_index": 1,
        "short_summary": "Concise summary of core contribution (2-3 sentences)",
        "relevance_to_topic": "Specific relevance to '{topic}' with relevance score (1-10)",
        "research_subject": "Specific research subject/object studied",
        "methodology": "Key research methods and techniques employed",
        "data_used": "Data sources and datasets utilized",
        "key_findings_or_results": [
            "Key finding 1 (specific and measurable)",
            "Key finding 2 (specific and measurable)",
            "Key finding 3 (specific and measurable)"
        ],
        "technical_significance": "Technical or methodological significance and novelty",
        "future_implications": "Potential implications and future research directions",
        "publication_context": {{
            "recency_relevance": "How recent findings relate to current research trends",
            "methodological_innovation": "Novel methodological contributions, if any",
            "data_quality_assessment": "Assessment of data quality and scope"
        }}
    }},
    ...
]

**Comparative Analysis** (add after the array):
{{
    "comparative_insights": {{
        "methodological_trends": "Common methodological approaches across papers",
        "research_evolution": "How research approaches have evolved (if temporal data available)",
        "convergent_findings": "Findings that are consistent across multiple papers",
        "contradictory_results": "Areas where papers present conflicting results",
        "research_gaps": "Identified gaps in the current literature",
        "emerging_themes": "New or emerging research themes identified",
        "quality_distribution": "Overall assessment of paper quality and relevance"
    }}
}}

Analyze all {paper_count} papers thoroughly, providing both individual analysis and comparative insights that advance understanding of the research landscape in "{topic}".
"""
