{"main_topic": "Test Research Topic for GUI Improvements", "sub_questions": ["How can we improve the GUI display for research plans?", "What are the best practices for implementing dropdown menus in tkinter?", "How should we handle file loading and validation in research applications?"], "general_keywords": ["GUI design", "User interface", "Dropdown menus", "File handling", "Research tools"], "sub_question_keywords": {"sub_question_1": ["GUI", "display", "research plans"], "sub_question_2": ["tkinter", "dropdown", "best practices"], "sub_question_3": ["file loading", "validation", "applications"]}, "ads_search_suggestions": {"sub_question_1": {"search_strategy": "GUI AND research AND display", "date_range": "2020-2024", "notes": "Focus on user interface design papers"}}, "created_at": "2025-07-20T22:00:00", "saved_at": "2025-07-20T22:00:00"}