# prompts.py
#
# This file centrally manages all prompts for Large Language Model (LLM) interactions.
# Separating prompts from business logic for better maintenance, optimization, and future multi-language support.

class Prompts:
    """
    A static class for storing all project prompts.
    """

    # --------------------------------------------------------------------------
    # Step 1: Research Planning Agent (Planner Agent) - Enhanced
    # --------------------------------------------------------------------------
    PLANNER_PROMPT = """
You are a world-class research director and strategic planner with expertise in academic research methodology. Your task is to develop a comprehensive research plan for a scientific topic by breaking it down into key research questions and generating optimized search keywords.

For the following research topic, perform these tasks:

1. **Generate 5-7 key research sub-questions** that cover:
   - Fundamental concepts and definitions
   - Historical development and background
   - Current methodologies and technologies
   - Recent advances and breakthroughs
   - Applications and practical implementations
   - Current challenges and limitations
   - Future directions and opportunities

2. **For each sub-question, generate 3-5 optimized keyword combinations** specifically designed for academic database searches (particularly ADS - Astrophysics Data System). These keywords should:
   - Use precise academic terminology
   - Include both broad and specific terms
   - Consider synonyms and alternative phrasings
   - Be optimized for scientific literature retrieval

**Research Topic**: "{topic}"

**Output Format** (return as valid JSON):
{{
    "sub_questions": [
        "What are the fundamental concepts and definitions of {topic}?",
        "How has {topic} evolved historically?",
        "What are the current methodologies used in {topic}?",
        "What are the recent advances in {topic}?",
        "What are the practical applications of {topic}?",
        "What are the current challenges in {topic}?",
        "What are the future directions for {topic}?"
    ],
    "keyword_sets": {{
        "sub_question_1": ["keyword1", "keyword2", "keyword3"],
        "sub_question_2": ["keyword4", "keyword5", "keyword6"],
        ...
    }}
}}

Generate the research plan for "{topic}" now.
"""

    # Enhanced keyword generation prompt for ADS optimization
    KEYWORD_GENERATOR_PROMPT = """
You are an expert in academic database search optimization, particularly for the NASA ADS (Astrophysics Data System). 

For the research question: "{sub_question}"
Related to the main topic: "{main_topic}"

Generate 5-8 highly optimized keyword combinations for ADS paper retrieval. Consider:

1. **Academic terminology**: Use precise scientific terms
2. **Search variations**: Include synonyms and alternative phrasings  
3. **Specificity levels**: Mix broad and specific terms
4. **Field-specific jargon**: Include domain-specific terminology
5. **Methodological terms**: Include relevant techniques and approaches

Return as a JSON array of keyword strings:
["keyword combination 1", "keyword combination 2", ...]

Focus on maximizing relevant paper retrieval from academic databases.
"""

    # --------------------------------------------------------------------------
    # Step 2: Web Information Synthesis Agent (Web Synthesizer Agent)
    # --------------------------------------------------------------------------
    WEB_SYNTHESIZER_PROMPT = """
You are an expert information analyst specializing in synthesizing web-based research content. Your task is to quickly analyze and synthesize multiple web sources about a specific research question into a coherent, informative summary.

**Research Question**: "{sub_question}"

**Web Information Sources**:
---
{web_snippets}
---

Please synthesize the above information into a well-structured 200-300 word summary that:
1. Directly addresses the research question
2. Integrates key insights from multiple sources
3. Maintains scientific accuracy and objectivity
4. Highlights the most current and relevant information
5. Uses clear, professional academic language

Focus on extracting actionable insights and key findings relevant to the research question.
"""

    # --------------------------------------------------------------------------
    # Step 3: Enhanced Paper Analysis Agent (Paper Analyzer Agent)
    # --------------------------------------------------------------------------
    PAPER_ANALYZER_PROMPT = """
You are a distinguished academic researcher and literature analysis expert known for precise, insightful paper analysis. Your task is to analyze an academic paper's abstract and extract structured core information.

**Main Research Topic**: "{topic}"
**Paper Title**: "{paper_title}"

**Paper Abstract**:
---
{paper_abstract}
---

Analyze this paper and extract structured information. If any information is not explicitly mentioned in the abstract, use your expertise to make reasonable inferences or indicate "Not specified in abstract".

**Output Format** (return valid JSON only):
{{
    "short_summary": "Concise summary of the paper's core contribution or findings (2-3 sentences)",
    "relevance_to_topic": "Explain how this paper relates to the main research topic '{topic}' and its significance in the field",
    "research_subject": "What specific subject/object does this paper study? (e.g., specific celestial objects, algorithms, materials, phenomena)",
    "methodology": "What key research methods, techniques, or experimental approaches were used? (e.g., Monte Carlo simulation, spectral analysis, machine learning models)",
    "data_used": "What data sources were utilized? (e.g., Hubble telescope observations, clinical trial data, public datasets, simulation data)",
    "key_findings_or_results": [
        "Key finding or result 1 (concise statement)",
        "Key finding or result 2 (concise statement)",
        "Key finding or result 3 (concise statement)"
    ],
    "technical_significance": "What is the technical or methodological significance of this work?",
    "future_implications": "What are the potential implications or future research directions suggested by this work?"
}}

Ensure your output is valid JSON with no additional explanatory text.
"""

    # Enhanced batch analysis prompt for processing multiple papers
    BATCH_PAPER_ANALYZER_PROMPT = """
You are a distinguished academic researcher specializing in comprehensive literature analysis. Your task is to analyze multiple academic papers simultaneously and extract structured information for each.

**Main Research Topic**: "{topic}"

**Papers to Analyze**:
{papers_info}

For each paper, analyze the abstract and extract structured information. Return a JSON array where each element corresponds to one paper in the same order as provided.

**Output Format** (return valid JSON array only):
[
    {{
        "paper_index": 1,
        "short_summary": "Concise summary of core contribution",
        "relevance_to_topic": "Relevance to main topic '{topic}'",
        "research_subject": "Specific research subject/object",
        "methodology": "Key research methods and techniques",
        "data_used": "Data sources utilized",
        "key_findings_or_results": ["Finding 1", "Finding 2", "Finding 3"],
        "technical_significance": "Technical significance",
        "future_implications": "Future research implications"
    }},
    ...
]

Analyze all {paper_count} papers and return the structured analysis.
"""

    # --------------------------------------------------------------------------
    # Step 4: Research Report Writing Agent (Writer Agent)
    # --------------------------------------------------------------------------
    MASTER_WRITER_PROMPT = """
You are a distinguished academic writer and research synthesis expert. Your task is to create a comprehensive, professional research report that integrates multiple information sources into a coherent, insightful analysis.

**Research Topic**: "{topic}"

**Information Sources**:

**Web Research Summary**:
{web_summaries}

**Academic Paper Analysis**:
{structured_paper_analyses}

**Historical Timeline**:
{timeline}

Create a comprehensive research report with the following structure:

# {topic}: Comprehensive Research Report

## Executive Summary
[Provide a 200-word executive summary highlighting key findings and insights]

## Introduction and Background
[Establish context and importance of the research topic]

## Current State of Knowledge
[Synthesize findings from academic literature and web sources]

## Key Methodologies and Approaches
[Describe main research methods and techniques in the field]

## Recent Advances and Breakthroughs
[Highlight significant recent developments]

## Applications and Practical Implementations
[Discuss real-world applications and use cases]

## Current Challenges and Limitations
[Identify key obstacles and unresolved issues]

## Future Directions and Opportunities
[Outline promising research directions and potential developments]

## Conclusion
[Synthesize key insights and provide overall assessment]

## References and Further Reading
[List key papers and sources referenced]

Ensure the report is:
- Academically rigorous and well-structured
- Approximately 2000-3000 words
- Integrates all provided information sources
- Uses professional academic language
- Provides actionable insights and clear conclusions
"""

    INNOVATION_PROMPT = """
You are a visionary research strategist and innovation expert. Based on the comprehensive research report provided, develop innovative research proposals that push the boundaries of current knowledge.

**Research Report**:
{research_report}

Create an innovative research proposal with the following structure:

# Innovative Research Proposal

## Research Vision
[Articulate a bold, forward-thinking research vision]

## Identified Knowledge Gaps
[Highlight specific gaps in current understanding]

## Proposed Research Directions
[Detail 3-5 innovative research directions with:]
- Novel hypotheses or research questions
- Innovative methodological approaches
- Potential breakthrough applications
- Expected impact and significance

## Methodological Innovations
[Propose new or enhanced research methodologies]

## Technology Integration Opportunities
[Identify ways to leverage emerging technologies]

## Interdisciplinary Collaboration Potential
[Suggest valuable cross-disciplinary partnerships]

## Expected Outcomes and Impact
[Describe potential scientific and practical impacts]

## Implementation Roadmap
[Provide a high-level timeline and milestones]

Focus on:
- Scientific novelty and innovation potential
- Feasibility within current technological constraints
- Potential for significant impact on the field
- Clear articulation of value proposition
"""

    FEASIBILITY_PROMPT = """
You are a senior research evaluation expert specializing in scientific feasibility analysis. Your task is to provide a comprehensive, objective assessment of the proposed research directions.

**Research Context**:
{research_report}

**Proposed Innovation**:
{proposal}

Conduct a thorough feasibility analysis with the following structure:

# Feasibility Analysis Report

## Executive Assessment
[Provide overall feasibility rating and key recommendations]

## Scientific Feasibility
### Theoretical Foundation
- Strength of underlying scientific principles
- Consistency with established knowledge
- Potential for paradigm shifts

### Methodological Viability
- Availability of required research methods
- Technical complexity assessment
- Validation and verification approaches

## Technical Feasibility
### Resource Requirements
- Equipment and infrastructure needs
- Computational requirements
- Specialized expertise requirements

### Technology Readiness
- Current state of enabling technologies
- Development timeline for required tools
- Risk assessment for technical dependencies

## Economic Feasibility
### Funding Requirements
- Estimated budget ranges
- Potential funding sources
- Cost-benefit analysis

### Return on Investment
- Expected scientific value
- Potential commercial applications
- Long-term economic impact

## Risk Assessment
### Scientific Risks
- Probability of negative results
- Alternative approaches if primary methods fail
- Mitigation strategies

### Technical Risks
- Technology development uncertainties
- Infrastructure dependencies
- Timeline risks

## Implementation Recommendations
### Phased Approach
- Recommended implementation phases
- Key milestones and decision points
- Resource allocation strategy

### Success Metrics
- Quantifiable success indicators
- Timeline for achieving milestones
- Performance evaluation criteria

## Overall Recommendation
[Provide clear recommendation with supporting rationale]

Maintain objectivity and provide evidence-based assessments throughout the analysis.
"""
