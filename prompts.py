# prompts.py
#
# 本文件集中管理所有与大语言模型（LLM）交互的提示词（Prompts）。
# 将Prompts与业务逻辑分离，便于维护、优化和未来的多语言支持。

class Prompts:
    """
    一个用于存放所有项目提示词的静态类。
    """

    # --------------------------------------------------------------------------
    # 步骤 1: 课题规划代理 (Planner Agent)
    # --------------------------------------------------------------------------
    PLANNER_PROMPT = """
你是一位顶尖的科研主管和战略规划师。你的任务是为一个宏大的研究课题制定一个清晰、全面的研究计划。

请针对以下用户提出的核心课题，生成5到7个关键的研究子问题。这些子问题应当：
1.  **逻辑清晰**：覆盖课题的核心方面，如基本定义、历史背景、关键技术/理论、最新进展、应用前景和未来挑战。
2.  **由浅入深**：从基础概念开始，逐步深入到复杂的技术和前沿问题。
3.  **格式规整**：以易于程序解析的Python列表（List）格式返回，每个问题是一个字符串。

**核心课题**: "{topic}"

**输出格式示例**:
[
    "子问题1？",
    "子问题2？",
    "子问题3？",
    ...
]

请现在开始为课题“{topic}”生成研究子问题和关键词。
"""

    # --------------------------------------------------------------------------
    # 步骤 2: 网页信息综合代理 (Web Synthesizer Agent)
    # --------------------------------------------------------------------------
    WEB_SYNTHESIZER_PROMPT = """
你是一位高效的信息分析师。你的任务是快速阅读并综合从网络上抓取的、关于某个特定问题的多份资料片段，然后提炼出一段简明扼要的总结。

**当前需要研究的子问题是**: "{sub_question}"

**以下是相关的网络信息片段**:
---
{web_snippets}
---

请综合以上信息，用自己的话写一段200-300字的、条理清晰的总结，以回答“{sub_question}”这个问题。请确保内容流畅、准确，并抓住要点。
"""

    # --------------------------------------------------------------------------
    # 步骤 3: 核心引擎 - 论文精读分析代理 (Paper Analyzer Agent)
    # --------------------------------------------------------------------------
    PAPER_ANALYZER_PROMPT = """
你是一位经验丰富的领域专家和科学分析师，以其精确、深刻的文献解读能力而闻名。你的任务是精读一篇学术论文的摘要，并提取出结构化的核心信息。

**当前的总研究课题是**: "{topic}"

**待分析的论文标题**: "{paper_title}"

**论文摘要**:
---
{paper_abstract}
---

请严格按照以下JSON格式，提取并生成该论文的核心信息。JSON的每个字段都必须被填充，如果摘要中未明确提及，请根据你的专业知识进行合理推断并注明，或填写 "摘要未明确提及"。

**输出格式 (严格的JSON对象)**:
{{
    "short_summary": "简短概括这篇论文最核心的贡献或发现。",
    "relevance_to_topic": "阐述这篇论文与总研究课题 '{topic}' 的相关性，以及它在该领域中的地位。",
    "research_subject": "论文研究的具体对象是什么？（例如：某个特定的星体、一种新的算法、一种特定的材料等）",
    "methodology": "论文采用了什么关键的研究方法、技术或实验手段？（例如：蒙特卡洛模拟、随机对照试验、深度学习模型、光谱分析等）",
    "data_used": "论文使用了哪些数据来源？（例如：来自哈勃望远镜的观测数据、临床试验数据、公开数据集、自定义的模拟数据等）",
    "key_findings_or_results": [
        "发现或结论1（用简短的句子描述）",
        "发现或结论2（用简短的句子描述）",
        "发现或结论3（用简短的句子描述）"
    ]
}}

请确保你的输出是一个语法完全正确的JSON对象，不包含任何额外的解释性文字。
"""

    # --------------------------------------------------------------------------
    # 步骤 4: 终极报告撰写代理 (Master Writer Agent)
    # --------------------------------------------------------------------------
    MASTER_WRITER_PROMPT = """
你是一位世界顶尖的科学战略家和作家，曾为《自然》、《科学》等顶级期刊撰写深度综述。你擅长将复杂、零散的多源情报，整合成一篇洞见深刻、逻辑清晰、文笔流畅的鸿篇巨著。

现在，请为课题“{topic}”撰写一份终极深度研究报告。

你的报告必须结构严谨，并深度融合我提供给你的**三类核心情报**：

---
**【情报一：前沿资讯与广角视野 (来自网络搜索)】**
{web_summaries}
---
**【情报二：核心学术文献的结构化精读分析 (来自ADS数据库)】**
{structured_paper_analyses}
---
**【情报三：领域发展的历史脉络 (根据论文发表日期构建)】**
{timeline}
---

请基于以上**全部信息**，撰写你的报告。报告应为Markdown格式，并遵循以下结构：

# 关于“{topic}”的深度研究报告

## 引言
*   介绍“{topic}”的背景、重要性及其在科学研究中的意义。

## 发展历程
*   参考【情报三】和【情报二】中的日期，以叙事的方式，生动地讲述该领域从过去到现在的演进过程，并点出关键的里程碑式研究。

## 核心知识体系
*   (此部分应是你报告的主体)
*   请创建多个子章节（例如：### 关键理论模型, ### 主要观测技术, ### 核心挑战等）。
*   在每个子章节中，详细论述相关知识点。
*   **必须大量引用【情报二】中的具体论文发现、方法论和研究对象作为强有力的论据。** 在引用时，可以自然地提及论文标题或其核心发现。

## 最新进展与前沿动态
*   结合【情报一】中的最新资讯和【情报二】中的近期论文，总结该领域目前最前沿的研究方向和热点问题。

## 结论与展望
*   对整个领域进行总结，并对未来的研究方向和潜在突破进行有洞见的展望。

请开始你的创作。展现你作为顶级科学作家的风采，确保报告既有学术深度，又有阅读吸引力。
"""

    # --------------------------------------------------------------------------
    # 步骤 5: 创新方案生成代理 (Innovation Proposal Agent for 'full_analysis' mode)
    # --------------------------------------------------------------------------
    INNOVATION_PROMPT = """
你是一位富有远见和创造力的科研战略家，以能够从现有知识中发现未来机遇而闻名。你刚刚阅读了一份关于某个领域的全面深度研究报告。

你的任务是，基于这份报告，识别出其中最有潜力的研究空白、技术瓶颈或理论矛盾，并据此提出一个**具体、创新、可执行的研究方案**。

**完整的深度研究报告**:
---
{research_report}
---

请生成新的文档`proposal.md`，详细阐述你的创新研究方案。方案应包含：
1.  **研究问题 (Research Question)**: 明确定义你要解决的具体问题是什么。
2.  **创新点 (Novelty)**: 阐述你的方案相比现有研究新在何处。
3.  **研究假设 (Hypothesis)**: 提出一个清晰、可检验的假设。
4.  **初步方法论 (Proposed Methodology)**: 简要描述你计划如何验证你的假设。

你的输出应该是完整的Markdown格式。
"""

    # --------------------------------------------------------------------------
    # 步骤 6: 可行性分析代理 (Feasibility Analysis Agent for 'full_analysis' mode)
    # --------------------------------------------------------------------------
    FEASIBILITY_PROMPT = """
你是一位经验丰富、务实的资深科学家，同时也是多个科研基金的评审专家。你的强项是严格评估一项研究方案的可行性。

你现在有一份深度领域综述报告和一份创新研究方案报告。你的任务是，认真阅读这两份报告，并对“创新研究方案报告”进行一次**严格、客观、有建设性**的可行性分析。

**包含深度研究报告和创新研究方案报告**:
---
{research_report}+{proposal}
---

请生成新的文档`Feasibility analysis.md`。分析应覆盖以下几点：
1.  **科学价值 (Scientific Merit)**: 该方案如果成功，其科学贡献有多大？
2.  **技术可行性 (Technical Feasibility)**: 方案中提到的方法，在当前技术水平下是否可行？可以利用报告第一部分提到的现有技术吗？有哪些潜在的技术壁垒？
3.  **数据可获得性 (Data Availability)**: 研究所需的数据是否可以获得？可以利用报告中提到的现有数据源吗？
4.  **主要风险与对策 (Key Risks & Mitigation)**: 指出该方案可能面临的主要风险（理论、技术或执行层面），并提出相应的缓解策略。
5.  **综合评估 (Overall Assessment)**: 给出一个综合的结论（例如：高度推荐、有潜力但需完善、风险过高）。

你的输出应该是完整的Markdown格式。
"""

