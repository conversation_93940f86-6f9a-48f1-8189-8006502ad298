
---

# AI-Powered Research Assistant (AI驱动的科研助理)

## 1. 项目概述 (Project Overview)

本项目是一个高度智能化、自动化的科研助理工具。它旨在将研究人员从繁重、耗时的文献调研和信息整合工作中解放出来，使其能专注于最核心的创新与思考。

通过模拟一位顶级研究员的工作流程，本工具能够针对用户提出的任何科研课题，完成从**宏观探索**、**微观精读**到**时序叙事**的全方位信息处理，并最终生成两份极具价值的报告：一份是堪比专业咨询公司出品的**深度研究报告**，另一份是可供反复查阅的**精读分析笔记**。

---

## 2. 核心设计理念：三位一体 (Core Philosophy: The Trinity)

我们的系统之所以强大，源于其独特的三位一体情报融合机制，确保了最终报告的广度、深度与历史感：

1.  **宏观探索 (Macro-level Exploration)**: 借鉴 `gpt-researcher` 的思想，系统首先会主动生成关键子问题，并利用网络搜索引擎（tavily Search api）抓取最新的新闻、学术科研项目网页、博客文章和多样的观点，从而构建起对课题的广阔视野。

2.  **微观精读 (Micro-level Analysis)**: 这是我们系统的“心脏”。系统会通过NASA ADS（天体物理数据系统）等学术数据库，精准定位领域内的核心、高影响力文献。随后，它会**逐篇**对这些文献进行“精读”，通过大语言模型（LLM）提取结构化的关键信息，如研究方法、实验对象、核心结论等。

3.  **时序叙事 (Temporal Narrative)**: 系统会将所有核心文献按发表日期排序，并提取其“简短总结”，从而构建出一条清晰的领域发展时间线。这为最终的报告注入了宝贵的历史纵深和叙事感。

---

## 3. 功能特性 (Features)

*   **双模式执行**: 用户可在配置文件中轻松选择 `deep_research`（深度研究）或 `full_analysis`（全面分析）模式。
*   **自动化双轨信息采集**: 并行使用网络搜索引擎和学术数据库，确保信息来源的全面性。
*   **结构化文献分析**: 对每一篇核心论文进行深度解析，提取关键信息并生成结构化数据。
*   **智能报告生成**: 融合宏观、微观、时序三类情报，生成逻辑严谨、内容详实的深度研究报告。
*   **过程即价值**: 自动生成一份详细的 `research_details.md`，记录每一篇论文的精读笔记，成为一个可复用的知识库。
*   **创新方案建议 (仅限 `full_analysis` 模式)**: 在深度研究的基础上，进一步提出创新的研究方向和方案。
*   **可行性分析 (仅限 `full_analysis` 模式)**: 对提出的新方案进行严格、有据可依的可行性评估。
*   **高度可配置**: 所有API密钥、搜索参数、LLM模型选择均可在 `config.py` 中进行配置。

---

## 4. 执行模式详解 (Execution Modes Explained)

#### A. 模式一: `deep_research` (深度研究)

此模式旨在为用户提供一份关于某个科研领域的**终极参考指南**。

*   **最终产出**:
    1.  `research_report.md`: 一份融合了广度、深度和历史叙事感的终极研究综述报告。
    2.  `research_details.md`: 一份对所有参考文献的逐篇、结构化精读笔记。

#### B. 模式二: `full_analysis` (全面分析)

此模式在深度研究的基础上，扮演一位顶级的**科研战略顾问**，提出真正有洞见的未来方向。

*   **最终产出**:
    1.  `research_report.md`: 包含三个顶级部分的报告：
        *   **第一部分：深度研究与领域综述** (直接采纳 `deep_research` 模式的输出)
        *   **第二部分：基于此的创新方案与可行性分析**
        *   **第三部分：方案可行性分析**
    2.  `research_details.md`: （同上）一份详细的精读笔记。

---

## 5. 项目结构 (Project Structure)

/
├── main.py                 # 程序主入口，负责调度和编排整个流程
├── config.py               # 配置文件，用于存放API密钥、模式选择、搜索参数等
├── requirements.txt        # 项目依赖的Python包
├── README.md               # 本文档
│
├── agents/                 # 存放与LLM交互的“智能代理”
│   ├── planner_agent.py    # 负责生成研究子问题的代理
│   ├── synthesizer_agent.py# 负责综合多源信息的代理
│   └── writer_agent.py     # 负责撰写最终报告的代理
│
├── clients/                # 存放与外部API交互的客户端
│   ├── ads_client.py       # 封装NASA ADS API的客户端
│   └── web_search_client.py# 封装tavily Search API的客户端
│
├── models/                 # 存放数据模型
│   └── paper.py            # 定义Paper对象，包含标题、作者、AI分析结果等字段
│
├── utils/                  # 存放工具函数
│   └── report_generator.py # 负责将最终内容格式化并写入Markdown文件的工具
│
└── outputs/                # 存放所有最终生成的报告文件
    ├── research_report.md
    └── research_details.md

---

## 6. 安装与配置 (Installation & Setup)

### 6.1 环境要求

- Python 3.8 或更高版本
- 稳定的网络连接
- 至少 2GB 可用磁盘空间

### 6.2 快速开始

1.  **克隆仓库**
    ```bash
    git clone <your_repository_url>
    cd AI-Research-Assistant
    ```

2.  **创建虚拟环境（推荐）**
    ```bash
    python -m venv venv

    # Windows
    venv\Scripts\activate

    # macOS/Linux
    source venv/bin/activate
    ```

3.  **安装依赖**
    ```bash
    pip install -r requirements.txt
    ```

4.  **配置环境变量**
    ```bash
    # 复制环境变量模板
    cp .env.example .env

    # 编辑 .env 文件，填入你的API密钥
    ```

### 6.3 API密钥配置

在 `.env` 文件中配置以下必需的API密钥：

#### 必需配置
- `ADS_API_TOKEN`: NASA ADS API令牌 ([获取地址](https://ui.adsabs.harvard.edu/user/settings/token))
- `OPENAI_API_KEY` 或 `ANTHROPIC_API_KEY`: LLM API密钥

#### 可选配置
- `TAVILY_API_KEY`: Tavily搜索API密钥 ([获取地址](https://tavily.com))
- `GOOGLE_SEARCH_API_KEY` + `GOOGLE_CSE_ID`: Google自定义搜索API

### 6.4 配置验证

运行健康检查确保配置正确：
```bash
python main.py --health
```

---

## 7. 如何运行 (How to Run)

### 7.1 基本使用

1.  **交互式运行**
    ```bash
    python main.py
    ```
    程序会引导你输入研究课题并执行完整的研究流程。

2.  **查看帮助信息**
    ```bash
    python main.py --help
    ```

3.  **检查系统状态**
    ```bash
    python main.py --health
    ```

4.  **查看当前配置**
    ```bash
    python main.py --config
    ```

### 7.2 输出文件

执行完成后，在 `outputs/` 文件夹下找到生成的报告：

#### 深度研究模式 (`deep_research`)
- `research_report.md` - 主研究报告
- `research_details.md` - 详细研究笔记
- `session_summary.json` - 会话摘要

#### 全面分析模式 (`full_analysis`)
- `research_report.md` - 主研究报告
- `research_details.md` - 详细研究笔记
- `proposal.md` - 创新研究方案
- `feasibility_analysis.md` - 可行性分析
- `session_summary.json` - 会话摘要

### 7.3 使用示例

```bash
# 1. 检查系统健康状态
python main.py --health

# 2. 运行研究（交互式）
python main.py

# 输入示例课题：
# "深度学习在天体物理学中的应用"

# 3. 查看生成的报告
ls outputs/
```

---

## 8. 详细工作流 (Detailed Workflow)

本工具的内部工作流程如下：

1.  **初始化与规划**: `main.py` 读取用户输入和 `config.py` 配置。`PlannerAgent` 被调用，将用户课题分解为一系列关键的子问题和关键词。

2.  **双轨信息采集**: 对于每个子问题，系统并行启动：
    *   `web_search_client` 进行广泛的网络搜索，获取前沿资讯。
    *   `ads_client` 进行精准的学术文献搜索，获取核心论文。

3.  **初步信息综合**: `SynthesizerAgent` 将每个子问题下的网络搜索结果初步整理成简短的摘要。

4.  **核心引擎 - 文献精读**:
    *   系统汇总所有从ADS获取的论文，并去重。
    *   进入核心循环，**逐篇**处理每一篇论文。
    *   调用 `SynthesizerAgent`（或一个专门的 `PaperAnalysisAgent`），根据我们精心设计的Prompt，从论文摘要中提取结构化信息（简短总结、研究方法、数据等）。
    *   每处理完一批（由用户在配置文件中设置，初始为5篇），就将其结构化信息追加写入 `outputs/research_details.md`。

5.  **终极报告合成**:
    *   `WriterAgent` 被调用，并接收一个包含我们**三位一体**情报的庞大Prompt：
        *   **情报一**: 所有网络搜索的初步摘要。
        *   **情报二**: 所有论文的结构化精读分析结果（JSON格式）。
        *   **情报三**: 根据论文发表日期和简短总结构建的领域发展时间线。
    *   `WriterAgent` 基于这些全面的信息，撰写出最终的 `research_report.md`。

6.  **战略分析 (仅限 `full_analysis` 模式)**:
    *   如果模式为 `full_analysis`，程序将刚才生成的深度报告作为上下文。
    *   再次调用 `WriterAgent`，先后要求它**提出创新方案**并进行**可行性分析**。
    *   将这两部分分别生成对应的md文档。

7.  **完成**: 程序结束，所有产出均保存在 `outputs/` 目录中。

---

## 9. 测试 (Testing)

### 9.1 运行测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定类型的测试
python run_tests.py unit          # 单元测试
python run_tests.py integration   # 集成测试
python run_tests.py models        # 模型测试

# 生成覆盖率报告
python run_tests.py --coverage

# 详细输出
python run_tests.py --verbose
```

### 9.2 测试依赖

```bash
# 检查测试依赖
python run_tests.py --check-deps

# 安装测试依赖
pip install pytest pytest-cov pytest-mock
```

---

## 10. 故障排除 (Troubleshooting)

### 10.1 常见问题

**问题**: API连接失败
```
解决方案:
1. 检查网络连接
2. 验证API密钥是否正确
3. 运行 python main.py --health 检查服务状态
```

**问题**: 内存不足
```
解决方案:
1. 减少 MAX_PAPERS_TO_ANALYZE 配置
2. 降低 PAPER_BATCH_SIZE 配置
3. 确保至少有2GB可用内存
```

**问题**: 生成的报告质量不佳
```
解决方案:
1. 检查输入的研究课题是否具体明确
2. 确保相关领域有足够的学术文献
3. 尝试调整 LLM_TEMPERATURE 参数
```

### 10.2 日志调试

启用详细日志：
```bash
# 在 .env 文件中设置
LOG_LEVEL=DEBUG
VERBOSE_LOGGING=true
```

查看日志文件：
```bash
tail -f research_assistant.log
```

### 10.3 性能优化

```bash
# 在 .env 文件中调整以下参数
MAX_PAPERS_TO_ANALYZE=30        # 减少论文数量
PAPER_BATCH_SIZE=3              # 减少批处理大小
MAX_CONCURRENT_REQUESTS=2       # 减少并发请求
REQUEST_DELAY=2.0               # 增加请求间隔
```

---

## 11. 高级配置 (Advanced Configuration)

### 11.1 自定义配置

所有配置项都可以通过环境变量自定义：

```bash
# 执行模式
EXECUTION_MODE=full_analysis

# LLM配置
LLM_PROVIDER=anthropic
LLM_TEMPERATURE=0.8
LLM_MAX_TOKENS=6000

# 搜索配置
WEB_SEARCH_RESULTS_PER_QUERY=8
ADS_SEARCH_RESULTS_PER_QUERY=15

# 高级选项
ENABLE_CACHE=true
PAPER_SIMILARITY_THRESHOLD=0.9
OUTPUT_LANGUAGE=en
```

### 11.2 缓存配置

启用缓存以提高性能：
```bash
ENABLE_CACHE=true
CACHE_DIR=cache
```

### 11.3 多语言支持

```bash
# 设置输出语言
OUTPUT_LANGUAGE=zh    # 中文
OUTPUT_LANGUAGE=en    # 英文
```

---
