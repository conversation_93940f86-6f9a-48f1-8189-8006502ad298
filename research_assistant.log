2025-07-20 13:40:28 - main - INFO - 初始化AI驱动的科研助理...
2025-07-20 13:40:28 - main - INFO - 执行模式: full_analysis
2025-07-20 13:40:28 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:40:28 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:28 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:28 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:40:28 - main - INFO - 执行系统健康检查...
2025-07-20 13:40:31 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:40:31 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 13:40:34 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:35 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:35 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:40:35 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 13:40:35 - clients.llm_client - ERROR - openai-compatible API error: 'Config' object has no attribute 'LLM_TEMPERATURE'
2025-07-20 13:40:35 - main - INFO - 健康检查完成: unhealthy
2025-07-20 13:40:35 - main - INFO - 服务状态: 4健康, 0警告, 1异常
2025-07-20 13:40:35 - main - INFO - ✅ Configuration: Configuration is valid
2025-07-20 13:40:35 - main - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 13:40:35 - main - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 13:40:35 - main - ERROR - 组件初始化失败: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 13:41:50 - main - INFO - 初始化AI驱动的科研助理...
2025-07-20 13:41:50 - main - INFO - 执行模式: full_analysis
2025-07-20 13:41:50 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:41:50 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:51 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:51 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:41:51 - main - INFO - 执行系统健康检查...
2025-07-20 13:41:52 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:41:52 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 13:41:55 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:56 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:56 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:41:56 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 13:41:56 - clients.llm_client - ERROR - openai-compatible API error: 'Config' object has no attribute 'LLM_TEMPERATURE'
2025-07-20 13:41:56 - main - INFO - 健康检查完成: unhealthy
2025-07-20 13:41:56 - main - INFO - 服务状态: 4健康, 0警告, 1异常
2025-07-20 13:41:56 - main - INFO - ✅ Configuration: Configuration is valid
2025-07-20 13:41:56 - main - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 13:41:56 - main - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 13:41:56 - main - ERROR - 组件初始化失败: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 14:19:44 - __main__ - INFO - 初始化AI驱动的科研助理...
2025-07-20 14:19:44 - __main__ - INFO - 执行模式: deep_research
2025-07-20 14:19:44 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:19:44 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:45 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:45 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:19:45 - __main__ - INFO - 执行系统健康检查...
2025-07-20 14:19:46 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:19:46 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 14:19:48 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:49 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:49 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:19:49 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:19:49 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:19:52 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:20:00 - __main__ - INFO - 健康检查完成: unhealthy
2025-07-20 14:20:00 - __main__ - INFO - 服务状态: 4健康, 0警告, 1异常
2025-07-20 14:20:00 - __main__ - INFO - ✅ Configuration: Configuration is valid
2025-07-20 14:20:00 - __main__ - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 14:20:00 - __main__ - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 14:20:00 - __main__ - ERROR - 组件初始化失败: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 14:20:00 - root - ERROR - Main execution failed: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
Traceback (most recent call last):
  File "U:\Cursor\My_Deep-Research\main.py", line 551, in main
    assistant = ResearchAssistant()
                ^^^^^^^^^^^^^^^^^^^
  File "U:\Cursor\My_Deep-Research\main.py", line 45, in __init__
    self._initialize_components()
  File "U:\Cursor\My_Deep-Research\main.py", line 70, in _initialize_components
    self._test_connections()
  File "U:\Cursor\My_Deep-Research\main.py", line 89, in _test_connections
    raise APIConnectionError(
utils.exceptions.APIConnectionError: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 14:31:52 - main - INFO - 初始化AI驱动的科研助理...
2025-07-20 14:31:52 - main - INFO - 执行模式: deep_research
2025-07-20 14:31:52 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:31:52 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:31:53 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:31:53 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:31:53 - main - INFO - 执行系统健康检查...
2025-07-20 14:31:54 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:31:54 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 14:31:56 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:31:57 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:31:57 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:31:57 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:31:57 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:31:58 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:32:05 - main - INFO - 健康检查完成: healthy
2025-07-20 14:32:05 - main - INFO - 服务状态: 5健康, 0警告, 0异常
2025-07-20 14:32:05 - main - INFO - ✅ Configuration: Configuration is valid
2025-07-20 14:32:05 - main - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 14:32:05 - main - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 14:32:05 - main - INFO - ✅ LLM API: LLM API (openai-compatible) is accessible
2025-07-20 14:32:05 - main - INFO - ✅ File System: File system accessible, 8.0GB available
2025-07-20 14:32:05 - main - INFO - 所有组件初始化成功
2025-07-20 14:33:41 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:33:41 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 14:33:43 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:33:44 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:33:44 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:33:44 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:33:44 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:33:46 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:35:21 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:35:21 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 14:35:24 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:35:24 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:35:24 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:35:24 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:35:24 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:35:26 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:35:54 - __main__ - INFO - 初始化AI驱动的科研助理...
2025-07-20 14:35:54 - __main__ - INFO - 执行模式: deep_research
2025-07-20 14:35:54 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:35:54 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:35:55 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:35:55 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:35:55 - __main__ - INFO - 执行系统健康检查...
2025-07-20 14:35:56 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:35:56 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 14:35:59 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:36:00 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:36:00 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:36:00 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:36:00 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:36:01 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:36:08 - __main__ - INFO - 健康检查完成: healthy
2025-07-20 14:36:08 - __main__ - INFO - 服务状态: 5健康, 0警告, 0异常
2025-07-20 14:36:08 - __main__ - INFO - ✅ Configuration: Configuration is valid
2025-07-20 14:36:08 - __main__ - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 14:36:08 - __main__ - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 14:36:08 - __main__ - INFO - ✅ LLM API: LLM API (openai-compatible) is accessible
2025-07-20 14:36:08 - __main__ - INFO - ✅ File System: File system accessible, 8.0GB available
2025-07-20 14:36:08 - __main__ - INFO - 所有组件初始化成功
2025-07-20 14:36:08 - __main__ - INFO - 开始研究课题: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 14:36:08 - agents.planner_agent - INFO - Generating research plan with confirmation for: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 14:36:08 - agents.planner_agent - INFO - Generation attempt 1/3
2025-07-20 14:36:08 - agents.planner_agent - INFO - Generating research plan for topic: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 14:36:09 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:36:09 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:36:10 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:36:40 - agents.planner_agent - INFO - Successfully parsed AI response: 5 questions, 11 general keywords, 5 sub-question keyword sets
2025-07-20 14:36:40 - agents.planner_agent - INFO - Generated 5 sub-questions and 36 keywords
2025-07-20 14:39:42 - agents.planner_agent - INFO - Research plan confirmed by user
2025-07-20 14:39:42 - __main__ - INFO - 开始增强的双轨信息采集...
2025-07-20 14:39:42 - utils.logger - INFO - 信息采集: 1/5 (20.0%) - 预计剩余: 0秒 - 处理子问题 1: What observational tracers and computational algorithms are most effective for inferring the 3D orientation of interstellar filaments from 2D projected data?
2025-07-20 14:39:43 - clients.web_search_client - INFO - Searching Tavily for: What observational tracers and computational algorithms are most effective for inferring the 3D orientation of interstellar filaments from 2D projected data?
2025-07-20 14:39:48 - clients.ads_client - INFO - Enhanced search for sub-question 0: Velocity gradients
2025-07-20 14:39:51 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 33
2025-07-20 14:39:51 - clients.ads_client - INFO - Enhanced search for sub-question 0: Magnetic field morphology
2025-07-20 14:39:53 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 33
2025-07-20 14:39:53 - clients.ads_client - INFO - Enhanced search for sub-question 0: Far-infrared polarimetry
2025-07-20 14:39:54 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 53
2025-07-20 14:39:54 - utils.logger - INFO - 信息采集: 2/5 (40.0%) - 预计剩余: 17秒 - 处理子问题 2: How can the observed alignment between magnetic field morphology, inferred from dust polarization, and filamentary structures be leveraged to constrain their 3D orientation relative to the line-of-sight?
2025-07-20 14:39:55 - clients.web_search_client - INFO - Searching Tavily for: How can the observed alignment between magnetic field morphology, inferred from dust polarization, and filamentary structures be leveraged to constrain their 3D orientation relative to the line-of-sight?
2025-07-20 14:40:00 - clients.ads_client - INFO - Enhanced search for sub-question 1: Synthetic observations
2025-07-20 14:40:02 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 32
2025-07-20 14:40:02 - clients.ads_client - INFO - Enhanced search for sub-question 1: Hub-filament systems
2025-07-20 14:40:03 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 60
2025-07-20 14:40:03 - clients.ads_client - INFO - Enhanced search for sub-question 1: Algorithm validation
2025-07-20 14:40:04 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 33
2025-07-20 14:40:04 - utils.logger - INFO - 信息采集: 3/5 (60.0%) - 预计剩余: 15秒 - 处理子问题 3: To what extent can velocity gradients and velocity-coherent structures, observed in molecular line emission (e.g., CO, NH3), be used as a kinematic tracer to disentangle projection effects and reconstruct the true spatial orientation of filaments?
2025-07-20 14:40:06 - clients.web_search_client - INFO - Searching Tavily for: To what extent can velocity gradients and velocity-coherent structures, observed in molecular line emission (e.g., CO, NH3), be used as a kinematic tracer to disentangle projection effects and reconstruct the true spatial orientation of filaments?
2025-07-20 14:40:11 - clients.ads_client - INFO - Enhanced search for sub-question 2: Magnetohydrodynamics (MHD)
2025-07-20 14:40:12 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 34
2025-07-20 14:40:12 - clients.ads_client - INFO - Enhanced search for sub-question 2: Filamentary Structures
2025-07-20 14:40:14 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 34
2025-07-20 14:40:14 - clients.ads_client - INFO - Enhanced search for sub-question 2: Kinematic tomography
2025-07-20 14:40:15 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 50
2025-07-20 14:40:16 - utils.logger - INFO - 信息采集: 4/5 (80.0%) - 预计剩余: 8秒 - 处理子问题 4: How do 3D orientation reconstruction techniques perform when applied to synthetic observations generated from magnetohydrodynamic (MHD) simulations of filament formation, and what are their inherent biases and limitations?
2025-07-20 14:40:19 - clients.web_search_client - INFO - Searching Tavily for: How do 3D orientation reconstruction techniques perform when applied to synthetic observations generated from magnetohydrodynamic (MHD) simulations of filament formation, and what are their inherent biases and limitations?
2025-07-20 14:40:24 - clients.ads_client - INFO - Enhanced search for sub-question 3: Mass accretion
2025-07-20 14:40:25 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 53
2025-07-20 14:40:25 - clients.ads_client - INFO - Enhanced search for sub-question 3: Velocity-coherent structures
2025-07-20 14:40:27 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 38
2025-07-20 14:40:27 - clients.ads_client - INFO - Enhanced search for sub-question 3: Radiative transfer modeling
2025-07-20 14:40:28 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 35
2025-07-20 14:40:28 - utils.logger - INFO - 信息采集: 5/5 (100.0%) - 预计剩余: 0秒 - 处理子问题 5: What are the physical implications of a filament's true 3D orientation on its stability, mass accretion rates, and the process of prestellar core formation within it?
2025-07-20 14:40:29 - clients.web_search_client - INFO - Searching Tavily for: What are the physical implications of a filament's true 3D orientation on its stability, mass accretion rates, and the process of prestellar core formation within it?
2025-07-20 14:40:34 - clients.ads_client - INFO - Enhanced search for sub-question 4: Hessian matrix
2025-07-20 14:40:35 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 42
2025-07-20 14:40:35 - clients.ads_client - INFO - Enhanced search for sub-question 4: Star Formation
2025-07-20 14:40:36 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 54
2025-07-20 14:40:36 - clients.ads_client - INFO - Enhanced search for sub-question 4: Filament finding algorithms
2025-07-20 14:40:37 - clients.ads_client - INFO - Retrieved 100 papers, selected 60, final count after deduplication: 57
2025-07-20 14:40:37 - utils.logger - INFO - 信息采集 完成! 总耗时: 54.9秒, 平均每项: 11.0秒
2025-07-20 14:40:37 - __main__ - INFO - 增强信息采集完成: 25个网络结果, 300篇论文
2025-07-20 14:40:37 - __main__ - INFO - 开始增强的论文精读分析...
2025-07-20 14:40:37 - agents.synthesizer_agent - INFO - Enhanced batch analyzing 300 papers
2025-07-20 14:40:37 - __main__ - ERROR - Enhanced batch analysis failed, falling back to traditional method: name 'config' is not defined
2025-07-20 14:40:37 - __main__ - INFO - 处理第 1 批论文 (8 篇)
2025-07-20 14:40:37 - agents.synthesizer_agent - INFO - Batch analyzing 8 papers
2025-07-20 14:40:37 - agents.synthesizer_agent - INFO - Analyzing paper 1/8: Fast report: surface deformation associated with the 2025 Dapu earthquake
2025-07-20 14:40:37 - agents.synthesizer_agent - INFO - Analyzing paper: Fast report: surface deformation associated with the 2025 Dapu earthquake
2025-07-20 14:40:38 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:40:38 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:40:39 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:41:04 - agents.synthesizer_agent - INFO - Successfully analyzed paper: Fast report: surface deformation associated with the 2025 Dapu earthquake
2025-07-20 14:41:04 - agents.synthesizer_agent - INFO - Analyzing paper 2/8: Interior second order Hölder regularity for Stokes systems
2025-07-20 14:41:04 - agents.synthesizer_agent - INFO - Analyzing paper: Interior second order Hölder regularity for Stokes systems
2025-07-20 14:41:05 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:41:05 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:41:06 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:41:33 - agents.synthesizer_agent - INFO - Successfully analyzed paper: Interior second order Hölder regularity for Stokes systems
2025-07-20 14:41:33 - agents.synthesizer_agent - INFO - Analyzing paper 3/8: Thermal Convection in a Higher Velocity Gradient and Higher Temperature Gradient Fluid
2025-07-20 14:41:33 - agents.synthesizer_agent - INFO - Analyzing paper: Thermal Convection in a Higher Velocity Gradient and Higher Temperature Gradient Fluid
2025-07-20 14:41:34 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:41:34 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:41:35 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:41:59 - agents.synthesizer_agent - INFO - Successfully analyzed paper: Thermal Convection in a Higher Velocity Gradient and Higher Temperature Gradient Fluid
2025-07-20 14:41:59 - agents.synthesizer_agent - INFO - Analyzing paper 4/8: Influence of the impeller structure and operating parameters of a high intensity conditioning system on the flotation of fine-grained pyrite
2025-07-20 14:41:59 - agents.synthesizer_agent - INFO - Analyzing paper: Influence of the impeller structure and operating parameters of a high intensity conditioning system on the flotation of fine-grained pyrite
2025-07-20 14:42:00 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:42:00 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:42:00 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:42:16 - agents.synthesizer_agent - INFO - Successfully analyzed paper: Influence of the impeller structure and operating parameters of a high intensity conditioning system on the flotation of fine-grained pyrite
2025-07-20 14:42:16 - agents.synthesizer_agent - INFO - Analyzing paper 5/8: Separation control applied to the turbulent flow around a NACA4412 wing section
2025-07-20 14:42:16 - agents.synthesizer_agent - INFO - Analyzing paper: Separation control applied to the turbulent flow around a NACA4412 wing section
2025-07-20 14:42:17 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:42:17 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:42:17 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 15:51:05 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 15:51:05 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 15:51:08 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 15:51:09 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 15:51:09 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 15:51:09 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-flash
2025-07-20 15:51:09 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-flash
2025-07-20 15:51:11 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 15:51:28 - __main__ - INFO - 初始化AI驱动的科研助理...
2025-07-20 15:51:28 - __main__ - INFO - 执行模式: deep_research
2025-07-20 15:51:28 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 15:51:28 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 15:51:32 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 15:51:33 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 15:51:33 - __main__ - INFO - 执行系统健康检查...
2025-07-20 15:51:34 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 15:51:34 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 15:51:38 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 15:51:41 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 15:51:41 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 15:51:41 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-flash
2025-07-20 15:51:41 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-flash
2025-07-20 15:51:42 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 15:51:45 - __main__ - INFO - 健康检查完成: healthy
2025-07-20 15:51:45 - __main__ - INFO - 服务状态: 5健康, 0警告, 0异常
2025-07-20 15:51:45 - __main__ - INFO - ✅ Configuration: Configuration is valid
2025-07-20 15:51:45 - __main__ - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 15:51:45 - __main__ - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 15:51:45 - __main__ - INFO - ✅ LLM API: LLM API (openai-compatible) is accessible
2025-07-20 15:51:45 - __main__ - INFO - ✅ File System: File system accessible, 8.0GB available
2025-07-20 15:51:45 - __main__ - INFO - 所有组件初始化成功
2025-07-20 15:51:45 - __main__ - INFO - 开始研究课题: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 15:51:45 - agents.planner_agent - INFO - Generating research plan with confirmation for: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 15:51:45 - agents.planner_agent - INFO - Generation attempt 1/3
2025-07-20 15:51:45 - agents.planner_agent - INFO - Generating research plan for topic: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 15:51:46 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-flash
2025-07-20 15:51:46 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-flash
2025-07-20 15:51:47 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 15:51:56 - agents.planner_agent - INFO - Successfully parsed AI response: 6 questions, 12 general keywords, 6 sub-question keyword sets
2025-07-20 15:51:56 - agents.planner_agent - INFO - Generated 6 sub-questions and 41 keywords
2025-07-20 16:01:02 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 16:01:02 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 16:01:04 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:01:05 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:01:05 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 16:01:05 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.0-flash
2025-07-20 16:01:05 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.0-flash
2025-07-20 16:01:06 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 16:02:00 - __main__ - INFO - 初始化AI驱动的科研助理...
2025-07-20 16:02:00 - __main__ - INFO - 执行模式: deep_research
2025-07-20 16:02:00 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 16:02:00 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:02:01 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:02:01 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 16:02:01 - __main__ - INFO - 执行系统健康检查...
2025-07-20 16:02:02 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 16:02:02 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 16:02:04 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:02:05 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:02:05 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 16:02:05 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.0-flash
2025-07-20 16:02:05 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.0-flash
2025-07-20 16:02:06 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 16:02:06 - __main__ - INFO - 健康检查完成: healthy
2025-07-20 16:02:06 - __main__ - INFO - 服务状态: 5健康, 0警告, 0异常
2025-07-20 16:02:06 - __main__ - INFO - ✅ Configuration: Configuration is valid
2025-07-20 16:02:06 - __main__ - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 16:02:06 - __main__ - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 16:02:06 - __main__ - INFO - ✅ LLM API: LLM API (openai-compatible) is accessible
2025-07-20 16:02:06 - __main__ - INFO - ✅ File System: File system accessible, 8.0GB available
2025-07-20 16:02:06 - __main__ - INFO - 所有组件初始化成功
2025-07-20 16:02:06 - __main__ - INFO - 开始研究课题: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 16:02:06 - agents.planner_agent - INFO - Generating research plan with confirmation for: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 16:02:06 - agents.planner_agent - INFO - Generation attempt 1/3
2025-07-20 16:02:06 - agents.planner_agent - INFO - Generating research plan for topic: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 16:02:07 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.0-flash
2025-07-20 16:02:07 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.0-flash
2025-07-20 16:02:08 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 16:02:13 - agents.planner_agent - INFO - Successfully parsed AI response: 7 questions, 12 general keywords, 7 sub-question keyword sets
2025-07-20 16:02:13 - agents.planner_agent - INFO - Generated 7 sub-questions and 47 keywords
2025-07-20 16:21:40 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 16:21:40 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 16:21:42 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:21:43 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:21:43 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 16:21:43 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.0-flash
2025-07-20 16:21:43 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.0-flash
2025-07-20 16:21:44 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 16:21:56 - __main__ - INFO - 初始化AI驱动的科研助理...
2025-07-20 16:21:56 - __main__ - INFO - 执行模式: deep_research
2025-07-20 16:21:56 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 16:21:56 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:21:57 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:21:57 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 16:21:57 - __main__ - INFO - 执行系统健康检查...
2025-07-20 16:21:58 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 16:21:58 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 16:22:00 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:22:00 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 16:22:00 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 16:22:00 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.0-flash
2025-07-20 16:22:00 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.0-flash
2025-07-20 16:22:01 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 16:22:02 - __main__ - INFO - 健康检查完成: healthy
2025-07-20 16:22:02 - __main__ - INFO - 服务状态: 5健康, 0警告, 0异常
2025-07-20 16:22:02 - __main__ - INFO - ✅ Configuration: Configuration is valid
2025-07-20 16:22:02 - __main__ - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 16:22:02 - __main__ - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 16:22:02 - __main__ - INFO - ✅ LLM API: LLM API (openai-compatible) is accessible
2025-07-20 16:22:02 - __main__ - INFO - ✅ File System: File system accessible, 8.0GB available
2025-07-20 16:22:02 - __main__ - INFO - 所有组件初始化成功
2025-07-20 16:22:02 - __main__ - INFO - 开始研究课题: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 16:22:02 - agents.planner_agent - INFO - Generating research plan with confirmation for: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 16:22:02 - agents.planner_agent - INFO - Generation attempt 1/3
2025-07-20 16:22:02 - agents.planner_agent - INFO - Generating research plan for topic: Three-Dimensional Orientation Reconstruction of Filamentary Structures in Molecular Clouds
2025-07-20 16:22:03 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.0-flash
2025-07-20 16:22:03 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.0-flash
2025-07-20 16:22:04 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 16:22:09 - agents.planner_agent - INFO - Successfully parsed AI response: 7 questions, 12 general keywords, 7 sub-question keyword sets
2025-07-20 16:22:09 - agents.planner_agent - INFO - Generated 7 sub-questions and 47 keywords
