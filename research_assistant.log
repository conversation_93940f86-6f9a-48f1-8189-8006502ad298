2025-07-20 13:40:28 - main - INFO - 初始化AI驱动的科研助理...
2025-07-20 13:40:28 - main - INFO - 执行模式: full_analysis
2025-07-20 13:40:28 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:40:28 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:28 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:28 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:40:28 - main - INFO - 执行系统健康检查...
2025-07-20 13:40:31 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:40:31 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 13:40:34 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:35 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:40:35 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:40:35 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 13:40:35 - clients.llm_client - ERROR - openai-compatible API error: 'Config' object has no attribute 'LLM_TEMPERATURE'
2025-07-20 13:40:35 - main - INFO - 健康检查完成: unhealthy
2025-07-20 13:40:35 - main - INFO - 服务状态: 4健康, 0警告, 1异常
2025-07-20 13:40:35 - main - INFO - ✅ Configuration: Configuration is valid
2025-07-20 13:40:35 - main - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 13:40:35 - main - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 13:40:35 - main - ERROR - 组件初始化失败: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 13:41:50 - main - INFO - 初始化AI驱动的科研助理...
2025-07-20 13:41:50 - main - INFO - 执行模式: full_analysis
2025-07-20 13:41:50 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:41:50 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:51 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:51 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:41:51 - main - INFO - 执行系统健康检查...
2025-07-20 13:41:52 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 13:41:52 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 13:41:55 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:56 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 13:41:56 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 13:41:56 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 13:41:56 - clients.llm_client - ERROR - openai-compatible API error: 'Config' object has no attribute 'LLM_TEMPERATURE'
2025-07-20 13:41:56 - main - INFO - 健康检查完成: unhealthy
2025-07-20 13:41:56 - main - INFO - 服务状态: 4健康, 0警告, 1异常
2025-07-20 13:41:56 - main - INFO - ✅ Configuration: Configuration is valid
2025-07-20 13:41:56 - main - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 13:41:56 - main - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 13:41:56 - main - ERROR - 组件初始化失败: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 14:19:44 - __main__ - INFO - 初始化AI驱动的科研助理...
2025-07-20 14:19:44 - __main__ - INFO - 执行模式: deep_research
2025-07-20 14:19:44 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:19:44 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:45 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:45 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:19:45 - __main__ - INFO - 执行系统健康检查...
2025-07-20 14:19:46 - clients.web_search_client - INFO - Using Tavily Search API
2025-07-20 14:19:46 - clients.web_search_client - INFO - Searching Tavily for: test
2025-07-20 14:19:48 - clients.llm_client - INFO - Using custom OpenAI-compatible endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:49 - clients.llm_client - INFO - Initialized openai-compatible client with endpoint: https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com
2025-07-20 14:19:49 - clients.llm_client - INFO - Using OpenAI-compatible API
2025-07-20 14:19:49 - clients.llm_client - INFO - Generating response with openai-compatible gemini-2.5-pro
2025-07-20 14:19:49 - clients.llm_client - INFO - Generating response with OpenAI gemini-2.5-pro
2025-07-20 14:19:52 - httpx - INFO - HTTP Request: POST https://tqipbhsswsjw.ap-northeast-1.clawcloudrun.com/chat/completions "HTTP/1.1 200 OK"
2025-07-20 14:20:00 - __main__ - INFO - 健康检查完成: unhealthy
2025-07-20 14:20:00 - __main__ - INFO - 服务状态: 4健康, 0警告, 1异常
2025-07-20 14:20:00 - __main__ - INFO - ✅ Configuration: Configuration is valid
2025-07-20 14:20:00 - __main__ - INFO - ✅ ADS API: ADS API is accessible
2025-07-20 14:20:00 - __main__ - INFO - ✅ Web Search API: Web search API is accessible
2025-07-20 14:20:00 - __main__ - ERROR - 组件初始化失败: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
2025-07-20 14:20:00 - root - ERROR - Main execution failed: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
Traceback (most recent call last):
  File "U:\Cursor\My_Deep-Research\main.py", line 551, in main
    assistant = ResearchAssistant()
                ^^^^^^^^^^^^^^^^^^^
  File "U:\Cursor\My_Deep-Research\main.py", line 45, in __init__
    self._initialize_components()
  File "U:\Cursor\My_Deep-Research\main.py", line 70, in _initialize_components
    self._test_connections()
  File "U:\Cursor\My_Deep-Research\main.py", line 89, in _test_connections
    raise APIConnectionError(
utils.exceptions.APIConnectionError: 关键服务 LLM API 不可用: LLM API error: 'Config' object has no attribute 'LLM_PROVIDER'
