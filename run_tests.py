#!/usr/bin/env python3
# run_tests.py
#
# 测试运行脚本

import sys
import subprocess
import argparse
from pathlib import Path


def run_tests(test_type="all", verbose=False, coverage=False):
    """
    运行测试
    
    Args:
        test_type: 测试类型 ("all", "unit", "integration", "models", "config", "agents")
        verbose: 是否显示详细输出
        coverage: 是否生成覆盖率报告
    """
    
    # 确保在项目根目录
    project_root = Path(__file__).parent
    
    # 构建pytest命令
    cmd = ["python", "-m", "pytest"]
    
    # 添加测试路径
    if test_type == "all":
        cmd.append("tests/")
    elif test_type == "unit":
        cmd.extend(["tests/test_models.py", "tests/test_config.py", "tests/test_agents.py"])
    elif test_type == "integration":
        cmd.append("tests/test_integration.py")
    elif test_type in ["models", "config", "agents"]:
        cmd.append(f"tests/test_{test_type}.py")
    else:
        print(f"未知的测试类型: {test_type}")
        return False
    
    # 添加选项
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # 添加覆盖率选项
    if coverage:
        cmd.extend([
            "--cov=.",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-exclude=tests/*"
        ])
    
    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",  # 简短的错误回溯
        "--strict-markers",  # 严格的标记模式
        "-x"  # 遇到第一个失败就停止
    ])
    
    print(f"运行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode == 0
    except FileNotFoundError:
        print("错误: 找不到pytest。请确保已安装pytest:")
        print("pip install pytest pytest-cov")
        return False
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return False


def check_dependencies():
    """检查测试依赖"""
    required_packages = ["pytest", "pytest-cov"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下测试依赖:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行AI科研助理测试")
    
    parser.add_argument(
        "test_type",
        nargs="?",
        default="all",
        choices=["all", "unit", "integration", "models", "config", "agents"],
        help="测试类型 (默认: all)"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="显示详细输出"
    )
    
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="检查测试依赖"
    )
    
    args = parser.parse_args()
    
    print("AI科研助理 - 测试运行器")
    print("=" * 60)
    
    # 检查依赖
    if args.check_deps or not check_dependencies():
        if args.check_deps:
            print("✅ 所有测试依赖已安装" if check_dependencies() else "❌ 缺少测试依赖")
        return
    
    # 运行测试
    success = run_tests(
        test_type=args.test_type,
        verbose=args.verbose,
        coverage=args.coverage
    )
    
    print("=" * 60)
    if success:
        print("✅ 所有测试通过!")
        if args.coverage:
            print("📊 覆盖率报告已生成到 htmlcov/ 目录")
    else:
        print("❌ 测试失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
