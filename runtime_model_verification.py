#!/usr/bin/env python3
# runtime_model_verification.py
#
# Runtime verification of actual model usage with logging

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_detailed_logging():
    """Setup detailed logging to capture all model usage"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_planner_agent_model_usage():
    """Test PlannerAgent actual model usage"""
    print("🔍 Testing PlannerAgent Model Usage")
    print("=" * 60)
    
    try:
        from agents.planner_agent import PlannerAgent
        from config import config
        
        expected_model = config.PLANNER_AGENT_MODEL
        print(f"Expected Model: {expected_model}")
        
        # Create PlannerAgent
        planner = PlannerAgent()
        actual_model = getattr(planner.llm_client, 'model', None)
        
        print(f"Actual Model: {actual_model}")
        print(f"Client Type: {type(planner.llm_client).__name__}")
        
        if actual_model == expected_model:
            print("✅ PlannerAgent is using the correct model")
            return True
        else:
            print("❌ PlannerAgent is using the wrong model")
            return False
            
    except Exception as e:
        print(f"❌ PlannerAgent test failed: {e}")
        return False

def test_synthesizer_agent_model_usage():
    """Test SynthesizerAgent actual model usage"""
    print("\n🔍 Testing SynthesizerAgent Model Usage")
    print("=" * 60)
    
    try:
        from agents.synthesizer_agent import SynthesizerAgent
        from config import config
        
        expected_model = config.SYNTHESIZER_AGENT_MODEL
        print(f"Expected Model: {expected_model}")
        
        # Create SynthesizerAgent
        synthesizer = SynthesizerAgent()
        actual_model = getattr(synthesizer.llm_client, 'model', None)
        
        print(f"Actual Model: {actual_model}")
        print(f"Client Type: {type(synthesizer.llm_client).__name__}")
        
        if actual_model == expected_model:
            print("✅ SynthesizerAgent is using the correct model")
            return True
        else:
            print("❌ SynthesizerAgent is using the wrong model")
            return False
            
    except Exception as e:
        print(f"❌ SynthesizerAgent test failed: {e}")
        return False

def test_writer_agent_model_usage():
    """Test WriterAgent actual model usage"""
    print("\n🔍 Testing WriterAgent Model Usage")
    print("=" * 60)
    
    try:
        from agents.writer_agent import WriterAgent
        from config import config
        
        expected_model = config.WRITER_AGENT_MODEL
        print(f"Expected Model: {expected_model}")
        
        # Create WriterAgent
        writer = WriterAgent()
        actual_model = getattr(writer.llm_client, 'model', None)
        
        print(f"Actual Model: {actual_model}")
        print(f"Client Type: {type(writer.llm_client).__name__}")
        
        if actual_model == expected_model:
            print("✅ WriterAgent is using the correct model")
            return True
        else:
            print("❌ WriterAgent is using the wrong model")
            return False
            
    except Exception as e:
        print(f"❌ WriterAgent test failed: {e}")
        return False

def test_model_differentiation():
    """Test that different agents use different models as configured"""
    print("\n🔍 Testing Model Differentiation Between Agents")
    print("=" * 60)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        # Create all agents
        planner = PlannerAgent()
        synthesizer = SynthesizerAgent()
        writer = WriterAgent()
        
        # Get actual models
        planner_model = getattr(planner.llm_client, 'model', None)
        synthesizer_model = getattr(synthesizer.llm_client, 'model', None)
        writer_model = getattr(writer.llm_client, 'model', None)
        
        print(f"PlannerAgent Model: {planner_model}")
        print(f"SynthesizerAgent Model: {synthesizer_model}")
        print(f"WriterAgent Model: {writer_model}")
        
        # Check differentiation
        models_are_different = (
            planner_model != synthesizer_model or
            synthesizer_model != writer_model
        )
        
        # Check against expected configuration
        expected_planner = config.PLANNER_AGENT_MODEL
        expected_synthesizer = config.SYNTHESIZER_AGENT_MODEL
        expected_writer = config.WRITER_AGENT_MODEL
        
        print(f"\nExpected Configuration:")
        print(f"PlannerAgent: {expected_planner}")
        print(f"SynthesizerAgent: {expected_synthesizer}")
        print(f"WriterAgent: {expected_writer}")
        
        all_correct = (
            planner_model == expected_planner and
            synthesizer_model == expected_synthesizer and
            writer_model == expected_writer
        )
        
        if all_correct:
            print("✅ All agents are using their correctly configured models")
            if models_are_different:
                print("✅ Model differentiation is working correctly")
            else:
                print("ℹ️  Some agents use the same model (as configured)")
            return True
        else:
            print("❌ Some agents are not using their configured models")
            return False
            
    except Exception as e:
        print(f"❌ Model differentiation test failed: {e}")
        return False

def verify_no_fallback_override():
    """Verify that fallback mechanisms are not overriding agent-specific models"""
    print("\n🔍 Verifying No Fallback Override")
    print("=" * 60)
    
    try:
        from config import config
        
        # Check if any agent model is empty (which would trigger fallback)
        agent_models = {
            "planner": config.PLANNER_AGENT_MODEL,
            "synthesizer": config.SYNTHESIZER_AGENT_MODEL,
            "writer": config.WRITER_AGENT_MODEL
        }
        
        fallback_model = config.OPENAI_COMPATIBLE_MODEL
        
        print(f"Fallback Model (OPENAI_COMPATIBLE_MODEL): {fallback_model}")
        print(f"Agent Models:")
        
        all_have_specific_models = True
        
        for agent, model in agent_models.items():
            print(f"  {agent}: {model}")
            if not model or model == fallback_model:
                print(f"    ⚠️  {agent} might be using fallback")
                all_have_specific_models = False
            else:
                print(f"    ✅ {agent} has specific model")
        
        if all_have_specific_models:
            print("✅ All agents have specific models, no fallback override")
            return True
        else:
            print("❌ Some agents might be using fallback models")
            return False
            
    except Exception as e:
        print(f"❌ Fallback override verification failed: {e}")
        return False

def main():
    """Run runtime model verification"""
    print("🚀 RUNTIME MODEL VERIFICATION")
    print("=" * 80)
    
    # Setup logging
    setup_detailed_logging()
    
    tests = [
        ("PlannerAgent Model Usage", test_planner_agent_model_usage),
        ("SynthesizerAgent Model Usage", test_synthesizer_agent_model_usage),
        ("WriterAgent Model Usage", test_writer_agent_model_usage),
        ("Model Differentiation", test_model_differentiation),
        ("No Fallback Override", verify_no_fallback_override),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 RUNTIME VERIFICATION RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 RUNTIME VERIFICATION SUCCESSFUL!")
        print("✅ All agents are using their specifically configured models at runtime")
        print("✅ Model selection logic is working correctly in practice")
        print("✅ No configuration issues found")
    else:
        print("\n❌ RUNTIME VERIFICATION FAILED!")
        print("🔧 Issues found:")
        for test_name, result in results:
            if not result:
                print(f"   - {test_name}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
