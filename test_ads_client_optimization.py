#!/usr/bin/env python3
# test_ads_client_optimization.py
#
# 测试ADS客户端针对PaperRankingAgent的优化

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_ads_client_configuration():
    """测试ADS客户端配置优化"""
    print("🧪 Testing ADS Client Configuration Optimization")
    print("=" * 70)
    
    try:
        from config import config
        
        print("📋 Testing ADS configuration for PaperRankingAgent integration:")
        
        # 检查新的配置项
        ads_configs = {
            "ADS_PAPERS_PER_SUBQUESTION": config.ADS_PAPERS_PER_SUBQUESTION,
            "ADS_TOP_CITED_COUNT": config.ADS_TOP_CITED_COUNT,
            "ADS_RECENT_PAPERS_COUNT": config.ADS_RECENT_PAPERS_COUNT,
            "ADS_SEARCH_RESULTS_PER_QUERY": config.ADS_SEARCH_RESULTS_PER_QUERY
        }
        
        for config_name, config_value in ads_configs.items():
            print(f"   ✅ {config_name}: {config_value}")
        
        # 验证配置值是否合理
        if config.ADS_PAPERS_PER_SUBQUESTION >= 100:
            print(f"   ✅ Papers per subquestion ({config.ADS_PAPERS_PER_SUBQUESTION}) provides adequate pool for PaperRankingAgent")
        else:
            print(f"   ⚠️  Papers per subquestion ({config.ADS_PAPERS_PER_SUBQUESTION}) may be too small for optimal ranking")
        
        return True
        
    except Exception as e:
        print(f"❌ ADS client configuration test failed: {e}")
        return False


def test_ads_client_methods():
    """测试ADS客户端方法优化"""
    print("\n🧪 Testing ADS Client Methods Optimization")
    print("=" * 70)
    
    try:
        from clients.ads_client import ADSClient
        
        # 创建ADS客户端（不需要真实API token进行方法测试）
        try:
            ads_client = ADSClient()
        except ValueError:
            # 如果没有API token，创建一个模拟客户端用于方法测试
            ads_client = ADSClient.__new__(ADSClient)
            ads_client.api_token = "test_token"
            ads_client.base_url = "https://api.adsabs.harvard.edu/v1"
            ads_client.logger = None
        
        print("📋 Testing optimized ADS client methods:")
        
        # 检查新方法存在
        new_methods = [
            "search_papers_for_ranking",
            "_retrieve_papers_bulk_optimized", 
            "_convert_to_paper_enhanced",
            "_basic_deduplicate_papers",
            "_normalize_title_basic"
        ]
        
        for method_name in new_methods:
            if hasattr(ads_client, method_name):
                print(f"   ✅ New method exists: {method_name}")
            else:
                print(f"   ❌ New method missing: {method_name}")
                return False
        
        # 检查向后兼容方法
        legacy_methods = [
            "search_papers",
            "_retrieve_papers_bulk",
            "_convert_to_paper",
            "_deduplicate_papers",
            "_normalize_title"
        ]
        
        for method_name in legacy_methods:
            if hasattr(ads_client, method_name):
                print(f"   ✅ Legacy method preserved: {method_name}")
            else:
                print(f"   ❌ Legacy method missing: {method_name}")
                return False
        
        # 检查弃用方法
        deprecated_methods = ["_apply_two_tier_sorting"]
        for method_name in deprecated_methods:
            if hasattr(ads_client, method_name):
                print(f"   ✅ Deprecated method preserved for compatibility: {method_name}")
            else:
                print(f"   ❌ Deprecated method missing: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ ADS client methods test failed: {e}")
        return False


def test_paper_conversion_enhancement():
    """测试论文转换增强功能"""
    print("\n🧪 Testing Paper Conversion Enhancement")
    print("=" * 70)
    
    try:
        from clients.ads_client import ADSClient
        
        # 创建模拟ADS客户端
        ads_client = ADSClient.__new__(ADSClient)
        ads_client.logger = None
        
        # 模拟ADS API返回的论文数据
        mock_paper_data = {
            'title': ['Machine Learning Applications in Astrophysics: A Comprehensive Review'],
            'author': ['Smith, John A.', 'Johnson, Mary B.', 'Brown, David C.'],
            'abstract': 'This paper reviews the current applications of machine learning in astrophysics...',
            'pubdate': '2023-06-00',
            'pub': 'Astrophysical Journal',
            'doi': ['10.1088/0004-637X/123/4/567'],
            'bibcode': '2023ApJ...123..567S',
            'citation_count': 45,
            'arxiv_class': ['astro-ph.IM'],
            'keyword': ['machine learning', 'astrophysics', 'data analysis'],
            'property': ['REFEREED']
        }
        
        print("📋 Testing enhanced paper conversion:")
        
        # 测试增强的转换方法
        paper = ads_client._convert_to_paper_enhanced(mock_paper_data)
        
        # 验证转换结果
        checks = [
            ("Title", paper.title, "Machine Learning Applications in Astrophysics: A Comprehensive Review"),
            ("Authors count", len(paper.authors), 3),
            ("Abstract exists", bool(paper.abstract), True),
            ("Publication date", paper.publication_date, "2023-06-00"),
            ("Journal", paper.journal, "Astrophysical Journal"),
            ("DOI", paper.doi, "10.1088/0004-637X/123/4/567"),
            ("Bibcode", paper.ads_bibcode, "2023ApJ...123..567S"),
            ("Citation count", paper.citation_count, 45),
            ("Source", paper.source, "ads")
        ]
        
        for check_name, actual, expected in checks:
            if actual == expected:
                print(f"   ✅ {check_name}: {actual}")
            else:
                print(f"   ❌ {check_name}: expected {expected}, got {actual}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Paper conversion enhancement test failed: {e}")
        return False


def test_deduplication_optimization():
    """测试去重优化功能"""
    print("\n🧪 Testing Deduplication Optimization")
    print("=" * 70)
    
    try:
        from clients.ads_client import ADSClient
        from models.paper import Paper
        
        # 创建模拟ADS客户端
        ads_client = ADSClient.__new__(ADSClient)
        ads_client.logger = None
        
        # 创建测试论文数据
        test_papers = [
            Paper(
                title="Machine Learning in Astrophysics",
                authors=["Smith, J."],
                abstract="Abstract 1",
                publication_date="2023-01-01",
                ads_bibcode="2023ApJ...123..001S",
                citation_count=50
            ),
            Paper(
                title="Machine Learning in Astrophysics",  # 重复标题
                authors=["Johnson, A."],
                abstract="Abstract 2", 
                publication_date="2023-02-01",
                ads_bibcode="2023MNRAS.456..002J",
                citation_count=30
            ),
            Paper(
                title="Deep Learning for Galaxy Classification",
                authors=["Brown, K."],
                abstract="Abstract 3",
                publication_date="2023-03-01",
                ads_bibcode="2023A&A...789..003B",
                citation_count=25
            ),
            Paper(
                title="The Deep Learning for Galaxy Classification",  # 相似标题
                authors=["Davis, L."],
                abstract="Abstract 4",
                publication_date="2023-04-01",
                ads_bibcode="2023ApJ...123..004D",
                citation_count=20
            )
        ]
        
        print("📋 Testing basic deduplication:")
        print(f"   Original papers: {len(test_papers)}")
        
        # 测试基础去重
        deduplicated = ads_client._basic_deduplicate_papers(test_papers)
        print(f"   After basic deduplication: {len(deduplicated)}")
        
        # 验证去重效果
        if len(deduplicated) < len(test_papers):
            print(f"   ✅ Deduplication working: removed {len(test_papers) - len(deduplicated)} duplicates")
        else:
            print(f"   ⚠️  No duplicates removed, may need adjustment")
        
        # 测试标题标准化
        test_titles = [
            "Machine Learning in Astrophysics",
            "The Machine Learning in Astrophysics",
            "A Machine Learning in Astrophysics"
        ]
        
        print(f"\n📋 Testing title normalization:")
        for title in test_titles:
            normalized = ads_client._normalize_title_basic(title)
            print(f"   '{title}' → '{normalized}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Deduplication optimization test failed: {e}")
        return False


def test_integration_with_paper_ranking_agent():
    """测试与PaperRankingAgent的集成"""
    print("\n🧪 Testing Integration with PaperRankingAgent")
    print("=" * 70)
    
    try:
        from clients.ads_client import ADSClient
        from agents.paper_ranking_agent import PaperRankingAgent
        from config import config
        
        print("📋 Testing ADS client and PaperRankingAgent integration:")
        
        # 检查配置兼容性
        ads_papers_per_subquestion = config.ADS_PAPERS_PER_SUBQUESTION
        ranking_default_top_n = config.PAPER_RANKING_DEFAULT_TOP_N
        
        print(f"   ✅ ADS retrieves: {ads_papers_per_subquestion} papers")
        print(f"   ✅ PaperRankingAgent filters to: {ranking_default_top_n} papers")
        
        if ads_papers_per_subquestion > ranking_default_top_n * 3:
            print(f"   ✅ Good ratio: ADS provides {ads_papers_per_subquestion // ranking_default_top_n}x more papers than final selection")
        else:
            print(f"   ⚠️  Ratio may be too low for optimal ranking")
        
        # 检查工作流兼容性
        workflow_steps = [
            "1. ADS Client: Bulk retrieval with search_papers_for_ranking()",
            "2. ADS Client: Basic deduplication and quality filtering",
            "3. PaperRankingAgent: AI-powered relevance assessment",
            "4. PaperRankingAgent: Multi-factor importance scoring", 
            "5. PaperRankingAgent: Composite ranking and top-N selection"
        ]
        
        print(f"\n📋 Optimized workflow:")
        for step in workflow_steps:
            print(f"   {step}")
        
        # 验证方法兼容性
        try:
            ads_client = ADSClient()
            ranking_agent = PaperRankingAgent()
            print(f"   ✅ Both ADS client and PaperRankingAgent can be instantiated")
        except ValueError as e:
            if "ADS API token" in str(e):
                print(f"   ✅ ADS client requires token (expected), PaperRankingAgent OK")
            else:
                raise e
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """运行所有ADS客户端优化测试"""
    print("🚀 ADS Client Optimization Testing")
    print("=" * 90)
    print("Testing ADS client optimizations for PaperRankingAgent integration")
    
    tests = [
        ("ADS Client Configuration", test_ads_client_configuration),
        ("ADS Client Methods", test_ads_client_methods),
        ("Paper Conversion Enhancement", test_paper_conversion_enhancement),
        ("Deduplication Optimization", test_deduplication_optimization),
        ("Integration with PaperRankingAgent", test_integration_with_paper_ranking_agent),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # 生成测试结果总结
    print("\n🎉 ADS CLIENT OPTIMIZATION TESTING RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL ADS CLIENT OPTIMIZATION TESTS PASSED!")
        print("✅ ADS client configuration is optimized for PaperRankingAgent")
        print("✅ New methods for bulk retrieval are working")
        print("✅ Enhanced paper conversion includes all required metadata")
        print("✅ Basic deduplication preserves more papers for ranking")
        print("✅ Integration with PaperRankingAgent is seamless")
        print("✅ Ready for production deployment")
    else:
        print("\n⚠️  Some ADS client optimization tests failed")
        print("🔧 Review failed components before deployment")
    
    print("\n💡 ADS Client Optimization Features:")
    print("   📚 Increased paper pool: 120 papers per subquestion")
    print("   🔄 Simplified retrieval logic: Focus on bulk collection")
    print("   📊 Enhanced metadata: Complete information for ranking")
    print("   🧹 Basic deduplication: Preserve more papers for AI ranking")
    print("   ⚡ Improved efficiency: Remove complex sorting overhead")
    print("   🔗 Seamless integration: Perfect workflow with PaperRankingAgent")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
