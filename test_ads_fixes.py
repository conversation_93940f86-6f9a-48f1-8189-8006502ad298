#!/usr/bin/env python3
# test_ads_fixes.py
#
# 简化测试验证ADS客户端修复

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_paper_conversion_fix():
    """测试论文转换修复"""
    print("🧪 Testing Paper Conversion Fix")
    print("=" * 50)
    
    try:
        from clients.ads_client import ADSClient
        
        # 创建模拟ADS客户端
        ads_client = ADSClient.__new__(ADSClient)
        ads_client.logger = None
        
        # 模拟ADS API返回的论文数据
        mock_paper_data = {
            'title': ['Machine Learning Applications in Astrophysics'],
            'author': ['<PERSON>, <PERSON>', '<PERSON>, <PERSON>'],
            'abstract': 'This paper reviews machine learning applications...',
            'pubdate': '2023-06-00',
            'pub': 'Astrophysical Journal',
            'doi': ['10.1088/0004-637X/123/4/567'],
            'bibcode': '2023ApJ...123..567S',
            'citation_count': 45
        }
        
        # 测试增强的转换方法
        paper = ads_client._convert_to_paper_enhanced(mock_paper_data)
        
        print(f"   ✅ Title: {paper.title}")
        print(f"   ✅ Authors: <AUTHORS>
        print(f"   ✅ Publication date: {paper.publication_date}")
        print(f"   ✅ Citation count: {paper.citation_count}")
        
        # 验证日期修复
        if paper.publication_date == '2023-06-00':
            print(f"   ✅ Date conversion fix working")
            return True
        else:
            print(f"   ❌ Date conversion issue: {paper.publication_date}")
            return False
        
    except Exception as e:
        print(f"❌ Paper conversion test failed: {e}")
        return False


def test_deduplication_fix():
    """测试去重修复"""
    print("\n🧪 Testing Deduplication Fix")
    print("=" * 50)
    
    try:
        from clients.ads_client import ADSClient
        from models.paper import Paper
        
        # 创建模拟ADS客户端
        ads_client = ADSClient.__new__(ADSClient)
        ads_client.logger = None  # 设置为None以测试日志修复
        
        # 创建测试论文
        test_papers = [
            Paper(
                title="Machine Learning in Astrophysics",
                authors=["Smith, J."],
                abstract="Abstract 1",
                publication_date="2023-01-01",
                ads_bibcode="2023ApJ...123..001S",
                citation_count=50
            ),
            Paper(
                title="Deep Learning for Galaxy Classification",
                authors=["Brown, K."],
                abstract="Abstract 2",
                publication_date="2023-02-01",
                ads_bibcode="2023MNRAS.456..002B",
                citation_count=25
            )
        ]
        
        # 测试基础去重
        deduplicated = ads_client._basic_deduplicate_papers(test_papers)
        
        print(f"   ✅ Original papers: {len(test_papers)}")
        print(f"   ✅ After deduplication: {len(deduplicated)}")
        print(f"   ✅ Deduplication fix working (no logger error)")
        
        return True
        
    except Exception as e:
        print(f"❌ Deduplication test failed: {e}")
        return False


def test_configuration():
    """测试配置"""
    print("\n🧪 Testing Configuration")
    print("=" * 50)
    
    try:
        from config import config
        
        print(f"   ✅ ADS_PAPERS_PER_SUBQUESTION: {config.ADS_PAPERS_PER_SUBQUESTION}")
        print(f"   ✅ ADS_SEARCH_RESULTS_PER_QUERY: {config.ADS_SEARCH_RESULTS_PER_QUERY}")
        print(f"   ✅ PAPER_RANKING_DEFAULT_TOP_N: {config.PAPER_RANKING_DEFAULT_TOP_N}")
        
        # 验证比例
        ratio = config.ADS_PAPERS_PER_SUBQUESTION / config.PAPER_RANKING_DEFAULT_TOP_N
        print(f"   ✅ ADS to Ranking ratio: {ratio:.1f}x")
        
        if ratio >= 3:
            print(f"   ✅ Good ratio for effective ranking")
            return True
        else:
            print(f"   ⚠️  Ratio may be low")
            return True  # Still pass, just a warning
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def main():
    """运行简化测试"""
    print("🚀 ADS Client Fixes Verification")
    print("=" * 60)
    
    tests = [
        ("Paper Conversion Fix", test_paper_conversion_fix),
        ("Deduplication Fix", test_deduplication_fix),
        ("Configuration", test_configuration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n🎉 FIXES VERIFICATION RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL FIXES VERIFIED!")
        print("✅ Paper conversion working correctly")
        print("✅ Deduplication logger issue fixed")
        print("✅ Configuration optimized for PaperRankingAgent")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
