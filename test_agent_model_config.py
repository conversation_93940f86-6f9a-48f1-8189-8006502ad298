#!/usr/bin/env python3
# test_agent_model_config.py
#
# 测试Agent模型配置是否正确

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_config_values():
    """测试配置值"""
    print("🧪 Testing Configuration Values")
    print("=" * 60)
    
    try:
        from config import config
        
        print("📋 Environment Configuration:")
        print(f"   OPENAI_COMPATIBLE_MODEL: {config.OPENAI_COMPATIBLE_MODEL}")
        print(f"   PLANNER_AGENT_MODEL: {config.PLANNER_AGENT_MODEL}")
        print(f"   SYNTHESIZER_AGENT_MODEL: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WRITER_AGENT_MODEL: {config.WRITER_AGENT_MODEL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_agent_configs():
    """测试Agent配置"""
    print("\n🧪 Testing Agent Configurations")
    print("=" * 60)
    
    try:
        from config import config
        
        agents = ["planner", "synthesizer", "writer"]
        
        for agent in agents:
            print(f"\n📋 {agent.title()} Agent Configuration:")
            agent_config = config.get_agent_config(agent)
            
            for key, value in agent_config.items():
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent configuration test failed: {e}")
        return False


def test_model_for_provider():
    """测试get_model_for_provider方法"""
    print("\n🧪 Testing get_model_for_provider Method")
    print("=" * 60)
    
    try:
        from config import config
        
        providers = ["openai", "openai-compatible", "anthropic", "gemini"]
        
        for provider in providers:
            model = config.get_model_for_provider(provider)
            print(f"   {provider}: {model}")
        
        return True
        
    except Exception as e:
        print(f"❌ get_model_for_provider test failed: {e}")
        return False


def test_llm_client_creation():
    """测试LLM客户端创建"""
    print("\n🧪 Testing LLM Client Creation")
    print("=" * 60)
    
    try:
        from clients.llm_client import LLMClient
        
        base_client = LLMClient()
        agents = ["planner", "synthesizer", "writer"]
        
        for agent in agents:
            print(f"\n📋 Creating client for {agent} agent:")
            agent_client = base_client.create_client_for_agent(agent)
            
            if agent_client:
                print(f"   ✅ Client created: {type(agent_client).__name__}")
                if hasattr(agent_client, 'model'):
                    print(f"   ✅ Model: {agent_client.model}")
                else:
                    print(f"   ⚠️  Model attribute not found")
            else:
                print(f"   ❌ Failed to create client")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM client creation test failed: {e}")
        return False


def test_synthesizer_agent_model():
    """专门测试SynthesizerAgent的模型配置"""
    print("\n🧪 Testing SynthesizerAgent Model Configuration")
    print("=" * 60)
    
    try:
        from agents.synthesizer_agent import SynthesizerAgent
        from config import config
        
        print("📋 Expected Configuration:")
        synthesizer_config = config.get_agent_config("synthesizer")
        print(f"   Provider: {synthesizer_config.get('provider')}")
        print(f"   Model: {synthesizer_config.get('model')}")
        
        print("\n📋 Creating SynthesizerAgent:")
        synthesizer = SynthesizerAgent()
        
        if hasattr(synthesizer, 'llm_client') and synthesizer.llm_client:
            print(f"   ✅ LLM Client: {type(synthesizer.llm_client).__name__}")
            if hasattr(synthesizer.llm_client, 'model'):
                print(f"   ✅ Actual Model: {synthesizer.llm_client.model}")
                
                # 检查是否使用了正确的模型
                expected_model = synthesizer_config.get('model')
                actual_model = synthesizer.llm_client.model
                
                if expected_model and actual_model == expected_model:
                    print(f"   ✅ Model configuration CORRECT: {actual_model}")
                    return True
                else:
                    print(f"   ❌ Model configuration INCORRECT:")
                    print(f"       Expected: {expected_model}")
                    print(f"       Actual: {actual_model}")
                    return False
            else:
                print(f"   ❌ Model attribute not found")
                return False
        else:
            print(f"   ❌ LLM client not found")
            return False
        
    except Exception as e:
        print(f"❌ SynthesizerAgent model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_all_agents_models():
    """测试所有Agent的模型配置"""
    print("\n🧪 Testing All Agents Model Configuration")
    print("=" * 60)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        agents_classes = {
            "planner": PlannerAgent,
            "synthesizer": SynthesizerAgent,
            "writer": WriterAgent
        }
        
        all_correct = True
        
        for agent_name, agent_class in agents_classes.items():
            print(f"\n📋 Testing {agent_name.title()}Agent:")
            
            # 获取期望配置
            expected_config = config.get_agent_config(agent_name)
            expected_model = expected_config.get('model')
            
            # 创建Agent实例
            agent = agent_class()
            
            if hasattr(agent, 'llm_client') and agent.llm_client:
                actual_model = getattr(agent.llm_client, 'model', None)
                
                print(f"   Expected Model: {expected_model}")
                print(f"   Actual Model: {actual_model}")
                
                if expected_model and actual_model == expected_model:
                    print(f"   ✅ Model configuration CORRECT")
                else:
                    print(f"   ❌ Model configuration INCORRECT")
                    all_correct = False
            else:
                print(f"   ❌ LLM client not found")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ All agents model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🚀 Agent Model Configuration Testing")
    print("=" * 80)
    
    tests = [
        ("Configuration Values", test_config_values),
        ("Agent Configurations", test_agent_configs),
        ("Model for Provider", test_model_for_provider),
        ("LLM Client Creation", test_llm_client_creation),
        ("SynthesizerAgent Model", test_synthesizer_agent_model),
        ("All Agents Models", test_all_agents_models),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Agent model configurations are correct.")
    else:
        print("\n❌ Some tests failed. Agent model configurations need fixing.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
