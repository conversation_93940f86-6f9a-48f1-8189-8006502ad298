#!/usr/bin/env python3
# test_agent_optimization.py
#
# 测试Agent优化和配置

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_agent_configurations():
    """测试Agent配置"""
    print("🧪 Testing Agent Configurations")
    print("=" * 60)
    
    try:
        from config import config
        
        agents = ["planner", "synthesizer", "writer"]
        
        for agent in agents:
            print(f"\n📋 {agent.title()} Agent Configuration:")
            agent_config = config.get_agent_config(agent)
            
            for key, value in agent_config.items():
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent configuration test failed: {e}")
        return False


def test_llm_client_agent_creation():
    """测试LLM客户端为Agent创建专用客户端"""
    print("\n🧪 Testing LLM Client Agent Creation")
    print("=" * 60)
    
    try:
        from clients.llm_client import LLMClient
        
        base_client = LLMClient()
        agents = ["planner", "synthesizer", "writer"]
        
        for agent in agents:
            print(f"\n📋 Creating client for {agent} agent:")
            agent_client = base_client.create_client_for_agent(agent)
            
            if agent_client:
                print(f"   ✅ Client created: {type(agent_client).__name__}")
            else:
                print(f"   ❌ Failed to create client")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM client agent creation test failed: {e}")
        return False


def test_planner_agent_structure():
    """测试PlannerAgent返回的数据结构"""
    print("\n🧪 Testing PlannerAgent Data Structure")
    print("=" * 60)
    
    try:
        from agents.planner_agent import PlannerAgent
        
        planner = PlannerAgent()
        
        # 测试数据结构（不实际调用AI）
        print("📋 Testing PlannerAgent initialization:")
        print(f"   ✅ PlannerAgent created: {type(planner).__name__}")
        print(f"   ✅ LLM Client: {type(planner.llm_client).__name__}")
        
        # 检查方法是否存在
        methods_to_check = [
            'generate_research_plan',
            'generate_research_plan_with_confirmation',
            '_create_comprehensive_prompt',
            '_parse_ai_response'
        ]
        
        for method in methods_to_check:
            if hasattr(planner, method):
                print(f"   ✅ Method exists: {method}")
            else:
                print(f"   ❌ Method missing: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ PlannerAgent structure test failed: {e}")
        return False


def test_research_query_structure():
    """测试ResearchQuery数据结构"""
    print("\n🧪 Testing ResearchQuery Data Structure")
    print("=" * 60)
    
    try:
        from models import ResearchQuery
        
        # 创建测试查询
        query = ResearchQuery(main_topic="Test Topic")
        
        print("📋 Testing ResearchQuery structure:")
        print(f"   ✅ Main topic: {query.main_topic}")
        
        # 测试添加子问题
        test_questions = [
            "What are the main aspects?",
            "What are the challenges?",
            "What are the future directions?"
        ]
        
        query.sub_questions = test_questions
        print(f"   ✅ Sub-questions: {len(query.sub_questions)}")
        
        # 测试添加关键词
        test_keywords = ["keyword1", "keyword2", "keyword3", "keyword4"]
        for keyword in test_keywords:
            query.add_keyword(keyword)
        print(f"   ✅ Keywords: {len(query.keywords)}")
        
        # 测试子问题关键词映射
        query.sub_question_keywords = {
            "sub_question_1": ["keyword1", "keyword2"],
            "sub_question_2": ["keyword3", "keyword4"],
            "sub_question_3": ["keyword1", "keyword3"]
        }
        print(f"   ✅ Sub-question keywords mapping: {len(query.sub_question_keywords)}")
        
        # 测试更新方法
        if hasattr(query, '_update_sub_question_keywords'):
            query._update_sub_question_keywords()
            print(f"   ✅ Update method works")
        else:
            print(f"   ❌ Update method missing")
        
        return True
        
    except Exception as e:
        print(f"❌ ResearchQuery structure test failed: {e}")
        return False


def test_gui_dialog_structure():
    """测试GUI对话框结构"""
    print("\n🧪 Testing GUI Dialog Structure")
    print("=" * 60)
    
    try:
        from gui.planner_review_dialog import PlannerReviewDialog
        
        # 测试数据
        topic = "Test Research Topic"
        sub_questions = [
            "What are the current applications?",
            "What are the main challenges?",
            "What are the future prospects?"
        ]
        keywords = ["keyword1", "keyword2", "keyword3", "keyword4"]
        
        print("📋 Testing PlannerReviewDialog structure:")
        
        # 创建对话框实例（不显示）
        dialog = PlannerReviewDialog(topic, sub_questions, keywords, 100, "15-20 minutes")
        
        print(f"   ✅ Dialog created with topic: {dialog.topic}")
        print(f"   ✅ Sub-questions: {len(dialog.sub_questions)}")
        print(f"   ✅ Keywords: {len(dialog.keywords)}")
        print(f"   ✅ Estimated papers: {dialog.estimated_papers}")
        print(f"   ✅ Estimated time: {dialog.estimated_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI dialog structure test failed: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 Agent Optimization Testing")
    print("=" * 80)
    
    tests = [
        ("Agent Configurations", test_agent_configurations),
        ("LLM Client Agent Creation", test_llm_client_agent_creation),
        ("PlannerAgent Structure", test_planner_agent_structure),
        ("ResearchQuery Structure", test_research_query_structure),
        ("GUI Dialog Structure", test_gui_dialog_structure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Agent system is working correctly.")
    elif passed >= total * 0.8:
        print("\n✅ Most tests passed. Minor optimizations may be needed.")
    else:
        print("\n❌ Multiple tests failed. Significant optimizations needed.")
    
    return passed >= total * 0.8


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
