#!/usr/bin/env python3
# test_all_agents_model_config.py
#
# 全面测试所有Agent的模型配置

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_current_env_config():
    """测试当前环境配置"""
    print("🧪 Testing Current Environment Configuration")
    print("=" * 70)
    
    try:
        from config import config
        
        print("📋 Current Environment Variables:")
        print(f"   OPENAI_COMPATIBLE_MODEL: {config.OPENAI_COMPATIBLE_MODEL}")
        print(f"   PLANNER_AGENT_MODEL: {config.PLANNER_AGENT_MODEL}")
        print(f"   SYNTHESIZER_AGENT_MODEL: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WRITER_AGENT_MODEL: {config.WRITER_AGENT_MODEL}")
        
        print("\n📋 Expected Agent Models:")
        print(f"   PlannerAgent should use: {config.PLANNER_AGENT_MODEL}")
        print(f"   SynthesizerAgent should use: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WriterAgent should use: {config.WRITER_AGENT_MODEL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment config test failed: {e}")
        return False


def test_agent_config_method():
    """测试get_agent_config方法"""
    print("\n🧪 Testing get_agent_config Method")
    print("=" * 70)
    
    try:
        from config import config
        
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 {agent.title()} Agent Config:")
            agent_config = config.get_agent_config(agent)
            
            expected_model = getattr(config, f"{agent.upper()}_AGENT_MODEL")
            actual_model = agent_config.get("model")
            
            print(f"   Provider: {agent_config.get('provider')}")
            print(f"   Expected Model: {expected_model}")
            print(f"   Actual Model: {actual_model}")
            print(f"   Temperature: {agent_config.get('temperature')}")
            print(f"   Max Tokens: {agent_config.get('max_tokens')}")
            
            if actual_model == expected_model:
                print(f"   ✅ Model config CORRECT")
            else:
                print(f"   ❌ Model config INCORRECT")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Agent config method test failed: {e}")
        return False


def test_llm_client_for_agents():
    """测试为每个Agent创建的LLM客户端"""
    print("\n🧪 Testing LLM Client Creation for All Agents")
    print("=" * 70)
    
    try:
        from clients.llm_client import LLMClient
        from config import config
        
        base_client = LLMClient()
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 Creating LLM Client for {agent.title()}Agent:")
            
            # 获取期望的模型
            expected_model = getattr(config, f"{agent.upper()}_AGENT_MODEL")
            
            # 创建客户端
            agent_client = base_client.create_client_for_agent(agent)
            
            if agent_client:
                actual_model = getattr(agent_client, 'model', None)
                
                print(f"   Client Type: {type(agent_client).__name__}")
                print(f"   Expected Model: {expected_model}")
                print(f"   Actual Model: {actual_model}")
                
                if actual_model == expected_model:
                    print(f"   ✅ LLM Client model CORRECT")
                else:
                    print(f"   ❌ LLM Client model INCORRECT")
                    all_correct = False
            else:
                print(f"   ❌ Failed to create LLM client")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ LLM client test failed: {e}")
        return False


def test_actual_agent_instances():
    """测试实际的Agent实例"""
    print("\n🧪 Testing Actual Agent Instances")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        agents_info = [
            ("planner", PlannerAgent, config.PLANNER_AGENT_MODEL),
            ("synthesizer", SynthesizerAgent, config.SYNTHESIZER_AGENT_MODEL),
            ("writer", WriterAgent, config.WRITER_AGENT_MODEL)
        ]
        
        all_correct = True
        
        for agent_name, agent_class, expected_model in agents_info:
            print(f"\n📋 Testing {agent_name.title()}Agent Instance:")
            
            # 创建Agent实例
            agent = agent_class()
            
            if hasattr(agent, 'llm_client') and agent.llm_client:
                actual_model = getattr(agent.llm_client, 'model', None)
                client_type = type(agent.llm_client).__name__
                
                print(f"   Agent Class: {agent_class.__name__}")
                print(f"   LLM Client: {client_type}")
                print(f"   Expected Model: {expected_model}")
                print(f"   Actual Model: {actual_model}")
                
                if actual_model == expected_model:
                    print(f"   ✅ Agent instance model CORRECT")
                else:
                    print(f"   ❌ Agent instance model INCORRECT")
                    all_correct = False
            else:
                print(f"   ❌ Agent has no LLM client")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Agent instance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_provider_mapping():
    """测试模型提供商映射"""
    print("\n🧪 Testing Model Provider Mapping")
    print("=" * 70)
    
    try:
        from config import config
        
        providers = ["openai", "openai-compatible", "anthropic", "gemini"]
        
        print("📋 Provider to Model Mapping:")
        for provider in providers:
            model = config.get_model_for_provider(provider)
            print(f"   {provider}: {model}")
        
        # 特别检查openai-compatible
        openai_compatible_model = config.get_model_for_provider("openai-compatible")
        expected_model = config.OPENAI_COMPATIBLE_MODEL
        
        print(f"\n📋 OpenAI-Compatible Provider Check:")
        print(f"   Expected: {expected_model}")
        print(f"   Actual: {openai_compatible_model}")
        
        if openai_compatible_model == expected_model:
            print(f"   ✅ OpenAI-Compatible mapping CORRECT")
            return True
        else:
            print(f"   ❌ OpenAI-Compatible mapping INCORRECT")
            return False
        
    except Exception as e:
        print(f"❌ Model provider mapping test failed: {e}")
        return False


def test_config_fallback_logic():
    """测试配置回退逻辑"""
    print("\n🧪 Testing Configuration Fallback Logic")
    print("=" * 70)
    
    try:
        from config import config
        
        # 测试当agent模型为空时的回退逻辑
        print("📋 Testing fallback when agent model is empty:")
        
        # 模拟空模型配置
        test_config = {
            "provider": "openai-compatible",
            "model": "",  # 空模型
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        # 应用回退逻辑
        if not test_config.get("model"):
            provider = test_config.get("provider", config.DEFAULT_LLM_PROVIDER)
            if provider == "openai-compatible":
                test_config["model"] = config.OPENAI_COMPATIBLE_MODEL
        
        print(f"   Original model: '' (empty)")
        print(f"   Provider: {test_config['provider']}")
        print(f"   Fallback model: {test_config['model']}")
        print(f"   Expected fallback: {config.OPENAI_COMPATIBLE_MODEL}")
        
        if test_config["model"] == config.OPENAI_COMPATIBLE_MODEL:
            print(f"   ✅ Fallback logic CORRECT")
            return True
        else:
            print(f"   ❌ Fallback logic INCORRECT")
            return False
        
    except Exception as e:
        print(f"❌ Config fallback test failed: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 Comprehensive Agent Model Configuration Testing")
    print("=" * 90)
    
    tests = [
        ("Current Environment Configuration", test_current_env_config),
        ("Agent Config Method", test_agent_config_method),
        ("LLM Client Creation", test_llm_client_for_agents),
        ("Actual Agent Instances", test_actual_agent_instances),
        ("Model Provider Mapping", test_model_provider_mapping),
        ("Configuration Fallback Logic", test_config_fallback_logic),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 90)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! All Agent model configurations are correct.")
    else:
        print("\n❌ Some tests failed. Agent model configurations need attention.")
        print("\n🔧 Issues to check:")
        for test_name, result in results:
            if not result:
                print(f"   - {test_name}")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
