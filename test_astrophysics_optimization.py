#!/usr/bin/env python3
# test_astrophysics_optimization.py
#
# 测试天体物理学专用优化

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_astrophysics_config_integration():
    """测试天体物理学专用配置集成"""
    print("🧪 Testing Astrophysics Configuration Integration")
    print("=" * 70)
    
    try:
        from config import config
        
        print("📋 Testing PaperRankingAgent astrophysics configuration:")
        
        # 检查新的配置项
        astrophysics_configs = {
            "relevance_weight": config.PAPER_RANKING_RELEVANCE_WEIGHT,
            "importance_weight": config.PAPER_RANKING_IMPORTANCE_WEIGHT,
            "default_top_n": config.PAPER_RANKING_DEFAULT_TOP_N,
            "citation_threshold_high": config.PAPER_RANKING_CITATION_THRESHOLD_HIGH,
            "citation_threshold_medium": config.PAPER_RANKING_CITATION_THRESHOLD_MEDIUM,
            "recency_boost_years": config.PAPER_RANKING_RECENCY_BOOST_YEARS,
            "astrophysics_journal_boost": config.PAPER_RANKING_ASTROPHYSICS_JOURNAL_BOOST
        }
        
        for config_name, config_value in astrophysics_configs.items():
            print(f"   ✅ {config_name}: {config_value}")
        
        # 检查get_agent_config方法
        agent_config = config.get_agent_config("paper_ranking")
        
        expected_keys = [
            "provider", "model", "temperature", "max_tokens",
            "relevance_weight", "importance_weight", "default_top_n",
            "citation_threshold_high", "citation_threshold_medium",
            "recency_boost_years", "astrophysics_journal_boost"
        ]
        
        missing_keys = []
        for key in expected_keys:
            if key in agent_config:
                print(f"   ✅ Agent config '{key}': {agent_config[key]}")
            else:
                missing_keys.append(key)
                print(f"   ❌ Missing agent config key: {key}")
        
        return len(missing_keys) == 0
        
    except Exception as e:
        print(f"❌ Astrophysics configuration integration test failed: {e}")
        return False


def test_paper_ranking_agent_astrophysics_optimization():
    """测试PaperRankingAgent天体物理学优化"""
    print("\n🧪 Testing PaperRankingAgent Astrophysics Optimization")
    print("=" * 70)
    
    try:
        from agents.paper_ranking_agent import PaperRankingAgent
        from models.paper import Paper
        
        # 创建天体物理学专用的PaperRankingAgent
        ranking_agent = PaperRankingAgent()
        
        print("📋 Testing astrophysics-specific configuration:")
        print(f"   ✅ Relevance weight: {ranking_agent.relevance_weight}")
        print(f"   ✅ Importance weight: {ranking_agent.importance_weight}")
        print(f"   ✅ Default top N: {ranking_agent.default_top_n}")
        print(f"   ✅ Citation threshold high: {ranking_agent.citation_threshold_high}")
        print(f"   ✅ Citation threshold medium: {ranking_agent.citation_threshold_medium}")
        print(f"   ✅ Recency boost years: {ranking_agent.recency_boost_years}")
        print(f"   ✅ Astrophysics journal boost: {ranking_agent.astrophysics_journal_boost}")
        
        # 测试天体物理学期刊评分
        print(f"\n📋 Testing astrophysics journal scoring:")
        astrophysics_journals = [
            ("Astrophysical Journal", "Top astrophysics journal"),
            ("Monthly Notices of the Royal Astronomical Society", "Top astrophysics journal"),
            ("Astronomy and Astrophysics", "Top astrophysics journal"),
            ("Nature Astronomy", "Top astrophysics journal"),
            ("Astrophysical Journal Supplement", "High impact astrophysics"),
            ("Journal of Cosmology and Astroparticle Physics", "High impact astrophysics"),
            ("Publications of the Astronomical Society of the Pacific", "Specialized astrophysics"),
            ("Solar Physics", "Specialized astrophysics"),
            ("Nature", "General high impact"),
            ("Unknown Journal", "Unknown journal")
        ]
        
        for journal, description in astrophysics_journals:
            score = ranking_agent._calculate_journal_score(journal)
            print(f"   {description}: {journal} → {score:.2f}")
        
        # 测试天体物理学优化的引用数评分
        print(f"\n📋 Testing astrophysics-optimized citation scoring:")
        citation_tests = [
            (0, "No citations"),
            (10, "Low citations"),
            (25, "Medium citations (above threshold)"),
            (75, "High citations"),
            (150, "Very high citations (above high threshold)"),
            (600, "Extremely high citations")
        ]
        
        for citation_count, description in citation_tests:
            score = ranking_agent._calculate_citation_score(citation_count)
            print(f"   {description} ({citation_count}): {score:.2f}")
        
        # 测试天体物理学优化的时效性评分
        print(f"\n📋 Testing astrophysics-optimized recency scoring:")
        recency_tests = [
            ("2024-01-01", "Very recent"),
            ("2022-06-15", "Within recency boost period"),
            ("2020-03-10", "Recent"),
            ("2017-08-20", "Moderately old"),
            ("2010-12-05", "Old but still valuable for astrophysics"),
            ("2005-05-15", "Older")
        ]
        
        current_year = 2024
        for date, description in recency_tests:
            score = ranking_agent._calculate_recency_score(date, current_year)
            print(f"   {description} ({date}): {score:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ PaperRankingAgent astrophysics optimization test failed: {e}")
        return False


def test_astrophysics_prompts_integration():
    """测试天体物理学专用prompt集成"""
    print("\n🧪 Testing Astrophysics Prompts Integration")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.paper_ranking_agent import PaperRankingAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        
        print("📋 Testing astrophysics-specific prompt methods:")
        
        # 测试PlannerAgent
        planner = PlannerAgent()
        planner_prompt = planner._create_comprehensive_prompt("Dark matter detection methods")
        if "astrophysics" in planner_prompt.lower() and "observational" in planner_prompt.lower():
            print("   ✅ PlannerAgent: Astrophysics-specific prompt detected")
        else:
            print("   ❌ PlannerAgent: Astrophysics-specific prompt not found")
            return False
        
        # 测试PaperRankingAgent
        ranking_agent = PaperRankingAgent()
        test_papers = [{"index": 0, "title": "Dark Matter Detection", "year": "2023", "citations": 50}]
        ranking_prompt = ranking_agent._create_astrophysics_relevance_prompt("What are dark matter detection methods?", test_papers)
        if "astrophysicist" in ranking_prompt.lower() and "astronomical" in ranking_prompt.lower():
            print("   ✅ PaperRankingAgent: Astrophysics-specific prompt detected")
        else:
            print("   ❌ PaperRankingAgent: Astrophysics-specific prompt not found")
            return False
        
        # 测试SynthesizerAgent
        synthesizer = SynthesizerAgent()
        if hasattr(synthesizer, '_create_astrophysics_web_synthesis_prompt'):
            synthesis_prompt = synthesizer._create_astrophysics_web_synthesis_prompt("Dark matter", "Test content")
            if "astrophysicist" in synthesis_prompt.lower() and "observational" in synthesis_prompt.lower():
                print("   ✅ SynthesizerAgent: Astrophysics-specific prompt detected")
            else:
                print("   ❌ SynthesizerAgent: Astrophysics-specific prompt not comprehensive")
                return False
        else:
            print("   ❌ SynthesizerAgent: Astrophysics-specific prompt method not found")
            return False
        
        # 测试WriterAgent
        writer = WriterAgent()
        if hasattr(writer, '_create_astrophysics_research_report_prompt'):
            writer_prompt = writer._create_astrophysics_research_report_prompt("Dark matter", "web", "papers", "timeline")
            if "astrophysicist" in writer_prompt.lower() and "observational" in writer_prompt.lower():
                print("   ✅ WriterAgent: Astrophysics-specific prompt detected")
            else:
                print("   ❌ WriterAgent: Astrophysics-specific prompt not comprehensive")
                return False
        else:
            print("   ❌ WriterAgent: Astrophysics-specific prompt method not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Astrophysics prompts integration test failed: {e}")
        return False


def test_astrophysics_workflow_compatibility():
    """测试天体物理学优化的工作流兼容性"""
    print("\n🧪 Testing Astrophysics Workflow Compatibility")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.paper_ranking_agent import PaperRankingAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        
        print("📋 Testing all agents initialization with astrophysics optimization:")
        
        # 创建所有agents
        planner = PlannerAgent()
        ranking = PaperRankingAgent()
        synthesizer = SynthesizerAgent()
        writer = WriterAgent()
        
        print(f"   ✅ PlannerAgent: {type(planner).__name__}")
        print(f"   ✅ PaperRankingAgent: {type(ranking).__name__}")
        print(f"   ✅ SynthesizerAgent: {type(synthesizer).__name__}")
        print(f"   ✅ WriterAgent: {type(writer).__name__}")
        
        # 检查模型配置
        models = {
            "Planner": getattr(planner.llm_client, 'model', None),
            "Ranking": getattr(ranking.llm_client, 'model', None),
            "Synthesizer": getattr(synthesizer.llm_client, 'model', None),
            "Writer": getattr(writer.llm_client, 'model', None)
        }
        
        print(f"\n📋 Astrophysics-optimized agent model configuration:")
        for agent_name, model in models.items():
            print(f"   ✅ {agent_name}: {model}")
        
        # 验证天体物理学优化的工作流顺序
        workflow_order = [
            "1. PlannerAgent: Generate astrophysics research plan",
            "2. ADS Search: Retrieve astrophysics papers",
            "3. PaperRankingAgent: Rank with astrophysics-specific criteria",
            "4. SynthesizerAgent: Analyze with astrophysical context",
            "5. WriterAgent: Generate astrophysics research report"
        ]
        
        print(f"\n📋 Astrophysics-optimized workflow:")
        for step in workflow_order:
            print(f"   {step}")
        
        return True
        
    except Exception as e:
        print(f"❌ Astrophysics workflow compatibility test failed: {e}")
        return False


def main():
    """运行所有天体物理学优化测试"""
    print("🚀 Astrophysics Specialization Testing")
    print("=" * 90)
    print("Testing astrophysics-specific optimizations for all 4 agents")
    
    tests = [
        ("Astrophysics Configuration Integration", test_astrophysics_config_integration),
        ("PaperRankingAgent Astrophysics Optimization", test_paper_ranking_agent_astrophysics_optimization),
        ("Astrophysics Prompts Integration", test_astrophysics_prompts_integration),
        ("Astrophysics Workflow Compatibility", test_astrophysics_workflow_compatibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # 生成测试结果总结
    print("\n🎉 ASTROPHYSICS OPTIMIZATION TESTING RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL ASTROPHYSICS OPTIMIZATION TESTS PASSED!")
        print("✅ Astrophysics-specific configuration is working")
        print("✅ PaperRankingAgent astrophysics optimization is functional")
        print("✅ All agents have astrophysics-specific prompts")
        print("✅ Workflow compatibility is maintained")
        print("✅ Ready for astrophysics research deployment")
    else:
        print("\n⚠️  Some astrophysics optimization tests failed")
        print("🔧 Review failed components before deployment")
    
    print("\n💡 Astrophysics Specialization Features:")
    print("   🔭 Observational techniques and instruments focus")
    print("   🧮 Theoretical models and physical mechanisms")
    print("   💻 Computational astrophysics and simulations")
    print("   🌌 Multi-wavelength and multi-messenger astronomy")
    print("   📊 Astrophysics journal impact factor optimization")
    print("   🎯 ADS database search optimization")
    print("   📈 Citation patterns specific to astrophysics")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
