#!/usr/bin/env python3
# test_citation_functionality.py
#
# 测试AI研究助理系统的引用功能

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from models.paper import Paper, PaperAnalysis
from utils.citation_manager import CitationManager, CitationFormatter
from agents.writer_agent import WriterAgent
from config import config


def create_sample_papers():
    """创建示例论文数据"""
    papers = []
    
    # 论文1
    paper1 = Paper(
        title="Deep Learning Applications in Astrophysical Data Analysis",
        authors=["<PERSON>, <PERSON>", "<PERSON>, <PERSON>", "<PERSON>, <PERSON>"],
        abstract="This paper presents novel applications of deep learning techniques to large-scale astrophysical datasets, demonstrating significant improvements in classification accuracy.",
        publication_date=datetime(2023, 6, 15),
        journal="Astrophysical Journal",
        volume="915",
        issue="2",
        pages="123-145",
        doi="10.3847/1538-4357/ac1234",
        citation_count=45,
        source="ads"
    )
    
    paper1.analysis = PaperAnalysis(
        short_summary="Introduces deep learning methods for astrophysical data classification with 95% accuracy improvement.",
        relevance_to_topic="Highly relevant - directly addresses deep learning in astrophysics",
        research_subject="Large-scale astronomical survey data",
        methodology="Convolutional neural networks and transfer learning",
        data_used="Sloan Digital Sky Survey (SDSS) and Gaia data",
        key_findings_or_results=[
            "95% accuracy in galaxy classification",
            "50% reduction in processing time",
            "Novel feature extraction techniques"
        ]
    )
    papers.append(paper1)
    
    # 论文2
    paper2 = Paper(
        title="Neural Networks for Exoplanet Detection in Transit Photometry",
        authors=["Wilson, Sarah E.", "Davis, Michael R."],
        abstract="We develop a neural network approach for automated exoplanet detection in Kepler mission data, achieving state-of-the-art performance.",
        publication_date=datetime(2023, 8, 22),
        journal="Nature Astronomy",
        volume="7",
        pages="456-467",
        doi="10.1038/s41550-023-01234-5",
        citation_count=32,
        source="ads"
    )
    
    paper2.analysis = PaperAnalysis(
        short_summary="Develops automated exoplanet detection using neural networks with 98% accuracy.",
        relevance_to_topic="Highly relevant - demonstrates deep learning applications in planetary science",
        research_subject="Kepler mission transit photometry data",
        methodology="Recurrent neural networks and attention mechanisms",
        data_used="Kepler Space Telescope light curves",
        key_findings_or_results=[
            "98% detection accuracy",
            "Reduced false positive rate by 60%",
            "Discovered 12 new exoplanet candidates"
        ]
    )
    papers.append(paper2)
    
    # 论文3
    paper3 = Paper(
        title="Machine Learning Approaches to Gravitational Wave Detection",
        authors=["Anderson, Robert K.", "Thompson, Lisa M.", "Garcia, Carlos J.", "Lee, Jennifer Y."],
        abstract="This study explores machine learning techniques for improving gravitational wave detection sensitivity in LIGO data.",
        publication_date=datetime(2023, 4, 10),
        journal="Physical Review D",
        volume="107",
        issue="8",
        pages="084032",
        doi="10.1103/PhysRevD.107.084032",
        citation_count=28,
        source="ads"
    )
    
    paper3.analysis = PaperAnalysis(
        short_summary="Applies machine learning to enhance gravitational wave detection in LIGO data.",
        relevance_to_topic="Relevant - shows ML applications in gravitational wave astronomy",
        research_subject="LIGO gravitational wave detector data",
        methodology="Deep neural networks and signal processing",
        data_used="LIGO Hanford and Livingston detector data",
        key_findings_or_results=[
            "30% improvement in detection sensitivity",
            "Reduced computational requirements",
            "Better noise characterization"
        ]
    )
    papers.append(paper3)
    
    return papers


def test_citation_formatter():
    """测试引用格式化器"""
    print("🧪 测试引用格式化器")
    print("=" * 50)
    
    papers = create_sample_papers()
    
    for style in ["APA", "IEEE", "Nature"]:
        print(f"\n📋 {style} 格式测试:")
        print("-" * 30)
        
        formatter_method = getattr(CitationFormatter, f"format_{style.lower()}")
        
        for i, paper in enumerate(papers[:2], 1):  # 只测试前两篇
            citation = formatter_method(paper)
            print(f"\n论文 {i}:")
            print(f"内联引用: {citation['inline']}")
            print(f"完整引用: {citation['full']}")


def test_citation_manager():
    """测试引用管理器"""
    print("\n🔧 测试引用管理器")
    print("=" * 50)
    
    papers = create_sample_papers()
    
    for style in ["APA", "IEEE", "Nature"]:
        print(f"\n📚 {style} 引用管理测试:")
        print("-" * 30)
        
        manager = CitationManager(style)
        
        # 添加引用
        citations = []
        for paper in papers:
            citation = manager.add_citation(paper)
            citations.append(citation)
            print(f"添加引用: {paper.title[:50]}...")
            print(f"内联引用: {citation}")
        
        # 生成参考文献列表
        print(f"\n📖 参考文献列表:")
        bibliography = manager.generate_bibliography()
        print(bibliography)
        
        # 统计信息
        stats = manager.get_citation_statistics()
        print(f"\n📊 统计信息:")
        print(f"总引用数: {stats['total_citations']}")
        print(f"引用格式: {stats['citation_style']}")
        print(f"引用年份: {stats['citation_years']}")


def test_paper_citation_methods():
    """测试Paper模型的引用方法"""
    print("\n📄 测试Paper模型引用方法")
    print("=" * 50)
    
    papers = create_sample_papers()
    paper = papers[0]
    
    print(f"论文: {paper.title}")
    print(f"简短引用: {paper.get_short_citation()}")
    print(f"引用键: {paper.generate_citation_key()}")
    
    print(f"\n各格式完整引用:")
    print(f"APA: {paper.get_apa_citation()}")
    print(f"IEEE: {paper.get_ieee_citation()}")
    print(f"Nature: {paper.get_nature_citation()}")
    
    print(f"\nBibTeX条目:")
    print(paper.get_bibtex_entry())


def test_writer_agent_citations():
    """测试WriterAgent的引用功能"""
    print("\n✍️ 测试WriterAgent引用功能")
    print("=" * 50)
    
    # 临时启用引用功能
    original_enable = config.ENABLE_CITATIONS
    config.ENABLE_CITATIONS = True
    
    try:
        papers = create_sample_papers()
        writer = WriterAgent()
        
        print("📝 生成带引用的研究报告...")
        
        # 测试引用处理方法
        structured_analyses = writer._format_paper_analyses_with_citations(papers)
        print(f"\n📊 结构化分析预览 (前500字符):")
        print(structured_analyses[:500] + "...")
        
        # 测试引用感知提示词
        prompt = writer._create_citation_aware_prompt(
            "Deep Learning in Astrophysics",
            structured_analyses[:1000],  # 截断以节省空间
            "Web summaries here...",
            "Timeline here..."
        )
        print(f"\n🎯 引用感知提示词预览 (前300字符):")
        print(prompt[:300] + "...")
        
        # 测试备用报告生成
        fallback_report = writer._create_fallback_report_with_citations(
            "Deep Learning in Astrophysics",
            papers,
            "Sample web summaries",
            "Sample timeline"
        )
        print(f"\n📋 备用报告预览:")
        print(fallback_report[:800] + "...")
        
        print(f"\n✅ WriterAgent引用功能测试完成")
        
    finally:
        # 恢复原始配置
        config.ENABLE_CITATIONS = original_enable


def test_configuration():
    """测试引用配置"""
    print("\n⚙️ 测试引用配置")
    print("=" * 50)
    
    print(f"引用样式: {config.CITATION_STYLE}")
    print(f"内联引用格式: {config.INLINE_CITATION_FORMAT}")
    print(f"参考文献排序: {config.BIBLIOGRAPHY_SORT}")
    print(f"内联最大作者数: {config.MAX_AUTHORS_INLINE}")
    print(f"参考文献最大作者数: {config.MAX_AUTHORS_BIBLIOGRAPHY}")
    print(f"启用引用功能: {config.ENABLE_CITATIONS}")


def main():
    """运行所有测试"""
    print("🚀 AI研究助理系统引用功能测试")
    print("=" * 80)
    
    try:
        test_configuration()
        test_citation_formatter()
        test_citation_manager()
        test_paper_citation_methods()
        test_writer_agent_citations()
        
        print("\n🎉 所有测试完成!")
        print("\n📋 测试总结:")
        print("✅ 引用格式化器: 正常工作")
        print("✅ 引用管理器: 正常工作")
        print("✅ Paper模型引用方法: 正常工作")
        print("✅ WriterAgent引用功能: 正常工作")
        print("✅ 配置系统: 正常工作")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
