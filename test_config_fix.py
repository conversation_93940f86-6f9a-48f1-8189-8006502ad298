#!/usr/bin/env python3
# test_config_fix.py
#
# 测试配置错误修复

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_config_attributes():
    """测试配置属性是否正确"""
    print("🧪 Testing Configuration Attributes")
    print("=" * 60)
    
    try:
        from config import config
        
        # 测试正确的属性名称
        print("✅ Testing correct attribute names:")
        
        # 这些应该存在
        correct_attrs = [
            'DEFAULT_LLM_PROVIDER',
            'DEFAULT_LLM_TEMPERATURE', 
            'DEFAULT_LLM_MAX_TOKENS'
        ]
        
        for attr in correct_attrs:
            if hasattr(config, attr):
                value = getattr(config, attr)
                print(f"   ✅ {attr}: {value}")
            else:
                print(f"   ❌ {attr}: NOT FOUND")
        
        # 这些不应该存在（旧的错误属性名）
        print("\n❌ Testing old incorrect attribute names (should not exist):")
        incorrect_attrs = [
            'LLM_PROVIDER',
            'LLM_TEMPERATURE',
            'LLM_MAX_TOKENS'
        ]
        
        for attr in incorrect_attrs:
            if hasattr(config, attr):
                print(f"   ❌ {attr}: STILL EXISTS (should be removed)")
            else:
                print(f"   ✅ {attr}: Correctly removed")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_llm_client_initialization():
    """测试LLM客户端初始化"""
    print("\n🧪 Testing LLM Client Initialization")
    print("=" * 60)
    
    try:
        from clients.llm_client import LLMClient
        
        print("Attempting to create LLM client...")
        client = LLMClient()
        
        if client.client:
            print("✅ LLM client initialized successfully")
            print(f"   Client type: {type(client.client).__name__}")
            return True
        else:
            print("❌ LLM client is None")
            return False
            
    except AttributeError as e:
        if "'Config' object has no attribute 'LLM_PROVIDER'" in str(e):
            print(f"❌ Configuration error still exists: {e}")
            return False
        else:
            print(f"❌ Other AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠️  LLM client initialization failed (may be due to missing API keys): {e}")
        # This might be expected if API keys are not configured
        return True


def test_research_assistant_initialization():
    """测试ResearchAssistant初始化"""
    print("\n🧪 Testing ResearchAssistant Initialization")
    print("=" * 60)
    
    try:
        from main import ResearchAssistant
        
        print("Attempting to create ResearchAssistant...")
        assistant = ResearchAssistant()
        
        print("✅ ResearchAssistant initialized successfully")
        print(f"   Planner: {type(assistant.planner).__name__}")
        print(f"   Synthesizer: {type(assistant.synthesizer).__name__}")
        print(f"   Writer: {type(assistant.writer).__name__}")
        return True
        
    except AttributeError as e:
        if "'Config' object has no attribute" in str(e):
            print(f"❌ Configuration error still exists: {e}")
            return False
        else:
            print(f"❌ Other AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠️  ResearchAssistant initialization failed (may be due to API connection issues): {e}")
        # This might be expected if API connections fail
        return True


def test_health_check():
    """测试健康检查功能"""
    print("\n🧪 Testing Health Check")
    print("=" * 60)
    
    try:
        from utils.monitoring import HealthChecker
        
        print("Running health check...")
        health_checker = HealthChecker()
        health_summary = health_checker.get_health_summary()
        
        print("✅ Health check completed successfully")
        print(f"   Overall status: {health_summary['overall_status']}")
        print(f"   Services: {health_summary['healthy_services']} healthy, {health_summary['unhealthy_services']} unhealthy")
        
        return True
        
    except AttributeError as e:
        if "'Config' object has no attribute" in str(e):
            print(f"❌ Configuration error in health check: {e}")
            return False
        else:
            print(f"❌ Other AttributeError in health check: {e}")
            return False
    except Exception as e:
        print(f"⚠️  Health check failed: {e}")
        return True


def test_config_validation():
    """测试配置验证"""
    print("\n🧪 Testing Configuration Validation")
    print("=" * 60)
    
    try:
        from config import config
        
        print("Running configuration validation...")
        
        try:
            config.validate_config()
            print("✅ Configuration validation passed")
            return True
        except ValueError as e:
            print(f"⚠️  Configuration validation warnings: {e}")
            return True  # Warnings are acceptable
        except AttributeError as e:
            if "'Config' object has no attribute" in str(e):
                print(f"❌ Configuration error in validation: {e}")
                return False
            else:
                raise
        
    except Exception as e:
        print(f"❌ Configuration validation test failed: {e}")
        return False


def test_import_statements():
    """测试关键模块导入"""
    print("\n🧪 Testing Module Imports")
    print("=" * 60)
    
    modules_to_test = [
        'config',
        'clients.llm_client',
        'agents.planner_agent',
        'agents.synthesizer_agent', 
        'agents.writer_agent',
        'utils.monitoring',
        'main'
    ]
    
    success_count = 0
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name}")
            success_count += 1
        except AttributeError as e:
            if "'Config' object has no attribute" in str(e):
                print(f"   ❌ {module_name}: Configuration error - {e}")
            else:
                print(f"   ❌ {module_name}: AttributeError - {e}")
        except Exception as e:
            print(f"   ⚠️  {module_name}: {e}")
            success_count += 1  # Other errors might be expected
    
    print(f"\nImport success rate: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)


def main():
    """运行所有测试"""
    print("🚀 Configuration Fix Verification")
    print("=" * 80)
    
    tests = [
        ("Configuration Attributes", test_config_attributes),
        ("Module Imports", test_import_statements),
        ("Configuration Validation", test_config_validation),
        ("LLM Client Initialization", test_llm_client_initialization),
        ("Health Check", test_health_check),
        ("ResearchAssistant Initialization", test_research_assistant_initialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Configuration errors have been fixed.")
    elif passed >= total * 0.8:
        print("\n✅ Most tests passed. Minor issues may remain but core functionality should work.")
    else:
        print("\n❌ Multiple tests failed. Configuration errors may still exist.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
