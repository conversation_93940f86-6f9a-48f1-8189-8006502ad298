#!/usr/bin/env python3
# test_config_optimization.py
#
# 配置优化验证测试

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from clients import LLMClient, OpenAICompatibleClient


def test_token_limits():
    """测试TOKEN限制配置"""
    print("=" * 60)
    print("🚀 TOKEN限制配置测试")
    print("=" * 60)
    
    # 检查当前配置
    print("📊 当前TOKEN配置:")
    print(f"  - 全局默认: {config.DEFAULT_LLM_MAX_TOKENS}")
    print(f"  - PlannerAgent: {config.PLANNER_MAX_TOKENS}")
    print(f"  - SynthesizerAgent: {config.SYNTHESIZER_MAX_TOKENS}")
    print(f"  - WriterAgent: {config.WRITER_MAX_TOKENS}")
    
    # 验证配置合理性
    checks = {
        "全局默认TOKEN >= 16000": config.DEFAULT_LLM_MAX_TOKENS >= 16000,
        "PlannerAgent TOKEN >= 12000": config.PLANNER_MAX_TOKENS >= 12000,
        "SynthesizerAgent TOKEN >= 24000": config.SYNTHESIZER_MAX_TOKENS >= 24000,
        "WriterAgent TOKEN >= 32000": config.WRITER_MAX_TOKENS >= 32000,
        "配置递增合理": (config.PLANNER_MAX_TOKENS < config.SYNTHESIZER_MAX_TOKENS < config.WRITER_MAX_TOKENS)
    }
    
    print("\n✅ TOKEN配置验证:")
    for check, result in checks.items():
        print(f"  {'✅' if result else '❌'} {check}")
    
    passed = sum(checks.values())
    total = len(checks)
    print(f"\n📈 TOKEN配置评分: {passed}/{total} ({passed/total*100:.1f}%)")
    
    return passed >= total * 0.8


def test_paper_analysis_config():
    """测试论文分析配置"""
    print("\n" + "=" * 60)
    print("📊 论文分析配置测试")
    print("=" * 60)
    
    print("📋 当前论文分析配置:")
    print(f"  - 最大论文数: {config.MAX_PAPERS_TO_ANALYZE}")
    print(f"  - 批量分析大小: {config.PAPERS_PER_ANALYSIS_BATCH}")
    print(f"  - 传统批处理大小: {config.PAPER_BATCH_SIZE}")
    
    # 验证配置合理性
    checks = {
        "最大论文数 >= 100": config.MAX_PAPERS_TO_ANALYZE >= 100,
        "批量分析大小合理 (5-10)": 5 <= config.PAPERS_PER_ANALYSIS_BATCH <= 10,
        "传统批处理大小合理 (5-15)": 5 <= config.PAPER_BATCH_SIZE <= 15,
        "批量分析不超过传统批处理": config.PAPERS_PER_ANALYSIS_BATCH <= config.PAPER_BATCH_SIZE,
        "配置支持大规模分析": config.MAX_PAPERS_TO_ANALYZE >= 150
    }
    
    print("\n✅ 论文分析配置验证:")
    for check, result in checks.items():
        print(f"  {'✅' if result else '❌'} {check}")
    
    # 计算理论性能
    batches_enhanced = config.MAX_PAPERS_TO_ANALYZE // config.PAPERS_PER_ANALYSIS_BATCH
    batches_traditional = config.MAX_PAPERS_TO_ANALYZE // config.PAPER_BATCH_SIZE
    
    print(f"\n📈 理论性能分析:")
    print(f"  - 增强模式批次数: {batches_enhanced}")
    print(f"  - 传统模式批次数: {batches_traditional}")
    print(f"  - 性能提升比例: {batches_traditional/batches_enhanced:.1f}x")
    
    passed = sum(checks.values())
    total = len(checks)
    print(f"\n📊 论文分析配置评分: {passed}/{total} ({passed/total*100:.1f}%)")
    
    return passed >= total * 0.8


def test_api_configuration():
    """测试API配置"""
    print("\n" + "=" * 60)
    print("🔧 API配置测试")
    print("=" * 60)
    
    print("🔑 API配置状态:")
    print(f"  - 默认LLM提供商: {config.DEFAULT_LLM_PROVIDER}")
    print(f"  - OpenAI兼容端点: {config.OPENAI_COMPATIBLE_BASE_URL}")
    print(f"  - OpenAI兼容模型: {config.OPENAI_COMPATIBLE_MODEL}")
    
    # 检查代理配置
    print(f"\n🤖 代理配置:")
    for agent_type in ['planner', 'synthesizer', 'writer']:
        agent_config = config.get_agent_config(agent_type)
        print(f"  - {agent_type.capitalize()}Agent:")
        print(f"    * 提供商: {agent_config.get('provider', 'N/A')}")
        print(f"    * 模型: {agent_config.get('model', 'N/A')}")
        print(f"    * 温度: {agent_config.get('temperature', 'N/A')}")
        print(f"    * 最大TOKEN: {agent_config.get('max_tokens', 'N/A')}")
    
    # 验证配置
    checks = {
        "使用OpenAI兼容模式": config.DEFAULT_LLM_PROVIDER == "openai-compatible",
        "兼容端点已配置": bool(config.OPENAI_COMPATIBLE_BASE_URL),
        "兼容API密钥已配置": bool(config.OPENAI_COMPATIBLE_API_KEY),
        "兼容模型已配置": bool(config.OPENAI_COMPATIBLE_MODEL),
        "所有代理使用兼容模式": all(
            config.get_agent_config(agent).get('provider') == 'openai-compatible' 
            for agent in ['planner', 'synthesizer', 'writer']
        )
    }
    
    print(f"\n✅ API配置验证:")
    for check, result in checks.items():
        print(f"  {'✅' if result else '❌'} {check}")
    
    passed = sum(checks.values())
    total = len(checks)
    print(f"\n📊 API配置评分: {passed}/{total} ({passed/total*100:.1f}%)")
    
    return passed >= total * 0.8


def test_client_creation():
    """测试客户端创建"""
    print("\n" + "=" * 60)
    print("🔌 客户端创建测试")
    print("=" * 60)
    
    try:
        # 测试OpenAI兼容客户端创建
        print("🧪 测试OpenAI兼容客户端创建...")
        client = OpenAICompatibleClient()
        print("✅ OpenAI兼容客户端创建成功")
        
        # 测试LLM客户端创建
        print("🧪 测试LLM客户端创建...")
        llm_client = LLMClient()
        print("✅ LLM客户端创建成功")
        
        # 测试代理特定客户端创建
        print("🧪 测试代理特定客户端创建...")
        for agent_type in ['planner', 'synthesizer', 'writer']:
            agent_client = llm_client.create_client_for_agent(agent_type)
            print(f"✅ {agent_type.capitalize()}Agent客户端创建成功")
        
        print("\n🎉 所有客户端创建测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        return False


def test_configuration_validation():
    """测试配置验证"""
    print("\n" + "=" * 60)
    print("🔍 配置验证测试")
    print("=" * 60)
    
    try:
        print("🧪 运行配置验证...")
        config.validate_config()
        print("✅ 配置验证通过")
        
        # 显示配置摘要
        summary = config.get_config_summary()
        print(f"\n📋 配置摘要:")
        for key, value in summary.items():
            print(f"  - {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False


def main():
    """运行所有配置优化测试"""
    print("🚀 AI研究助理配置优化验证")
    print("=" * 80)
    
    # 运行所有测试
    test_results = {
        "TOKEN限制优化": test_token_limits(),
        "论文分析配置": test_paper_analysis_config(),
        "API配置": test_api_configuration(),
        "客户端创建": test_client_creation(),
        "配置验证": test_configuration_validation()
    }
    
    print("\n" + "=" * 80)
    print("📊 配置优化测试结果")
    print("=" * 80)
    
    passed_tests = 0
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    total_tests = len(test_results)
    success_rate = passed_tests / total_tests * 100
    
    print(f"\n📈 总体评分: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 配置优化成功!")
        print("\n🏆 优化成果:")
        print("  ✅ TOKEN限制大幅提升，支持更完整的AI响应")
        print("  ✅ 论文分析数量增加到200篇，支持大规模研究")
        print("  ✅ 批处理优化，提高分析效率")
        print("  ✅ OpenAI兼容模式配置正确")
        print("  ✅ 所有代理使用统一的高性能模型")
    else:
        print("\n⚠️  配置需要进一步优化")
        print("\n🔧 建议检查:")
        for test_name, result in test_results.items():
            if not result:
                print(f"  - {test_name}")
    
    print("\n📋 当前优化配置:")
    print("```bash")
    print("# TOKEN限制优化")
    print(f"DEFAULT_LLM_MAX_TOKENS={config.DEFAULT_LLM_MAX_TOKENS}")
    print(f"PLANNER_MAX_TOKENS={config.PLANNER_MAX_TOKENS}")
    print(f"SYNTHESIZER_MAX_TOKENS={config.SYNTHESIZER_MAX_TOKENS}")
    print(f"WRITER_MAX_TOKENS={config.WRITER_MAX_TOKENS}")
    print("")
    print("# 论文分析优化")
    print(f"MAX_PAPERS_TO_ANALYZE={config.MAX_PAPERS_TO_ANALYZE}")
    print(f"PAPERS_PER_ANALYSIS_BATCH={config.PAPERS_PER_ANALYSIS_BATCH}")
    print(f"PAPER_BATCH_SIZE={config.PAPER_BATCH_SIZE}")
    print("```")


if __name__ == "__main__":
    main()
