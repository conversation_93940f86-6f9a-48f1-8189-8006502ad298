#!/usr/bin/env python3
# test_dialog_fix.py
#
# 测试MainDialog修复

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_main_dialog_creation():
    """测试MainDialog创建"""
    print("🧪 Testing MainDialog Creation")
    print("=" * 50)
    
    try:
        from gui.main_dialog import MainDialog

        print("📋 开始创建MainDialog实例...")

        # 创建对话框实例（不显示）
        dialog = MainDialog()
        print("✅ MainDialog 实例创建成功")

        # 检查创建过程中的属性
        print(f"📋 检查属性创建状态:")
        print(f"   - root: {hasattr(dialog, 'root')}")
        print(f"   - storage: {hasattr(dialog, 'storage')}")
        print(f"   - logger: {hasattr(dialog, 'logger')}")
        print(f"   - topic_entry: {hasattr(dialog, 'topic_entry')}")
        print(f"   - use_history_var: {hasattr(dialog, 'use_history_var')}")
        print(f"   - history_var: {hasattr(dialog, 'history_var')}")
        print(f"   - history_combobox: {hasattr(dialog, 'history_combobox')}")
        
        # 检查关键属性
        attrs_to_check = [
            'history_combobox',
            'history_var', 
            'history_plans',
            'selected_plan',
            'use_history_var'
        ]
        
        for attr in attrs_to_check:
            if hasattr(dialog, attr):
                print(f"✅ 属性存在: {attr}")
            else:
                print(f"❌ 属性缺失: {attr}")
                return False
        
        # 检查方法
        methods_to_check = [
            '_on_history_combobox_select',
            '_load_plan_from_file',
            '_refresh_history_plans'
        ]
        
        for method in methods_to_check:
            if hasattr(dialog, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
                return False
        
        # 测试刷新历史计划（应该不会出错）
        try:
            dialog._refresh_history_plans()
            print("✅ _refresh_history_plans() 调用成功")
        except Exception as e:
            print(f"❌ _refresh_history_plans() 调用失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ MainDialog creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行测试"""
    print("🚀 MainDialog修复测试")
    print("=" * 60)
    
    success = test_main_dialog_creation()
    
    if success:
        print("\n🎉 MainDialog修复测试通过！")
        print("✅ 所有属性和方法正确创建")
        print("✅ 初始化顺序问题已修复")
        print("✅ 历史计划刷新功能正常")
    else:
        print("\n❌ MainDialog修复测试失败")
        print("🔧 需要进一步检查和修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
