#!/usr/bin/env python3
# test_enhancements.py
#
# Test script to verify all the implemented enhancements

import os
import sys
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from agents import PlannerAgent, SynthesizerAgent, WriterAgent
from clients import LLMClient, ADSClient, WebSearchClient
from models import Paper, PaperAnalysis
from prompts import Prompts


def test_config_enhancements():
    """Test the enhanced configuration system"""
    print("=" * 60)
    print("Testing Enhanced Configuration System")
    print("=" * 60)
    
    # Test multi-model configuration
    print(f"Default LLM Provider: {config.DEFAULT_LLM_PROVIDER}")
    print(f"Planner Agent Provider: {config.PLANNER_AGENT_PROVIDER}")
    print(f"Synthesizer Agent Provider: {config.SYNTHESIZER_AGENT_PROVIDER}")
    print(f"Writer Agent Provider: {config.WRITER_AGENT_PROVIDER}")
    
    # Test agent-specific configurations
    planner_config = config.get_agent_config("planner")
    print(f"Planner Config: {planner_config}")
    
    synthesizer_config = config.get_agent_config("synthesizer")
    print(f"Synthesizer Config: {synthesizer_config}")
    
    writer_config = config.get_agent_config("writer")
    print(f"Writer Config: {writer_config}")
    
    # Test new paper retrieval parameters
    print(f"ADS Papers per Sub-question: {config.ADS_PAPERS_PER_SUBQUESTION}")
    print(f"Top Cited Count: {config.ADS_TOP_CITED_COUNT}")
    print(f"Recent Papers Count: {config.ADS_RECENT_PAPERS_COUNT}")
    print(f"Papers per Analysis Batch: {config.PAPERS_PER_ANALYSIS_BATCH}")
    
    print("✅ Configuration system test completed\n")


def test_prompt_system():
    """Test the enhanced prompt system"""
    print("=" * 60)
    print("Testing Enhanced Prompt System")
    print("=" * 60)
    
    # Test that all prompts are in English
    print("Checking prompt language...")
    
    # Check planner prompt
    planner_prompt = Prompts.PLANNER_PROMPT
    print(f"Planner prompt length: {len(planner_prompt)} characters")
    print(f"Contains 'research': {'research' in planner_prompt.lower()}")
    
    # Check keyword generator prompt
    keyword_prompt = Prompts.KEYWORD_GENERATOR_PROMPT
    print(f"Keyword generator prompt length: {len(keyword_prompt)} characters")
    print(f"Contains 'ADS': {'ADS' in keyword_prompt}")
    
    # Check batch analyzer prompt
    batch_prompt = Prompts.BATCH_PAPER_ANALYZER_PROMPT
    print(f"Batch analyzer prompt length: {len(batch_prompt)} characters")
    print(f"Contains 'JSON': {'JSON' in batch_prompt}")
    
    print("✅ Prompt system test completed\n")


def test_planner_agent():
    """Test the enhanced PlannerAgent"""
    print("=" * 60)
    print("Testing Enhanced PlannerAgent")
    print("=" * 60)
    
    try:
        # Create a mock LLM client for testing
        class MockLLMClient:
            def generate(self, prompt, **kwargs):
                class MockResponse:
                    def __init__(self):
                        self.response = '''
                        {
                            "sub_questions": [
                                "What are the fundamental concepts of deep learning?",
                                "How has deep learning evolved historically?",
                                "What are the current methodologies in deep learning?",
                                "What are recent advances in deep learning?",
                                "What are practical applications of deep learning?"
                            ],
                            "keyword_sets": {
                                "sub_question_1": ["deep learning", "neural networks", "fundamentals"],
                                "sub_question_2": ["deep learning history", "evolution", "development"],
                                "sub_question_3": ["deep learning methods", "techniques", "algorithms"],
                                "sub_question_4": ["recent advances", "breakthroughs", "innovations"],
                                "sub_question_5": ["applications", "practical use", "implementation"]
                            }
                        }
                        '''
                    
                    def is_successful(self):
                        return True
                
                return MockResponse()
        
        # Test planner with mock client
        planner = PlannerAgent(MockLLMClient())
        research_query = planner.generate_research_plan("Deep Learning in Astrophysics")
        
        print(f"Main Topic: {research_query.main_topic}")
        print(f"Number of Sub-questions: {len(research_query.sub_questions)}")
        print(f"Sub-questions: {research_query.sub_questions}")
        print(f"Number of Keywords: {len(research_query.keywords)}")
        print(f"Has keyword mapping: {hasattr(research_query, 'sub_question_keywords')}")
        
        if hasattr(research_query, 'sub_question_keywords'):
            print(f"Keyword mapping keys: {list(research_query.sub_question_keywords.keys())}")
        
        print("✅ PlannerAgent test completed\n")
        
    except Exception as e:
        print(f"❌ PlannerAgent test failed: {e}\n")


def test_ads_client():
    """Test the enhanced ADS client"""
    print("=" * 60)
    print("Testing Enhanced ADS Client")
    print("=" * 60)
    
    try:
        ads_client = ADSClient()
        
        # Test if enhanced methods exist
        has_enhanced_search = hasattr(ads_client, 'search_papers_enhanced')
        has_bulk_retrieve = hasattr(ads_client, '_retrieve_papers_bulk')
        has_two_tier_sorting = hasattr(ads_client, '_apply_two_tier_sorting')
        has_deduplication = hasattr(ads_client, '_deduplicate_papers')
        
        print(f"Has enhanced search method: {has_enhanced_search}")
        print(f"Has bulk retrieve method: {has_bulk_retrieve}")
        print(f"Has two-tier sorting method: {has_two_tier_sorting}")
        print(f"Has deduplication method: {has_deduplication}")
        
        # Test title normalization
        if hasattr(ads_client, '_normalize_title'):
            test_title = "Deep Learning: A Comprehensive Survey of Methods and Applications"
            normalized = ads_client._normalize_title(test_title)
            print(f"Title normalization test: '{test_title}' -> '{normalized}'")
        
        print("✅ ADS Client test completed\n")
        
    except Exception as e:
        print(f"❌ ADS Client test failed: {e}\n")


def test_synthesizer_agent():
    """Test the enhanced SynthesizerAgent"""
    print("=" * 60)
    print("Testing Enhanced SynthesizerAgent")
    print("=" * 60)
    
    try:
        # Create mock papers for testing
        mock_papers = [
            Paper(
                title=f"Test Paper {i}",
                abstract=f"This is a test abstract for paper {i} about deep learning applications.",
                authors=[f"Author {i}"],
                publication_date="2023-01-01"
            )
            for i in range(1, 6)
        ]
        
        # Create a mock LLM client
        class MockLLMClient:
            def generate(self, prompt, **kwargs):
                class MockResponse:
                    def __init__(self):
                        # Mock batch analysis response
                        self.response = '''
                        [
                            {
                                "paper_index": 1,
                                "short_summary": "Test summary 1",
                                "relevance_to_topic": "Highly relevant",
                                "research_subject": "Deep learning models",
                                "methodology": "Experimental analysis",
                                "data_used": "Synthetic datasets",
                                "key_findings_or_results": ["Finding 1", "Finding 2"],
                                "technical_significance": "Significant contribution",
                                "future_implications": "Promising directions"
                            }
                        ]
                        '''
                    
                    def is_successful(self):
                        return True
                
                return MockResponse()
        
        synthesizer = SynthesizerAgent(MockLLMClient())
        
        # Test if enhanced methods exist
        has_enhanced_batch = hasattr(synthesizer, 'batch_analyze_papers_enhanced')
        has_batch_analysis = hasattr(synthesizer, '_analyze_paper_batch')
        has_batch_parsing = hasattr(synthesizer, '_parse_batch_analysis_response')
        
        print(f"Has enhanced batch analysis: {has_enhanced_batch}")
        print(f"Has batch analysis method: {has_batch_analysis}")
        print(f"Has batch parsing method: {has_batch_parsing}")
        
        print("✅ SynthesizerAgent test completed\n")
        
    except Exception as e:
        print(f"❌ SynthesizerAgent test failed: {e}\n")


def test_llm_client():
    """Test the enhanced LLM client with multi-model support"""
    print("=" * 60)
    print("Testing Enhanced LLM Client")
    print("=" * 60)
    
    try:
        llm_client = LLMClient()
        
        # Test if multi-model methods exist
        has_agent_client = hasattr(llm_client, 'create_client_for_agent')
        
        print(f"Has agent-specific client creation: {has_agent_client}")
        
        # Test available providers
        available_providers = config.get_available_providers()
        print(f"Available LLM providers: {available_providers['llm']}")
        
        # Test agent configurations
        for agent_type in ['planner', 'synthesizer', 'writer']:
            agent_config = config.get_agent_config(agent_type)
            print(f"{agent_type.capitalize()} agent config: {agent_config}")
        
        print("✅ LLM Client test completed\n")
        
    except Exception as e:
        print(f"❌ LLM Client test failed: {e}\n")


def main():
    """Run all enhancement tests"""
    print("🚀 Testing AI Research Assistant Enhancements")
    print("=" * 80)
    
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Reduce log noise during testing
    
    # Run all tests
    test_config_enhancements()
    test_prompt_system()
    test_planner_agent()
    test_ads_client()
    test_synthesizer_agent()
    test_llm_client()
    
    print("=" * 80)
    print("🎉 All enhancement tests completed!")
    print("\nNext steps:")
    print("1. Configure your API keys in .env file")
    print("2. Run: python main.py --health")
    print("3. Run: python main.py")
    print("4. Test with a research topic like 'Deep Learning in Astrophysics'")


if __name__ == "__main__":
    main()
