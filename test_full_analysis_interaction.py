#!/usr/bin/env python3
# test_full_analysis_interaction.py
#
# 测试full_analysis模式的交互逻辑

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from main import ResearchAssistant


def create_mock_research_report():
    """创建模拟的研究报告文件用于测试"""
    output_dir = Path(config.OUTPUT_DIR)
    output_dir.mkdir(exist_ok=True)
    
    report_path = output_dir / config.RESEARCH_REPORT_FILENAME
    
    mock_report = """# Deep Learning Applications in Astrophysics: Comprehensive Research Report

## Executive Summary

This report provides a comprehensive analysis of deep learning applications in astrophysics, 
covering recent advances, methodologies, and future directions in the field.

## Current State of Knowledge

Deep learning has revolutionized many aspects of astrophysical research, from galaxy 
classification to exoplanet detection. Recent studies have demonstrated significant 
improvements in accuracy and efficiency across various astronomical tasks.

### Galaxy Classification
Recent work has shown that convolutional neural networks can achieve over 95% accuracy 
in galaxy morphology classification tasks, significantly outperforming traditional methods.

### Exoplanet Detection
Machine learning approaches, particularly neural networks, have enhanced the sensitivity 
of exoplanet detection in transit photometry data, leading to the discovery of numerous 
new planetary candidates.

## Key Methodologies

The most successful approaches in this field include:
- Convolutional Neural Networks (CNNs) for image-based tasks
- Recurrent Neural Networks (RNNs) for time-series analysis
- Transformer architectures for sequence modeling
- Generative models for data augmentation

## Future Directions

Emerging trends include:
- Multi-modal learning approaches
- Unsupervised discovery methods
- Real-time processing capabilities
- Interpretable AI models

## Conclusion

Deep learning continues to transform astrophysical research, offering unprecedented 
capabilities for data analysis and discovery. The field is poised for continued growth 
and innovation in the coming years.

## References

[Generated automatically - this is a mock report for testing purposes]
"""
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(mock_report)
    
    print(f"✅ 创建模拟研究报告: {report_path}")
    return report_path


def test_interaction_with_existing_report():
    """测试存在现有报告时的交互逻辑"""
    print("🧪 测试场景1: 存在现有研究报告")
    print("=" * 60)
    
    # 创建模拟报告
    report_path = create_mock_research_report()
    
    # 设置为full_analysis模式
    original_mode = config.EXECUTION_MODE
    config.EXECUTION_MODE = "full_analysis"
    
    try:
        # 创建研究助理实例
        assistant = ResearchAssistant()
        
        # 测试检查现有报告的方法
        has_report = assistant._check_existing_research_report()
        print(f"检测到现有报告: {has_report}")
        
        if has_report:
            print("✅ 现有报告检测功能正常")
        else:
            print("❌ 现有报告检测失败")
        
        # 显示交互选项（但不实际执行）
        print("\n📋 交互选项预览:")
        print("如果运行 assistant._show_full_analysis_options('测试主题')，")
        print("用户将看到两个选项：")
        print("1. 重新开始完整研究")
        print("2. 基于现有报告继续")
        
    finally:
        # 恢复原始配置
        config.EXECUTION_MODE = original_mode
        
        # 清理测试文件
        if report_path.exists():
            report_path.unlink()
            print(f"🧹 清理测试文件: {report_path}")


def test_interaction_without_existing_report():
    """测试不存在现有报告时的交互逻辑"""
    print("\n🧪 测试场景2: 不存在现有研究报告")
    print("=" * 60)
    
    # 确保没有现有报告
    output_dir = Path(config.OUTPUT_DIR)
    report_path = output_dir / config.RESEARCH_REPORT_FILENAME
    if report_path.exists():
        report_path.unlink()
    
    # 设置为full_analysis模式
    original_mode = config.EXECUTION_MODE
    config.EXECUTION_MODE = "full_analysis"
    
    try:
        # 创建研究助理实例
        assistant = ResearchAssistant()
        
        # 测试检查现有报告的方法
        has_report = assistant._check_existing_research_report()
        print(f"检测到现有报告: {has_report}")
        
        if not has_report:
            print("✅ 无现有报告检测功能正常")
        else:
            print("❌ 无现有报告检测失败")
        
        # 显示交互选项（但不实际执行）
        print("\n📋 交互选项预览:")
        print("如果运行 assistant._show_full_analysis_options('测试主题')，")
        print("用户将看到完整研究流程的确认对话框")
        
    finally:
        # 恢复原始配置
        config.EXECUTION_MODE = original_mode


def test_deep_research_mode():
    """测试deep_research模式（应该没有交互逻辑）"""
    print("\n🧪 测试场景3: deep_research模式")
    print("=" * 60)
    
    # 设置为deep_research模式
    original_mode = config.EXECUTION_MODE
    config.EXECUTION_MODE = "deep_research"
    
    try:
        # 创建研究助理实例
        assistant = ResearchAssistant()
        
        print(f"当前执行模式: {config.EXECUTION_MODE}")
        print("✅ deep_research模式不应该显示交互选项")
        print("✅ 将直接执行标准的研究流程")
        
    finally:
        # 恢复原始配置
        config.EXECUTION_MODE = original_mode


def test_configuration_values():
    """测试相关配置值"""
    print("\n🧪 测试场景4: 配置值验证")
    print("=" * 60)
    
    print(f"OUTPUT_DIR: {config.OUTPUT_DIR}")
    print(f"RESEARCH_REPORT_FILENAME: {config.RESEARCH_REPORT_FILENAME}")
    print(f"当前EXECUTION_MODE: {config.EXECUTION_MODE}")
    
    # 检查输出目录
    output_dir = Path(config.OUTPUT_DIR)
    print(f"输出目录存在: {output_dir.exists()}")
    
    if not output_dir.exists():
        output_dir.mkdir(exist_ok=True)
        print(f"✅ 创建输出目录: {output_dir}")


def main():
    """运行所有测试"""
    print("🚀 Full Analysis 交互逻辑测试")
    print("=" * 80)
    
    try:
        # 测试配置值
        test_configuration_values()
        
        # 测试存在现有报告的情况
        test_interaction_with_existing_report()
        
        # 测试不存在现有报告的情况
        test_interaction_without_existing_report()
        
        # 测试deep_research模式
        test_deep_research_mode()
        
        print("\n🎉 所有测试完成!")
        print("\n📋 测试总结:")
        print("✅ 现有报告检测功能正常")
        print("✅ 交互逻辑设计合理")
        print("✅ 配置值验证通过")
        print("✅ 模式区分正确")
        
        print("\n💡 使用说明:")
        print("1. 设置 EXECUTION_MODE=full_analysis")
        print("2. 运行 python main.py")
        print("3. 输入研究主题")
        print("4. 根据是否存在现有报告，系统会显示相应的交互选项")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
