#!/usr/bin/env python3
# test_gui_interaction.py
#
# 测试GUI交互功能

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_main_dialog():
    """测试主对话框"""
    print("🧪 Testing Main Dialog")
    print("=" * 50)
    
    try:
        from gui.main_dialog import show_main_dialog
        
        print("✅ Main dialog import successful")
        print("📋 To test the dialog, uncomment the following line:")
        print("# result = show_main_dialog()")
        print("# if result:")
        print("#     topic, mode = result")
        print("#     print(f'Topic: {topic}')")
        print("#     print(f'Mode: {mode}')")
        
        # Uncomment to actually test the dialog
        # result = show_main_dialog()
        # if result:
        #     topic, mode = result
        #     print(f"Topic: {topic}")
        #     print(f"Mode: {mode}")
        # else:
        #     print("Dialog cancelled")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure tkinter is installed: pip install tk")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_planner_review_dialog():
    """测试PlannerAgent结果预览对话框"""
    print("\n🧪 Testing Planner Review Dialog")
    print("=" * 50)
    
    try:
        from gui.planner_review_dialog import show_planner_review_dialog
        
        print("✅ Planner review dialog import successful")
        
        # Test data
        test_topic = "Deep Learning Applications in Astrophysics"
        test_questions = [
            "What are the current applications of deep learning in astrophysics?",
            "How do neural networks improve astronomical data analysis?",
            "What are the challenges in applying AI to astrophysical research?",
            "What are the future prospects for AI in astronomy?"
        ]
        test_keywords = [
            "deep learning", "astrophysics", "neural networks", 
            "data analysis", "machine learning", "astronomy",
            "artificial intelligence", "cosmic data"
        ]
        
        print("📋 To test the dialog, uncomment the following lines:")
        print("# result = show_planner_review_dialog(")
        print("#     test_topic, test_questions, test_keywords, 160, '20-30 minutes'")
        print("# )")
        print("# if result:")
        print("#     action, questions, keywords = result")
        print("#     print(f'Action: {action}')")
        print("#     print(f'Questions: {len(questions)}')")
        print("#     print(f'Keywords: {len(keywords)}')")
        
        # Uncomment to actually test the dialog
        # result = show_planner_review_dialog(
        #     test_topic, test_questions, test_keywords, 160, "20-30 minutes"
        # )
        # if result:
        #     action, questions, keywords = result
        #     print(f"Action: {action}")
        #     print(f"Questions: {len(questions)}")
        #     print(f"Keywords: {len(keywords)}")
        # else:
        #     print("Dialog cancelled")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure tkinter is installed: pip install tk")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_planner_agent_integration():
    """测试PlannerAgent集成"""
    print("\n🧪 Testing PlannerAgent Integration")
    print("=" * 50)
    
    try:
        from agents.planner_agent import PlannerAgent
        from config import config
        
        print("✅ PlannerAgent import successful")
        
        # Test if new method exists
        planner = PlannerAgent()
        
        if hasattr(planner, 'generate_research_plan_with_confirmation'):
            print("✅ generate_research_plan_with_confirmation method exists")
        else:
            print("❌ generate_research_plan_with_confirmation method not found")
        
        if hasattr(planner, '_show_gui_confirmation'):
            print("✅ _show_gui_confirmation method exists")
        else:
            print("❌ _show_gui_confirmation method not found")
        
        if hasattr(planner, '_show_console_confirmation'):
            print("✅ _show_console_confirmation method exists")
        else:
            print("❌ _show_console_confirmation method not found")
        
        print("📋 To test the full integration, you would call:")
        print("# query = planner.generate_research_plan_with_confirmation('test topic', use_gui=False)")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_research_query_update():
    """测试ResearchQuery更新方法"""
    print("\n🧪 Testing ResearchQuery Update Method")
    print("=" * 50)
    
    try:
        from models import ResearchQuery
        
        print("✅ ResearchQuery import successful")
        
        # Create test query
        query = ResearchQuery(main_topic="Test Topic")
        query.sub_questions = ["Question 1", "Question 2", "Question 3"]
        query.keywords = ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5", "keyword6"]
        
        if hasattr(query, '_update_sub_question_keywords'):
            print("✅ _update_sub_question_keywords method exists")
            
            # Test the method
            query._update_sub_question_keywords()
            
            print(f"📊 Sub-question keywords mapping:")
            for key, keywords in query.sub_question_keywords.items():
                print(f"   {key}: {keywords}")
                
        else:
            print("❌ _update_sub_question_keywords method not found")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_gui_dependencies():
    """测试GUI依赖"""
    print("\n🧪 Testing GUI Dependencies")
    print("=" * 50)
    
    try:
        import tkinter as tk
        print("✅ tkinter available")
        
        # Test basic tkinter functionality
        root = tk.Tk()
        root.withdraw()  # Hide the window
        print("✅ tkinter basic functionality works")
        root.destroy()
        
    except ImportError:
        print("❌ tkinter not available")
        print("💡 Install tkinter:")
        print("   - On Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   - On CentOS/RHEL: sudo yum install tkinter")
        print("   - On Windows: Usually included with Python")
        print("   - On macOS: Usually included with Python")
    except Exception as e:
        print(f"❌ tkinter error: {e}")


def main():
    """运行所有测试"""
    print("🚀 GUI Interaction Testing")
    print("=" * 80)
    
    # Test GUI dependencies first
    test_gui_dependencies()
    
    # Test individual components
    test_main_dialog()
    test_planner_review_dialog()
    test_planner_agent_integration()
    test_research_query_update()
    
    print("\n🎉 Testing Complete!")
    print("=" * 80)
    
    print("\n💡 Usage Instructions:")
    print("1. To test the main dialog:")
    print("   python -c \"from gui.main_dialog import show_main_dialog; print(show_main_dialog())\"")
    
    print("\n2. To test the planner review dialog:")
    print("   python gui/planner_review_dialog.py")
    
    print("\n3. To test the full GUI integration:")
    print("   python main.py")
    
    print("\n📋 Features Implemented:")
    print("✅ Main dialog with topic input and mode selection")
    print("✅ Planner review dialog with editable questions and keywords")
    print("✅ PlannerAgent integration with confirmation workflow")
    print("✅ ResearchQuery update method for modified plans")
    print("✅ GUI fallback to console if tkinter unavailable")
    print("✅ Full integration with existing full_analysis interaction logic")


if __name__ == "__main__":
    main()
