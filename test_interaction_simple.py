#!/usr/bin/env python3
# test_interaction_simple.py
#
# 简化的交互逻辑测试，避免完整系统初始化

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config


def create_mock_research_report():
    """创建模拟的研究报告文件用于测试"""
    output_dir = Path(config.OUTPUT_DIR)
    output_dir.mkdir(exist_ok=True)
    
    report_path = output_dir / config.RESEARCH_REPORT_FILENAME
    
    mock_report = """# Deep Learning Applications in Astrophysics: Comprehensive Research Report

## Executive Summary

This report provides a comprehensive analysis of deep learning applications in astrophysics, 
covering recent advances, methodologies, and future directions in the field.

## Current State of Knowledge

Deep learning has revolutionized many aspects of astrophysical research, from galaxy 
classification to exoplanet detection. Recent studies have demonstrated significant 
improvements in accuracy and efficiency across various astronomical tasks.

## Conclusion

Deep learning continues to transform astrophysical research, offering unprecedented 
capabilities for data analysis and discovery.
"""
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(mock_report)
    
    print(f"✅ 创建模拟研究报告: {report_path}")
    return report_path


def test_check_existing_report():
    """测试检查现有报告的功能"""
    print("🧪 测试现有报告检查功能")
    print("=" * 50)
    
    # 模拟ResearchAssistant的_check_existing_research_report方法
    def check_existing_research_report():
        report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
        return report_path.exists() and report_path.stat().st_size > 0
    
    # 测试1: 没有现有报告
    report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
    if report_path.exists():
        report_path.unlink()
    
    has_report = check_existing_research_report()
    print(f"测试1 - 无现有报告: {has_report} (期望: False)")
    
    # 测试2: 有现有报告
    create_mock_research_report()
    has_report = check_existing_research_report()
    print(f"测试2 - 有现有报告: {has_report} (期望: True)")
    
    # 清理
    if report_path.exists():
        report_path.unlink()
        print("🧹 清理测试文件")


def simulate_full_analysis_options(topic: str, has_existing_report: bool):
    """模拟full_analysis选项显示"""
    print("\n" + "=" * 70)
    print("🔬 Full Analysis 模式 - 执行选项")
    print("=" * 70)
    
    if has_existing_report:
        report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
        
        print(f"📋 检测到现有研究报告:")
        print(f"   文件: {report_path}")
        if report_path.exists():
            report_size = report_path.stat().st_size / 1024  # KB
            print(f"   大小: {report_size:.1f} KB")
            print(f"   修改时间: {datetime.fromtimestamp(report_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        print("请选择执行方式:")
        print()
        print("1️⃣  重新开始完整研究")
        print("   - 从头开始执行完整的 full_analysis 流程")
        print("   - 包括: 研究规划 → 信息采集 → 论文分析 → 报告生成 → 创新方案 → 可行性分析")
        print("   - ⚠️  将覆盖现有的所有报告文件")
        print()
        print("2️⃣  基于现有报告继续")
        print("   - 跳过前面的研究步骤，直接基于现有报告执行后续任务")
        print("   - 包括: 创新方案设计 → 可行性分析")
        print("   - ✅ 保留现有的研究报告和详细笔记")
        print()
        
        print("💡 用户可以选择 1 或 2")
        
    else:
        print("📋 未检测到现有研究报告，将执行完整的 full_analysis 流程")
        print()
        print("📋 执行计划:")
        print("   1. 研究规划 (PlannerAgent)")
        print("   2. 双轨信息采集 (网络搜索 + 学术搜索)")
        print("   3. 批量论文分析 (SynthesizerAgent)")
        print("   4. 深度报告生成 (WriterAgent)")
        print("   5. 创新方案设计 (WriterAgent)")
        print("   6. 可行性分析 (WriterAgent)")
        
        print("\n💡 用户只需确认开始完整研究")


def test_interaction_scenarios():
    """测试不同的交互场景"""
    print("\n🧪 测试交互场景")
    print("=" * 50)
    
    topic = "深度学习在天体物理学中的应用"
    
    # 场景1: 存在现有报告
    print("\n📋 场景1: 存在现有研究报告")
    create_mock_research_report()
    simulate_full_analysis_options(topic, True)
    
    # 场景2: 不存在现有报告
    print("\n📋 场景2: 不存在现有研究报告")
    report_path = Path(config.OUTPUT_DIR) / config.RESEARCH_REPORT_FILENAME
    if report_path.exists():
        report_path.unlink()
    simulate_full_analysis_options(topic, False)


def test_execution_flow_logic():
    """测试执行流程逻辑"""
    print("\n🧪 测试执行流程逻辑")
    print("=" * 50)
    
    print("📋 执行模式判断:")
    print(f"   当前模式: {config.EXECUTION_MODE}")
    
    if config.EXECUTION_MODE == "full_analysis":
        print("   ✅ 将显示交互选项")
        print("   ✅ 根据用户选择执行相应流程")
    else:
        print("   ✅ 直接执行标准研究流程")
        print("   ✅ 不显示交互选项")
    
    print("\n📋 流程分支:")
    print("   选择1 (重新开始): run_research() 完整流程")
    print("   选择2 (基于现有报告): _run_innovation_only() 简化流程")
    print("   取消: 抛出 KeyboardInterrupt")


def test_configuration():
    """测试相关配置"""
    print("\n🧪 测试配置")
    print("=" * 50)
    
    print(f"OUTPUT_DIR: {config.OUTPUT_DIR}")
    print(f"RESEARCH_REPORT_FILENAME: {config.RESEARCH_REPORT_FILENAME}")
    print(f"EXECUTION_MODE: {config.EXECUTION_MODE}")
    print(f"DEFAULT_LLM_PROVIDER: {config.DEFAULT_LLM_PROVIDER}")
    
    # 检查输出目录
    output_dir = Path(config.OUTPUT_DIR)
    print(f"输出目录存在: {output_dir.exists()}")
    
    if not output_dir.exists():
        output_dir.mkdir(exist_ok=True)
        print(f"✅ 创建输出目录: {output_dir}")


def main():
    """运行所有测试"""
    print("🚀 Full Analysis 交互逻辑简化测试")
    print("=" * 80)
    
    try:
        # 测试配置
        test_configuration()
        
        # 测试现有报告检查功能
        test_check_existing_report()
        
        # 测试交互场景
        test_interaction_scenarios()
        
        # 测试执行流程逻辑
        test_execution_flow_logic()
        
        print("\n🎉 所有测试完成!")
        print("\n📋 测试总结:")
        print("✅ 现有报告检测功能正常")
        print("✅ 交互选项显示逻辑正确")
        print("✅ 执行流程分支清晰")
        print("✅ 配置验证通过")
        
        print("\n💡 实际使用方法:")
        print("1. 设置环境变量: EXECUTION_MODE=full_analysis")
        print("2. 运行: python main.py")
        print("3. 输入研究主题")
        print("4. 根据提示选择执行方式")
        
        print("\n🔧 主要改进:")
        print("✅ 添加了 _check_existing_research_report() 方法")
        print("✅ 添加了 _show_full_analysis_options() 交互方法")
        print("✅ 添加了 _run_innovation_only() 简化执行方法")
        print("✅ 修改了 run_research() 方法集成交互逻辑")
        print("✅ 更新了 main() 函数处理不同模式")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
