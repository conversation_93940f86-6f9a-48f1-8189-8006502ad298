#!/usr/bin/env python3
# test_llm_enhancements.py
#
# 测试LLM客户端增强功能

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from clients import LLMClient, OpenAIClient, OpenAICompatibleClient
from agents import PlannerAgent


def test_openai_compatible_client():
    """测试OpenAI兼容客户端"""
    print("=" * 60)
    print("测试OpenAI兼容客户端")
    print("=" * 60)
    
    try:
        # 检查是否有OpenAI兼容配置
        has_compatible_config = bool(getattr(config, 'OPENAI_COMPATIBLE_BASE_URL', None))
        print(f"OpenAI兼容配置可用: {has_compatible_config}")
        
        if has_compatible_config:
            print(f"兼容API端点: {config.OPENAI_COMPATIBLE_BASE_URL}")
            print(f"兼容模型: {config.OPENAI_COMPATIBLE_MODEL}")
            
            # 尝试创建客户端
            try:
                client = OpenAICompatibleClient()
                print("✅ OpenAI兼容客户端创建成功")
                
                # 测试连接（如果配置了真实端点）
                if "localhost" not in config.OPENAI_COMPATIBLE_BASE_URL:
                    connection_test = client.test_connection()
                    print(f"连接测试: {'✅ 成功' if connection_test else '❌ 失败'}")
                else:
                    print("⚠️  本地端点，跳过连接测试")
                    
            except Exception as e:
                print(f"❌ 客户端创建失败: {e}")
        else:
            print("⚠️  未配置OpenAI兼容端点，跳过测试")
        
        print("✅ OpenAI兼容客户端测试完成\n")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}\n")


def test_multi_provider_support():
    """测试多提供商支持"""
    print("=" * 60)
    print("测试多提供商支持")
    print("=" * 60)
    
    try:
        # 获取可用提供商
        available_providers = config.get_available_providers()
        print("可用的LLM提供商:")
        for provider, available in available_providers['llm'].items():
            status = "✅ 可用" if available else "❌ 不可用"
            print(f"  - {provider}: {status}")
        
        # 测试代理特定配置
        print("\n代理特定配置:")
        for agent_type in ['planner', 'synthesizer', 'writer']:
            agent_config = config.get_agent_config(agent_type)
            print(f"  - {agent_type}: {agent_config}")
        
        print("✅ 多提供商支持测试完成\n")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}\n")


def test_planner_ai_keywords():
    """测试PlannerAgent的AI关键词生成"""
    print("=" * 60)
    print("测试PlannerAgent AI关键词生成")
    print("=" * 60)
    
    try:
        # 创建模拟LLM客户端
        class MockLLMClient:
            def generate(self, prompt, **kwargs):
                class MockResponse:
                    def __init__(self):
                        # 模拟AI生成的关键词响应
                        if "keyword" in prompt.lower():
                            self.response = '''
                            ["deep learning", "neural networks", "machine learning", 
                             "artificial intelligence", "computational methods"]
                            '''
                        else:
                            # 模拟研究计划响应
                            self.response = '''
                            {
                                "sub_questions": [
                                    "What are the fundamental concepts of deep learning?",
                                    "How has deep learning evolved historically?",
                                    "What are current methodologies in deep learning?"
                                ],
                                "keyword_sets": {
                                    "sub_question_1": ["deep learning fundamentals", "neural network basics"],
                                    "sub_question_2": ["deep learning history", "AI evolution"],
                                    "sub_question_3": ["deep learning methods", "training algorithms"]
                                }
                            }
                            '''
                    
                    def is_successful(self):
                        return True
                
                return MockResponse()
        
        # 测试PlannerAgent
        planner = PlannerAgent(MockLLMClient())
        
        # 测试关键词生成方法
        print("测试AI关键词生成...")
        keywords = planner._generate_keywords_for_question(
            "What are the applications of deep learning?", 
            "Deep Learning in Science"
        )
        print(f"生成的关键词: {keywords}")
        print(f"关键词数量: {len(keywords)}")
        
        # 测试完整的研究计划生成
        print("\n测试完整研究计划生成...")
        research_query = planner.generate_research_plan("Deep Learning in Astrophysics")
        print(f"主题: {research_query.main_topic}")
        print(f"子问题数量: {len(research_query.sub_questions)}")
        print(f"关键词数量: {len(research_query.keywords)}")
        
        if hasattr(research_query, 'sub_question_keywords'):
            print(f"子问题关键词映射: {bool(research_query.sub_question_keywords)}")
        
        # 验证不再使用文本处理方法
        print("\n验证文本处理方法已弃用...")
        deprecated_keywords = planner._extract_words_from_text("test text")
        print(f"弃用方法返回: {deprecated_keywords} (应该为空列表)")
        
        print("✅ PlannerAgent AI关键词生成测试完成\n")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}\n")


def test_configuration_validation():
    """测试配置验证"""
    print("=" * 60)
    print("测试配置验证")
    print("=" * 60)
    
    try:
        # 测试配置验证
        print("运行配置验证...")
        
        try:
            config.validate_config()
            print("✅ 配置验证通过")
        except ValueError as e:
            print(f"⚠️  配置验证警告: {e}")
        
        # 显示配置摘要
        config_summary = config.get_config_summary()
        print(f"\n配置摘要:")
        print(f"  - 默认LLM提供商: {config_summary.get('llm_provider', 'N/A')}")
        print(f"  - API密钥配置状态: {config_summary.get('api_keys_configured', {})}")
        
        print("✅ 配置验证测试完成\n")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}\n")


def main():
    """运行所有测试"""
    print("🚀 测试LLM客户端增强功能")
    print("=" * 80)
    
    # 运行所有测试
    test_openai_compatible_client()
    test_multi_provider_support()
    test_planner_ai_keywords()
    test_configuration_validation()
    
    print("=" * 80)
    print("🎉 所有增强功能测试完成!")
    print("\n主要增强功能:")
    print("1. ✅ OpenAI兼容模式支持 - 支持本地部署和第三方兼容服务")
    print("2. ✅ 多模型代理配置 - 不同代理可使用不同AI模型")
    print("3. ✅ AI驱动关键词生成 - 完全依赖AI生成优化的学术搜索关键词")
    print("4. ✅ 弃用文本处理方法 - 移除简单的文本处理逻辑")
    print("\n配置示例:")
    print("# 使用OpenAI兼容模式")
    print("DEFAULT_LLM_PROVIDER=openai-compatible")
    print("OPENAI_COMPATIBLE_BASE_URL=http://localhost:8000/v1")
    print("OPENAI_COMPATIBLE_API_KEY=your-key")
    print("OPENAI_COMPATIBLE_MODEL=your-model")
    print("\n# 多代理配置")
    print("PLANNER_AGENT_PROVIDER=openai")
    print("SYNTHESIZER_AGENT_PROVIDER=gemini")
    print("WRITER_AGENT_PROVIDER=anthropic")


if __name__ == "__main__":
    main()
