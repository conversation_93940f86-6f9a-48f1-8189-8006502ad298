#!/usr/bin/env python3
# test_main_agent_initialization.py
#
# 测试main.py中agent初始化的修复

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_main_initialization():
    """测试main.py中的agent初始化"""
    print("🧪 Testing Main.py Agent Initialization Fix")
    print("=" * 70)
    
    try:
        from main import ResearchAssistant
        from config import config
        
        print("📋 Expected Agent Models:")
        print(f"   PlannerAgent: {config.PLANNER_AGENT_MODEL}")
        print(f"   SynthesizerAgent: {config.SYNTHESIZER_AGENT_MODEL}")
        print(f"   WriterAgent: {config.WRITER_AGENT_MODEL}")
        
        print(f"\n📋 Creating ResearchAssistant instance...")
        assistant = ResearchAssistant()
        
        # 检查每个agent的模型
        agents_info = [
            ("planner", assistant.planner, config.PLANNER_AGENT_MODEL),
            ("synthesizer", assistant.synthesizer, config.SYNTHESIZER_AGENT_MODEL),
            ("writer", assistant.writer, config.WRITER_AGENT_MODEL)
        ]
        
        all_correct = True
        
        for agent_name, agent, expected_model in agents_info:
            print(f"\n📋 Checking {agent_name.title()}Agent:")
            
            if hasattr(agent, 'llm_client') and agent.llm_client:
                actual_model = getattr(agent.llm_client, 'model', None)
                client_type = type(agent.llm_client).__name__
                
                print(f"   Client Type: {client_type}")
                print(f"   Expected Model: {expected_model}")
                print(f"   Actual Model: {actual_model}")
                
                if actual_model == expected_model:
                    print(f"   ✅ {agent_name.title()}Agent using correct model")
                else:
                    print(f"   ❌ {agent_name.title()}Agent using wrong model!")
                    all_correct = False
            else:
                print(f"   ❌ {agent_name.title()}Agent has no LLM client")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Main initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_shared_vs_dedicated_clients():
    """测试共享客户端 vs 专用客户端的区别"""
    print("\n🧪 Testing Shared vs Dedicated Client Behavior")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from clients.llm_client import LLMClient
        from config import config
        
        print("📋 Testing shared client behavior (OLD WAY):")
        
        # 创建共享客户端（旧方式）
        shared_client = LLMClient()
        shared_client_model = getattr(shared_client.client, 'model', None)
        print(f"   Shared client model: {shared_client_model}")
        
        # 使用共享客户端创建PlannerAgent
        planner_with_shared = PlannerAgent(shared_client)
        shared_planner_model = getattr(planner_with_shared.llm_client.client, 'model', None)
        print(f"   PlannerAgent with shared client model: {shared_planner_model}")
        
        print(f"\n📋 Testing dedicated client behavior (NEW WAY):")
        
        # 不传递客户端，让agent创建专用客户端（新方式）
        planner_with_dedicated = PlannerAgent()
        dedicated_planner_model = getattr(planner_with_dedicated.llm_client, 'model', None)
        print(f"   PlannerAgent with dedicated client model: {dedicated_planner_model}")
        
        expected_model = config.PLANNER_AGENT_MODEL
        print(f"   Expected PlannerAgent model: {expected_model}")
        
        # 比较结果
        print(f"\n📋 Comparison:")
        if shared_planner_model == expected_model:
            print(f"   ✅ Shared client approach works correctly")
        else:
            print(f"   ❌ Shared client approach uses wrong model: {shared_planner_model}")
        
        if dedicated_planner_model == expected_model:
            print(f"   ✅ Dedicated client approach works correctly")
        else:
            print(f"   ❌ Dedicated client approach uses wrong model: {dedicated_planner_model}")
        
        return dedicated_planner_model == expected_model
        
    except Exception as e:
        print(f"❌ Shared vs dedicated client test failed: {e}")
        return False


def test_agent_independence():
    """测试agent之间的独立性"""
    print("\n🧪 Testing Agent Independence")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        print("📋 Creating independent agents:")
        
        # 创建独立的agents
        planner = PlannerAgent()
        synthesizer = SynthesizerAgent()
        writer = WriterAgent()
        
        # 获取每个agent的模型
        planner_model = getattr(planner.llm_client, 'model', None)
        synthesizer_model = getattr(synthesizer.llm_client, 'model', None)
        writer_model = getattr(writer.llm_client, 'model', None)
        
        print(f"   PlannerAgent model: {planner_model}")
        print(f"   SynthesizerAgent model: {synthesizer_model}")
        print(f"   WriterAgent model: {writer_model}")
        
        # 检查期望值
        expected_planner = config.PLANNER_AGENT_MODEL
        expected_synthesizer = config.SYNTHESIZER_AGENT_MODEL
        expected_writer = config.WRITER_AGENT_MODEL
        
        print(f"\n📋 Expected models:")
        print(f"   PlannerAgent: {expected_planner}")
        print(f"   SynthesizerAgent: {expected_synthesizer}")
        print(f"   WriterAgent: {expected_writer}")
        
        # 验证独立性
        all_correct = True
        
        if planner_model == expected_planner:
            print(f"   ✅ PlannerAgent model correct")
        else:
            print(f"   ❌ PlannerAgent model incorrect")
            all_correct = False
        
        if synthesizer_model == expected_synthesizer:
            print(f"   ✅ SynthesizerAgent model correct")
        else:
            print(f"   ❌ SynthesizerAgent model incorrect")
            all_correct = False
        
        if writer_model == expected_writer:
            print(f"   ✅ WriterAgent model correct")
        else:
            print(f"   ❌ WriterAgent model incorrect")
            all_correct = False
        
        # 检查是否有不同的模型（证明独立性）
        models = [planner_model, synthesizer_model, writer_model]
        unique_models = set(models)
        
        print(f"\n📋 Independence check:")
        print(f"   Total models: {len(models)}")
        print(f"   Unique models: {len(unique_models)}")
        
        if len(unique_models) > 1:
            print(f"   ✅ Agents are using different models (independent)")
        else:
            print(f"   ⚠️  All agents using same model (may be correct if configured that way)")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Agent independence test failed: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 Main.py Agent Initialization Fix Testing")
    print("=" * 80)
    
    tests = [
        ("Main Initialization", test_main_initialization),
        ("Shared vs Dedicated Clients", test_shared_vs_dedicated_clients),
        ("Agent Independence", test_agent_independence),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Main.py agent initialization fix is working correctly.")
        print("✅ Each agent now creates its own dedicated LLM client")
        print("✅ Agents use their specifically configured models")
        print("✅ No more shared client interference")
    else:
        print("\n❌ Some tests failed. Check the agent initialization logic.")
    
    print("\n💡 Key Fix Applied:")
    print("✅ Modified main.py to not pass shared LLM client to agents")
    print("✅ Agents now create their own dedicated clients with correct models")
    print("✅ Eliminated the root cause of model configuration override")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
