#!/usr/bin/env python3
# test_optimization_tasks.py
#
# 测试三个优化任务的实现

import sys
import os
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_task1_research_plan_storage():
    """测试任务1：研究计划持久化存储"""
    print("🧪 Testing Task 1: Research Plan Persistent Storage")
    print("=" * 70)
    
    try:
        from utils.research_plan_storage import ResearchPlanStorage
        
        # 创建存储管理器
        storage = ResearchPlanStorage("test_research_plans.json")
        
        print("📋 Testing research plan storage functionality:")
        
        # 测试保存研究计划
        test_topic = "Machine Learning in Astrophysics Test"
        test_sub_questions = [
            "How can machine learning improve astronomical data analysis?",
            "What are the applications of deep learning in galaxy classification?",
            "How can AI assist in exoplanet detection?"
        ]
        test_keywords = ["machine learning", "astrophysics", "deep learning", "AI"]
        test_sub_keywords = {
            "sub_question_1": ["data analysis", "algorithms", "automation"],
            "sub_question_2": ["galaxy", "classification", "morphology"],
            "sub_question_3": ["exoplanet", "detection", "transit"]
        }
        
        # 保存计划
        plan_id = storage.save_research_plan(
            main_topic=test_topic,
            sub_questions=test_sub_questions,
            general_keywords=test_keywords,
            sub_question_keywords=test_sub_keywords
        )
        
        print(f"   ✅ Research plan saved with ID: {plan_id}")
        
        # 测试加载研究计划
        plans = storage.load_research_plans()
        print(f"   ✅ Loaded {len(plans)} research plans")
        
        # 测试获取特定计划
        retrieved_plan = storage.get_research_plan(plan_id)
        if retrieved_plan:
            print(f"   ✅ Retrieved plan: {retrieved_plan['main_topic']}")
        else:
            print(f"   ❌ Failed to retrieve plan with ID: {plan_id}")
            return False
        
        # 测试计划摘要
        summary = storage.get_plan_summary(retrieved_plan)
        print(f"   ✅ Plan summary: {summary[:80]}...")
        
        # 测试存储统计
        stats = storage.get_storage_stats()
        print(f"   ✅ Storage stats: {stats['total_plans']} plans, {stats['total_usage']} total usage")
        
        # 清理测试文件
        if Path("test_research_plans.json").exists():
            Path("test_research_plans.json").unlink()
            print(f"   ✅ Test file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Task 1 test failed: {e}")
        return False


def test_task2_gui_integration():
    """测试任务2：GUI集成（模拟测试）"""
    print("\n🧪 Testing Task 2: GUI Integration (Simulation)")
    print("=" * 70)
    
    try:
        from gui.main_dialog import MainDialog
        from utils.research_plan_storage import ResearchPlanStorage
        
        print("📋 Testing GUI components integration:")
        
        # 测试MainDialog初始化
        dialog = MainDialog()
        print(f"   ✅ MainDialog initialized with storage: {type(dialog.storage).__name__}")
        
        # 测试存储管理器集成
        if hasattr(dialog, 'storage') and dialog.storage:
            print(f"   ✅ Storage manager integrated in MainDialog")
        else:
            print(f"   ❌ Storage manager not integrated in MainDialog")
            return False
        
        # 测试方法存在性
        required_methods = [
            '_create_history_section',
            '_on_use_history_changed',
            '_on_history_select',
            '_refresh_history_plans',
            '_delete_selected_plan',
            '_preview_selected_plan',
            '_show_plan_preview',
            '_format_plan_content'
        ]
        
        for method_name in required_methods:
            if hasattr(dialog, method_name):
                print(f"   ✅ Method exists: {method_name}")
            else:
                print(f"   ❌ Method missing: {method_name}")
                return False
        
        # 测试PlannerReviewDialog集成
        from gui.planner_review_dialog import PlannerReviewDialog
        
        review_dialog = PlannerReviewDialog(
            topic="Test Topic",
            sub_questions=["Test question?"],
            keywords=["test", "keyword"]
        )
        
        if hasattr(review_dialog, 'storage') and review_dialog.storage:
            print(f"   ✅ Storage manager integrated in PlannerReviewDialog")
        else:
            print(f"   ❌ Storage manager not integrated in PlannerReviewDialog")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Task 2 test failed: {e}")
        return False


def test_task3_code_cleanup():
    """测试任务3：代码清理"""
    print("\n🧪 Testing Task 3: Code Cleanup")
    print("=" * 70)
    
    try:
        from clients.ads_client import ADSClient
        
        print("📋 Testing deprecated method removal:")
        
        # 创建ADS客户端实例
        try:
            ads_client = ADSClient()
        except ValueError:
            # 如果没有API token，创建模拟实例
            ads_client = ADSClient.__new__(ADSClient)
            ads_client.api_token = "test_token"
            ads_client.base_url = "https://api.adsabs.harvard.edu/v1"
            ads_client.logger = None
        
        # 检查已移除的向后兼容方法
        deprecated_methods = [
            "search_papers_enhanced",
            "_apply_two_tier_sorting"
        ]
        
        for method_name in deprecated_methods:
            if hasattr(ads_client, method_name):
                print(f"   ❌ Deprecated method still exists: {method_name}")
                return False
            else:
                print(f"   ✅ Deprecated method removed: {method_name}")
        
        # 检查保留的核心方法
        core_methods = [
            "search_papers_for_ranking",
            "search_papers",
            "_retrieve_papers_bulk",
            "_retrieve_papers_bulk_optimized",
            "_convert_to_paper",
            "_convert_to_paper_enhanced",
            "_deduplicate_papers",
            "_basic_deduplicate_papers",
            "_normalize_title",
            "_normalize_title_basic"
        ]
        
        for method_name in core_methods:
            if hasattr(ads_client, method_name):
                print(f"   ✅ Core method preserved: {method_name}")
            else:
                print(f"   ❌ Core method missing: {method_name}")
                return False
        
        # 检查导入清理
        import clients.ads_client as ads_module
        import inspect
        
        # 检查re模块是否正确导入
        source = inspect.getsource(ads_module)
        if "import re" in source and source.count("import re") == 1:
            print(f"   ✅ Import cleanup: re module properly imported once")
        else:
            print(f"   ⚠️  Import cleanup: re module import may need attention")
        
        return True
        
    except Exception as e:
        print(f"❌ Task 3 test failed: {e}")
        return False


def test_main_workflow_integration():
    """测试主工作流集成"""
    print("\n🧪 Testing Main Workflow Integration")
    print("=" * 70)
    
    try:
        # 测试main.py中的修改
        print("📋 Testing main.py integration:")
        
        # 检查show_main_dialog函数签名
        from gui.main_dialog import show_main_dialog
        
        # 这应该返回三元组而不是二元组
        print(f"   ✅ show_main_dialog function available")
        
        # 检查main.py中的导入
        import main
        
        # 检查ResearchAssistant类的run_research方法签名
        assistant = main.ResearchAssistant()
        
        # 检查方法签名（通过inspect）
        import inspect
        sig = inspect.signature(assistant.run_research)
        params = list(sig.parameters.keys())
        
        if 'selected_plan' in params:
            print(f"   ✅ run_research method supports selected_plan parameter")
        else:
            print(f"   ❌ run_research method missing selected_plan parameter")
            return False
        
        print(f"   ✅ Main workflow integration verified")
        
        return True
        
    except Exception as e:
        print(f"❌ Main workflow integration test failed: {e}")
        return False


def test_system_compatibility():
    """测试系统兼容性"""
    print("\n🧪 Testing System Compatibility")
    print("=" * 70)
    
    try:
        print("📋 Testing system-wide compatibility:")
        
        # 测试所有核心组件可以正常导入
        components = [
            ("ResearchPlanStorage", "utils.research_plan_storage"),
            ("MainDialog", "gui.main_dialog"),
            ("PlannerReviewDialog", "gui.planner_review_dialog"),
            ("ADSClient", "clients.ads_client"),
            ("PaperRankingAgent", "agents.paper_ranking_agent"),
            ("ResearchAssistant", "main")
        ]
        
        for component_name, module_path in components:
            try:
                module = __import__(module_path, fromlist=[component_name])
                component_class = getattr(module, component_name)
                print(f"   ✅ {component_name} import successful")
            except Exception as e:
                print(f"   ❌ {component_name} import failed: {e}")
                return False
        
        # 测试配置完整性
        from config import config
        
        required_configs = [
            "PAPER_RANKING_RELEVANCE_WEIGHT",
            "PAPER_RANKING_IMPORTANCE_WEIGHT", 
            "PAPER_RANKING_DEFAULT_TOP_N",
            "ADS_PAPERS_PER_SUBQUESTION"
        ]
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                value = getattr(config, config_name)
                print(f"   ✅ Config {config_name}: {value}")
            else:
                print(f"   ❌ Config missing: {config_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ System compatibility test failed: {e}")
        return False


def main():
    """运行所有优化任务测试"""
    print("🚀 AI Research Assistant Optimization Tasks Testing")
    print("=" * 90)
    print("Testing implementation of three optimization tasks")
    
    tests = [
        ("Task 1: Research Plan Storage", test_task1_research_plan_storage),
        ("Task 2: GUI Integration", test_task2_gui_integration),
        ("Task 3: Code Cleanup", test_task3_code_cleanup),
        ("Main Workflow Integration", test_main_workflow_integration),
        ("System Compatibility", test_system_compatibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # 生成测试结果总结
    print("\n🎉 OPTIMIZATION TASKS TESTING RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL OPTIMIZATION TASKS COMPLETED SUCCESSFULLY!")
        print("✅ Task 1: Research plan persistent storage implemented")
        print("✅ Task 2: Smart research plan reuse mechanism implemented")
        print("✅ Task 3: System code cleanup and refactoring completed")
        print("✅ All integrations working properly")
        print("✅ System ready for enhanced research workflow")
    else:
        print("\n⚠️  Some optimization tasks need attention")
        print("🔧 Review failed components before deployment")
    
    print("\n💡 Optimization Features Summary:")
    print("   💾 Persistent storage: Research plans saved to JSON")
    print("   🔄 Smart reuse: Historical plans available in GUI")
    print("   🧹 Clean codebase: Deprecated methods removed")
    print("   🎯 Enhanced workflow: Skip PlannerAgent for historical plans")
    print("   🔗 Seamless integration: All components work together")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
