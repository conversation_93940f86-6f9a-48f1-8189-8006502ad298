#!/usr/bin/env python3
# test_paper_ranking_agent.py
#
# PaperRankingAgent集成测试

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_paper_ranking_agent_initialization():
    """测试PaperRankingAgent初始化"""
    print("🧪 Testing PaperRankingAgent Initialization")
    print("=" * 70)
    
    try:
        from agents.paper_ranking_agent import PaperRankingAgent
        from config import config
        
        print("📋 Creating PaperRankingAgent:")
        
        # 测试Agent创建
        ranking_agent = PaperRankingAgent()
        
        # 检查初始化
        if hasattr(ranking_agent, 'llm_client') and ranking_agent.llm_client:
            model = getattr(ranking_agent.llm_client, 'model', None)
            expected_model = config.PAPER_RANKING_AGENT_MODEL
            
            print(f"   ✅ Agent created successfully")
            print(f"   ✅ LLM Client: {type(ranking_agent.llm_client).__name__}")
            print(f"   ✅ Expected Model: {expected_model}")
            print(f"   ✅ Actual Model: {model}")
            
            if model == expected_model:
                print(f"   ✅ Model configuration CORRECT")
            else:
                print(f"   ❌ Model configuration INCORRECT")
                return False
        else:
            print(f"   ❌ LLM client not properly initialized")
            return False
        
        # 检查配置
        agent_config = config.get_agent_config("paper_ranking")
        print(f"\n📋 Checking configuration:")
        print(f"   ✅ Provider: {agent_config.get('provider')}")
        print(f"   ✅ Temperature: {agent_config.get('temperature')}")
        print(f"   ✅ Max Tokens: {agent_config.get('max_tokens')}")
        
        # 检查关键方法
        key_methods = [
            'rank_and_filter_papers',
            '_assess_relevance',
            '_assess_importance',
            '_calculate_composite_scores'
        ]
        
        print(f"\n📋 Checking key methods:")
        for method_name in key_methods:
            if hasattr(ranking_agent, method_name):
                print(f"   ✅ Method exists: {method_name}")
            else:
                print(f"   ❌ Method missing: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ PaperRankingAgent initialization test failed: {e}")
        return False


def test_paper_ranking_functionality():
    """测试论文排序功能"""
    print("\n🧪 Testing Paper Ranking Functionality")
    print("=" * 70)
    
    try:
        from agents.paper_ranking_agent import PaperRankingAgent
        from models.paper import Paper
        
        # 创建测试论文数据
        test_papers = [
            Paper(
                title="Machine Learning Applications in Astrophysics: A Comprehensive Review",
                abstract="This paper reviews machine learning applications in astrophysics...",
                authors=["Smith, J.", "Johnson, A."],
                publication_date="2023-01-15",
                citation_count=45,
                url="https://example.com/paper1"
            ),
            Paper(
                title="Deep Neural Networks for Galaxy Classification",
                abstract="We present a deep learning approach for galaxy classification...",
                authors=["Brown, K.", "Davis, L."],
                publication_date="2022-06-20",
                citation_count=23,
                url="https://example.com/paper2"
            ),
            Paper(
                title="Statistical Methods in Cosmology",
                abstract="This work discusses statistical methods used in cosmological studies...",
                authors=["Wilson, M."],
                publication_date="2021-03-10",
                citation_count=67,
                url="https://example.com/paper3"
            ),
            Paper(
                title="Quantum Computing Applications in Physics",
                abstract="We explore quantum computing applications in various physics domains...",
                authors=["Taylor, R.", "Anderson, S."],
                publication_date="2023-08-05",
                citation_count=12,
                url="https://example.com/paper4"
            ),
            Paper(
                title="Automated Data Analysis in Astronomy",
                abstract="This paper presents automated methods for astronomical data analysis...",
                authors=["Clark, P.", "Lewis, H."],
                publication_date="2022-11-30",
                citation_count=34,
                url="https://example.com/paper5"
            )
        ]
        
        print(f"📋 Created {len(test_papers)} test papers")
        
        # 创建PaperRankingAgent
        ranking_agent = PaperRankingAgent()
        
        # 测试子问题
        sub_question = "What are the current machine learning applications in astrophysics and astronomy?"
        
        print(f"📋 Testing ranking for sub-question: '{sub_question}'")
        
        # 执行排序（使用较小的top_n进行测试）
        ranked_papers, stats = ranking_agent.rank_and_filter_papers(
            sub_question=sub_question,
            papers=test_papers,
            top_n=3
        )
        
        print(f"\n📊 Ranking Results:")
        print(f"   Total papers: {stats['total_papers']}")
        print(f"   Filtered papers: {stats['filtered_papers']}")
        print(f"   Ranking method: {stats['ranking_method']}")
        
        if ranked_papers:
            print(f"\n📋 Top ranked papers:")
            for i, paper in enumerate(ranked_papers, 1):
                composite_score = getattr(paper, 'composite_score', 0)
                relevance_score = getattr(paper, 'relevance_score', 0)
                importance_score = getattr(paper, 'importance_score', 0)
                
                print(f"   {i}. {paper.title[:60]}...")
                print(f"      Composite: {composite_score:.3f}, Relevance: {relevance_score:.3f}, Importance: {importance_score:.3f}")
        
        # 验证结果
        if len(ranked_papers) == 3 and stats['total_papers'] == 5:
            print(f"\n✅ Paper ranking functionality test PASSED")
            return True
        else:
            print(f"\n❌ Paper ranking functionality test FAILED")
            return False
        
    except Exception as e:
        print(f"❌ Paper ranking functionality test failed: {e}")
        return False


def test_importance_scoring():
    """测试重要性评分功能"""
    print("\n🧪 Testing Importance Scoring")
    print("=" * 70)
    
    try:
        from agents.paper_ranking_agent import PaperRankingAgent
        
        ranking_agent = PaperRankingAgent()
        
        # 测试引用数评分
        citation_tests = [
            (0, "No citations"),
            (5, "Low citations"),
            (25, "Medium citations"),
            (75, "High citations"),
            (150, "Very high citations"),
            (1000, "Extremely high citations")
        ]
        
        print("📋 Testing citation scoring:")
        for citation_count, description in citation_tests:
            score = ranking_agent._calculate_citation_score(citation_count)
            print(f"   {description} ({citation_count}): {score:.2f}")
        
        # 测试时效性评分
        recency_tests = [
            ("2024-01-01", "Very recent"),
            ("2022-06-15", "Recent"),
            ("2020-03-10", "Moderately recent"),
            ("2015-08-20", "Older"),
            ("2010-12-05", "Old"),
            ("", "Unknown date")
        ]
        
        print(f"\n📋 Testing recency scoring:")
        current_year = 2024
        for date, description in recency_tests:
            score = ranking_agent._calculate_recency_score(date, current_year)
            print(f"   {description} ({date}): {score:.2f}")
        
        # 测试期刊评分
        journal_tests = [
            ("Nature", "Top journal"),
            ("Physical Review Letters", "High impact"),
            ("Journal of Machine Learning Research", "Good journal"),
            ("International Conference on Learning", "Conference"),
            ("Unknown Journal", "Unknown"),
            ("", "No journal")
        ]
        
        print(f"\n📋 Testing journal scoring:")
        for journal, description in journal_tests:
            score = ranking_agent._calculate_journal_score(journal)
            print(f"   {description} ({journal}): {score:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Importance scoring test failed: {e}")
        return False


def test_config_integration():
    """测试配置集成"""
    print("\n🧪 Testing Configuration Integration")
    print("=" * 70)
    
    try:
        from config import config
        
        print("📋 Testing PaperRankingAgent configuration:")
        
        # 检查环境变量配置
        paper_ranking_model = config.PAPER_RANKING_AGENT_MODEL
        paper_ranking_provider = config.PAPER_RANKING_AGENT_PROVIDER
        paper_ranking_temp = config.PAPER_RANKING_TEMPERATURE
        paper_ranking_tokens = config.PAPER_RANKING_MAX_TOKENS
        
        print(f"   ✅ Model: {paper_ranking_model}")
        print(f"   ✅ Provider: {paper_ranking_provider}")
        print(f"   ✅ Temperature: {paper_ranking_temp}")
        print(f"   ✅ Max Tokens: {paper_ranking_tokens}")
        
        # 检查get_agent_config方法
        agent_config = config.get_agent_config("paper_ranking")
        
        expected_keys = ["provider", "model", "temperature", "max_tokens"]
        for key in expected_keys:
            if key in agent_config:
                print(f"   ✅ Config key '{key}': {agent_config[key]}")
            else:
                print(f"   ❌ Missing config key: {key}")
                return False
        
        # 验证配置值
        if (agent_config["model"] == paper_ranking_model and
            agent_config["provider"] == paper_ranking_provider and
            agent_config["temperature"] == paper_ranking_temp and
            agent_config["max_tokens"] == paper_ranking_tokens):
            print(f"   ✅ Configuration consistency VERIFIED")
            return True
        else:
            print(f"   ❌ Configuration consistency FAILED")
            return False
        
    except Exception as e:
        print(f"❌ Configuration integration test failed: {e}")
        return False


def test_workflow_integration():
    """测试工作流集成"""
    print("\n🧪 Testing Workflow Integration")
    print("=" * 70)
    
    try:
        # 测试与现有Agent的兼容性
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from agents.paper_ranking_agent import PaperRankingAgent
        
        print("📋 Testing agent compatibility:")
        
        # 创建所有agents
        planner = PlannerAgent()
        ranking = PaperRankingAgent()
        synthesizer = SynthesizerAgent()
        writer = WriterAgent()
        
        print(f"   ✅ PlannerAgent: {type(planner).__name__}")
        print(f"   ✅ PaperRankingAgent: {type(ranking).__name__}")
        print(f"   ✅ SynthesizerAgent: {type(synthesizer).__name__}")
        print(f"   ✅ WriterAgent: {type(writer).__name__}")
        
        # 检查模型配置
        models = {
            "Planner": getattr(planner.llm_client, 'model', None),
            "Ranking": getattr(ranking.llm_client, 'model', None),
            "Synthesizer": getattr(synthesizer.llm_client, 'model', None),
            "Writer": getattr(writer.llm_client, 'model', None)
        }
        
        print(f"\n📋 Agent model configuration:")
        for agent_name, model in models.items():
            print(f"   ✅ {agent_name}: {model}")
        
        # 验证新的工作流顺序
        workflow_order = ["PlannerAgent", "ADS Search", "PaperRankingAgent", "SynthesizerAgent", "WriterAgent"]
        print(f"\n📋 New workflow order:")
        for i, step in enumerate(workflow_order, 1):
            print(f"   {i}. {step}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow integration test failed: {e}")
        return False


def main():
    """运行所有PaperRankingAgent测试"""
    print("🚀 PaperRankingAgent Integration Testing")
    print("=" * 90)
    print("Testing the new PaperRankingAgent implementation and integration")
    
    tests = [
        ("PaperRankingAgent Initialization", test_paper_ranking_agent_initialization),
        ("Paper Ranking Functionality", test_paper_ranking_functionality),
        ("Importance Scoring", test_importance_scoring),
        ("Configuration Integration", test_config_integration),
        ("Workflow Integration", test_workflow_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # 生成测试结果总结
    print("\n🎉 PAPER RANKING AGENT TESTING RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL PAPER RANKING AGENT TESTS PASSED!")
        print("✅ PaperRankingAgent is properly implemented")
        print("✅ Configuration integration is working")
        print("✅ Ranking functionality is operational")
        print("✅ Workflow integration is compatible")
        print("✅ Ready for production deployment")
    else:
        print("\n⚠️  Some tests failed")
        print("🔧 Review failed components before deployment")
    
    print("\n💡 PaperRankingAgent Features:")
    print("   🎯 AI-powered relevance assessment")
    print("   📊 Multi-factor importance scoring")
    print("   🔄 Intelligent paper filtering (100 → 30)")
    print("   📈 Enhanced research quality and efficiency")
    print("   🔗 Seamless workflow integration")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
