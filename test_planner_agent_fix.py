#!/usr/bin/env python3
# test_planner_agent_fix.py
#
# 测试PlannerAgent的"too many values to unpack"修复

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_planner_agent_return_values():
    """测试PlannerAgent确认方法的返回值"""
    print("🧪 Testing PlannerAgent Return Values Fix")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from models import ResearchQuery
        
        # 创建PlannerAgent
        planner = PlannerAgent()
        
        # 创建测试查询
        query = ResearchQuery(main_topic="Test Topic")
        query.sub_questions = [
            "What are the main aspects?",
            "What are the challenges?"
        ]
        query.keywords = ["keyword1", "keyword2", "keyword3"]
        query.sub_question_keywords = {
            "sub_question_1": ["keyword1", "keyword2"],
            "sub_question_2": ["keyword2", "keyword3"]
        }
        
        print("📋 Testing console confirmation return values:")
        
        # 测试控制台确认方法的返回值结构
        try:
            # 我们不能直接调用这个方法因为它需要用户输入
            # 但我们可以检查方法签名
            import inspect
            
            console_method = planner._show_console_confirmation
            gui_method = planner._show_gui_confirmation
            
            console_sig = inspect.signature(console_method)
            gui_sig = inspect.signature(gui_method)
            
            print(f"   Console method signature: {console_sig}")
            print(f"   GUI method signature: {gui_sig}")
            
            # 检查返回类型注解
            console_return = console_method.__annotations__.get('return', 'No annotation')
            gui_return = gui_method.__annotations__.get('return', 'No annotation')
            
            print(f"   Console return type: {console_return}")
            print(f"   GUI return type: {gui_return}")
            
            # 两个方法都应该返回4个值的元组
            expected_return = "Optional[Tuple[str, List[str], List[str], Dict[str, List[str]]]]"
            
            if str(console_return) == expected_return and str(gui_return) == expected_return:
                print("   ✅ Both methods have correct return type annotations")
                return True
            else:
                print("   ❌ Return type annotations don't match expected format")
                return False
                
        except Exception as e:
            print(f"   ❌ Error checking method signatures: {e}")
            return False
        
    except Exception as e:
        print(f"❌ PlannerAgent return values test failed: {e}")
        return False


def test_unpack_logic():
    """测试解包逻辑修复"""
    print("\n🧪 Testing Unpack Logic Fix")
    print("=" * 70)
    
    try:
        # 模拟确认方法的返回值
        mock_result = ('confirm', ['question1', 'question2'], ['keyword1', 'keyword2'], {'sub_question_1': ['keyword1']})
        
        print("📋 Testing 4-value unpacking:")
        print(f"   Mock result: {mock_result}")
        
        # 测试新的解包逻辑
        try:
            action, sub_questions, keywords, sub_question_keywords = mock_result
            
            print(f"   ✅ Unpacking successful:")
            print(f"     action: {action}")
            print(f"     sub_questions: {sub_questions}")
            print(f"     keywords: {keywords}")
            print(f"     sub_question_keywords: {sub_question_keywords}")
            
            return True
            
        except ValueError as e:
            print(f"   ❌ Unpacking failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Unpack logic test failed: {e}")
        return False


def test_env_comment_update():
    """测试.env文件注释更新"""
    print("\n🧪 Testing .env Comment Update")
    print("=" * 70)
    
    try:
        env_file = ".env"
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("📋 Checking .env file comments:")
            
            # 检查是否还有过时的注释
            if "留空使用默认模型" in content:
                print("   ❌ Found outdated comment: '留空使用默认模型'")
                return False
            elif "必须明确指定，不能为空" in content:
                print("   ✅ Found updated comment: '必须明确指定，不能为空'")
                return True
            else:
                print("   ⚠️  Comment not found, checking line 36...")
                
                lines = content.split('\n')
                if len(lines) > 35:  # 第36行 (0-based index 35)
                    line_36 = lines[35]
                    print(f"   Line 36: {line_36}")
                    
                    if "必须明确指定" in line_36:
                        print("   ✅ Comment updated correctly")
                        return True
                    else:
                        print("   ❌ Comment not updated")
                        return False
                else:
                    print("   ❌ File too short")
                    return False
        else:
            print("   ❌ .env file not found")
            return False
        
    except Exception as e:
        print(f"❌ .env comment test failed: {e}")
        return False


def test_planner_agent_integration():
    """测试PlannerAgent集成"""
    print("\n🧪 Testing PlannerAgent Integration")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        
        print("📋 Creating PlannerAgent instance:")
        planner = PlannerAgent()
        
        if hasattr(planner, 'llm_client') and planner.llm_client:
            model = getattr(planner.llm_client, 'model', None)
            print(f"   ✅ PlannerAgent created successfully")
            print(f"   ✅ Using model: {model}")
            
            # 检查方法是否存在
            methods_to_check = [
                'generate_research_plan',
                'generate_research_plan_with_confirmation',
                '_show_gui_confirmation',
                '_show_console_confirmation'
            ]
            
            all_methods_exist = True
            for method_name in methods_to_check:
                if hasattr(planner, method_name):
                    print(f"   ✅ Method exists: {method_name}")
                else:
                    print(f"   ❌ Method missing: {method_name}")
                    all_methods_exist = False
            
            return all_methods_exist
        else:
            print("   ❌ PlannerAgent LLM client not initialized")
            return False
        
    except Exception as e:
        print(f"❌ PlannerAgent integration test failed: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 PlannerAgent Fix Testing")
    print("=" * 80)
    print("Testing fixes for:")
    print("1. 'too many values to unpack (expected 3)' error")
    print("2. .env file comment update")
    
    tests = [
        ("PlannerAgent Return Values", test_planner_agent_return_values),
        ("Unpack Logic Fix", test_unpack_logic),
        (".env Comment Update", test_env_comment_update),
        ("PlannerAgent Integration", test_planner_agent_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Fixes are working correctly.")
        print("✅ PlannerAgent unpacking error fixed")
        print("✅ .env comment updated to reflect strict mode")
        print("✅ System ready for production use")
    else:
        print("\n❌ Some tests failed. Check the fixes.")
    
    print("\n💡 Fixes Applied:")
    print("1. ✅ Updated PlannerAgent to unpack 4 values instead of 3")
    print("2. ✅ Updated .env comment: '留空使用默认模型' → '必须明确指定，不能为空'")
    print("3. ✅ Simplified sub_question_keywords assignment logic")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
