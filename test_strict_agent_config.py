#!/usr/bin/env python3
# test_strict_agent_config.py
#
# 测试严格的agent模型配置（不使用默认模型回退）

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_current_agent_configs():
    """测试当前agent配置（应该都有明确的模型）"""
    print("🧪 Testing Current Agent Configurations (Should All Pass)")
    print("=" * 70)
    
    try:
        from config import config
        
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 Testing {agent.title()}Agent:")
            
            # 获取agent配置
            agent_config = config.get_agent_config(agent)
            model = agent_config.get("model")
            provider = agent_config.get("provider")
            
            print(f"   Provider: {provider}")
            print(f"   Model: {model}")
            
            if model:
                print(f"   ✅ Agent has explicit model configuration")
            else:
                print(f"   ❌ Agent model is empty - this will now cause an error")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Agent config test failed: {e}")
        return False


def test_llm_client_creation():
    """测试LLM客户端创建（应该只使用明确配置的模型）"""
    print("\n🧪 Testing LLM Client Creation (Strict Mode)")
    print("=" * 70)
    
    try:
        from clients.llm_client import LLMClient
        from config import config
        
        base_client = LLMClient()
        agents = ["planner", "synthesizer", "writer"]
        all_correct = True
        
        for agent in agents:
            print(f"\n📋 Creating client for {agent.title()}Agent:")
            
            try:
                # 尝试创建客户端
                agent_client = base_client.create_client_for_agent(agent)
                
                if agent_client:
                    actual_model = getattr(agent_client, 'model', None)
                    expected_model = getattr(config, f"{agent.upper()}_AGENT_MODEL")
                    
                    print(f"   Expected Model: {expected_model}")
                    print(f"   Actual Model: {actual_model}")
                    
                    if actual_model == expected_model:
                        print(f"   ✅ Client created with correct explicit model")
                    else:
                        print(f"   ❌ Model mismatch")
                        all_correct = False
                else:
                    print(f"   ❌ Failed to create client")
                    all_correct = False
                    
            except ValueError as e:
                print(f"   ❌ ValueError (expected for empty models): {e}")
                all_correct = False
            except Exception as e:
                print(f"   ❌ Unexpected error: {e}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ LLM client creation test failed: {e}")
        return False


def test_agent_instances():
    """测试agent实例创建"""
    print("\n🧪 Testing Agent Instance Creation (Strict Mode)")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        agents_info = [
            ("planner", PlannerAgent, config.PLANNER_AGENT_MODEL),
            ("synthesizer", SynthesizerAgent, config.SYNTHESIZER_AGENT_MODEL),
            ("writer", WriterAgent, config.WRITER_AGENT_MODEL)
        ]
        
        all_correct = True
        
        for agent_name, agent_class, expected_model in agents_info:
            print(f"\n📋 Testing {agent_name.title()}Agent Instance:")
            
            try:
                # 创建agent实例
                agent = agent_class()
                
                if hasattr(agent, 'llm_client') and agent.llm_client:
                    actual_model = getattr(agent.llm_client, 'model', None)
                    
                    print(f"   Expected Model: {expected_model}")
                    print(f"   Actual Model: {actual_model}")
                    
                    if actual_model == expected_model:
                        print(f"   ✅ Agent instance using correct explicit model")
                    else:
                        print(f"   ❌ Model mismatch")
                        all_correct = False
                else:
                    print(f"   ❌ Agent has no LLM client")
                    all_correct = False
                    
            except ValueError as e:
                print(f"   ❌ ValueError (expected for empty models): {e}")
                all_correct = False
            except Exception as e:
                print(f"   ❌ Unexpected error: {e}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Agent instance test failed: {e}")
        return False


def test_empty_model_handling():
    """测试空模型的处理（应该抛出错误）"""
    print("\n🧪 Testing Empty Model Handling (Should Raise Errors)")
    print("=" * 70)
    
    try:
        from clients.llm_client import LLMClient
        
        base_client = LLMClient()
        
        print("📋 Testing with simulated empty model configuration:")
        
        # 模拟一个空模型的agent配置
        try:
            # 这应该会抛出ValueError，因为我们不再允许空模型
            # 我们需要直接测试create_client_for_agent的逻辑
            
            # 创建一个临时的配置来测试
            import config as config_module
            
            # 保存原始配置
            original_get_agent_config = config_module.config.get_agent_config
            
            # 创建一个返回空模型的临时配置函数
            def mock_get_agent_config(agent_type):
                return {
                    "provider": "openai-compatible",
                    "model": "",  # 空模型
                    "temperature": 0.7,
                    "max_tokens": 1000
                }
            
            # 临时替换配置函数
            config_module.config.get_agent_config = mock_get_agent_config
            
            try:
                # 这应该抛出ValueError
                client = base_client.create_client_for_agent("test_agent")
                print("   ❌ Expected ValueError was not raised")
                return False
            except ValueError as e:
                print(f"   ✅ Correctly raised ValueError: {e}")
                return True
            finally:
                # 恢复原始配置
                config_module.config.get_agent_config = original_get_agent_config
                
        except Exception as e:
            print(f"   ❌ Unexpected error in empty model test: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Empty model handling test failed: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 Strict Agent Model Configuration Testing")
    print("=" * 80)
    print("Testing the new strict mode: only use explicitly configured agent models")
    print("No fallback to default models when agent model is empty")
    
    tests = [
        ("Current Agent Configurations", test_current_agent_configs),
        ("LLM Client Creation", test_llm_client_creation),
        ("Agent Instance Creation", test_agent_instances),
        ("Empty Model Handling", test_empty_model_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Strict agent model configuration is working correctly.")
        print("✅ Only explicitly configured agent models are used")
        print("✅ No fallback to default models")
        print("✅ Empty models correctly raise errors")
    else:
        print("\n❌ Some tests failed. Check the configuration.")
    
    print("\n💡 Key Changes Made:")
    print("✅ Removed fallback logic in config.get_agent_config()")
    print("✅ Added strict validation in create_client_for_agent()")
    print("✅ Empty agent models now raise ValueError instead of using defaults")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
