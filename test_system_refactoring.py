#!/usr/bin/env python3
# test_system_refactoring.py
#
# 测试系统重构功能

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_enhanced_logger():
    """测试增强日志系统"""
    print("🧪 Testing Enhanced Logger System")
    print("=" * 70)
    
    try:
        from utils.enhanced_logger import create_enhanced_logger, ProgressTracker
        
        # 创建增强日志器
        logger = create_enhanced_logger("TestLogger")
        
        print("📋 Testing enhanced logger functionality:")
        
        # 测试各种日志方法
        logger.step("测试步骤信息")
        logger.progress("测试进度信息")
        logger.success("测试成功信息")
        logger.warning("测试警告信息")
        
        # 测试配置摘要
        config_info = {
            "LLM提供商": "OpenAI",
            "搜索提供商": "ADS",
            "论文数量": 100
        }
        logger.config_summary(config_info)
        
        # 测试文件信息
        logger.file_info("测试文件操作", "test_file.json", 1024)
        
        # 测试阶段管理
        logger.phase_start("测试阶段", "阶段描述")
        logger.phase_end("测试阶段", 5.5)
        
        # 测试进度跟踪器
        progress = ProgressTracker(logger, 5, "测试任务")
        for i in range(5):
            progress.update(message=f"处理项目 {i+1}")
        progress.complete()
        
        print(f"   ✅ 日志文件路径: {logger.get_log_file_path()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced logger test failed: {e}")
        return False


def test_planner_agent_enhancements():
    """测试PlannerAgent增强功能"""
    print("\n🧪 Testing PlannerAgent Enhancements")
    print("=" * 70)
    
    try:
        from agents.planner_agent import PlannerAgent
        from models import ResearchQuery
        
        print("📋 Testing PlannerAgent enhancements:")
        
        # 创建PlannerAgent实例（模拟）
        try:
            planner = PlannerAgent()
        except Exception:
            # 如果没有LLM配置，创建模拟实例
            planner = PlannerAgent.__new__(PlannerAgent)
            planner.logger = None
        
        # 测试ResearchQuery模型的新字段
        query = ResearchQuery(main_topic="Test Topic")
        
        # 测试ADS搜索建议字段
        query.ads_search_suggestions = {
            "sub_question_1": {
                "search_strategy": "title:(exoplanet) AND database:astronomy",
                "fields": ["title", "abstract"],
                "date_range": "2020-2024"
            }
        }
        
        # 测试获取ADS搜索建议的方法
        suggestion = query.get_ads_suggestion_for_subquestion(0)
        if suggestion:
            print(f"   ✅ ADS搜索建议功能正常: {suggestion['search_strategy']}")
        else:
            print(f"   ✅ ADS搜索建议方法存在")
        
        # 检查保存方法是否存在
        if hasattr(planner, '_save_research_plan'):
            print(f"   ✅ 研究计划保存方法已添加")
        else:
            print(f"   ❌ 研究计划保存方法缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ PlannerAgent enhancement test failed: {e}")
        return False


def test_ads_client_specialization():
    """测试ADS客户端专业化功能"""
    print("\n🧪 Testing ADS Client Specialization")
    print("=" * 70)
    
    try:
        from clients.ads_client import ADSClient
        
        print("📋 Testing ADS client specialization:")
        
        # 创建ADS客户端实例（模拟）
        try:
            ads_client = ADSClient()
        except ValueError:
            # 如果没有API token，创建模拟实例
            ads_client = ADSClient.__new__(ADSClient)
            ads_client.api_token = "test_token"
            ads_client.base_url = "https://api.adsabs.harvard.edu/v1"
            ads_client.logger = None
        
        # 检查专业化方法
        required_methods = [
            "search_astrophysics_papers_by_subquestion",
            "_build_astrophysics_query",
            "_retrieve_papers_with_retry",
            "_simplify_query",
            "_save_ads_results"
        ]
        
        for method_name in required_methods:
            if hasattr(ads_client, method_name):
                print(f"   ✅ 专业化方法存在: {method_name}")
            else:
                print(f"   ❌ 专业化方法缺失: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ ADS client specialization test failed: {e}")
        return False


def test_paper_processing_pipeline():
    """测试论文处理流水线"""
    print("\n🧪 Testing Paper Processing Pipeline")
    print("=" * 70)
    
    try:
        from utils.paper_processing_pipeline import PaperProcessingPipeline
        from agents.paper_ranking_agent import PaperRankingAgent
        
        print("📋 Testing paper processing pipeline:")
        
        # 创建排序代理（模拟）
        try:
            ranking_agent = PaperRankingAgent()
        except Exception:
            ranking_agent = PaperRankingAgent.__new__(PaperRankingAgent)
            ranking_agent.logger = None
        
        # 创建处理流水线
        pipeline = PaperProcessingPipeline(ranking_agent)
        
        # 检查核心方法
        required_methods = [
            "process_subquestion_papers",
            "merge_and_deduplicate_papers",
            "_save_ranked_papers",
            "_save_final_papers",
            "_deduplicate_papers_advanced"
        ]
        
        for method_name in required_methods:
            if hasattr(pipeline, method_name):
                print(f"   ✅ 流水线方法存在: {method_name}")
            else:
                print(f"   ❌ 流水线方法缺失: {method_name}")
                return False
        
        # 测试统计功能
        stats = pipeline.get_processing_stats()
        print(f"   ✅ 处理统计功能正常: {type(stats).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Paper processing pipeline test failed: {e}")
        return False


def test_synthesizer_agent_enhancements():
    """测试SynthesizerAgent增强功能"""
    print("\n🧪 Testing SynthesizerAgent Enhancements")
    print("=" * 70)
    
    try:
        from agents.synthesizer_agent import SynthesizerAgent
        
        print("📋 Testing SynthesizerAgent enhancements:")
        
        # 创建SynthesizerAgent实例（模拟）
        try:
            synthesizer = SynthesizerAgent()
        except Exception:
            synthesizer = SynthesizerAgent.__new__(SynthesizerAgent)
            synthesizer.logger = None
            synthesizer.enhanced_logger = None
        
        # 检查中文笔记生成方法
        chinese_methods = [
            "generate_chinese_reading_notes",
            "_update_paper_structured_info",
            "_create_chinese_notes_content",
            "_generate_chinese_abstract",
            "_translate_to_chinese",
            "_save_chinese_notes"
        ]
        
        for method_name in chinese_methods:
            if hasattr(synthesizer, method_name):
                print(f"   ✅ 中文笔记方法存在: {method_name}")
            else:
                print(f"   ❌ 中文笔记方法缺失: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ SynthesizerAgent enhancement test failed: {e}")
        return False


def test_writer_agent_chinese_report():
    """测试WriterAgent中文报告功能"""
    print("\n🧪 Testing WriterAgent Chinese Report")
    print("=" * 70)
    
    try:
        from agents.writer_agent import WriterAgent
        
        print("📋 Testing WriterAgent Chinese report functionality:")
        
        # 创建WriterAgent实例（模拟）
        try:
            writer = WriterAgent()
        except Exception:
            writer = WriterAgent.__new__(WriterAgent)
            writer.logger = None
            writer.enhanced_logger = None
        
        # 检查中文报告生成方法
        chinese_report_methods = [
            "generate_chinese_research_report",
            "_create_chinese_report_content",
            "_generate_research_background",
            "_generate_literature_review",
            "_generate_key_findings",
            "_generate_technical_analysis",
            "_generate_future_directions",
            "_generate_chinese_references",
            "_save_chinese_report"
        ]
        
        for method_name in chinese_report_methods:
            if hasattr(writer, method_name):
                print(f"   ✅ 中文报告方法存在: {method_name}")
            else:
                print(f"   ❌ 中文报告方法缺失: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ WriterAgent Chinese report test failed: {e}")
        return False


def test_outputs_directory_structure():
    """测试输出目录结构"""
    print("\n🧪 Testing Outputs Directory Structure")
    print("=" * 70)
    
    try:
        print("📋 Testing outputs directory structure:")
        
        outputs_dir = Path("outputs")
        
        # 检查outputs目录是否存在
        if outputs_dir.exists():
            print(f"   ✅ outputs目录存在: {outputs_dir}")
        else:
            outputs_dir.mkdir(exist_ok=True)
            print(f"   ✅ 创建outputs目录: {outputs_dir}")
        
        # 检查logs子目录
        logs_dir = outputs_dir / "logs"
        if logs_dir.exists():
            print(f"   ✅ logs子目录存在: {logs_dir}")
        else:
            logs_dir.mkdir(exist_ok=True)
            print(f"   ✅ 创建logs子目录: {logs_dir}")
        
        # 列出预期的输出文件类型
        expected_files = [
            "research_plan_*.json",
            "ads_results_*.json", 
            "ranked_papers_*.json",
            "final_ranked_papers.json",
            "paper_analysis_notes.md",
            "research_report_*.md"
        ]
        
        print(f"   ✅ 预期输出文件类型:")
        for file_pattern in expected_files:
            print(f"      - {file_pattern}")
        
        return True
        
    except Exception as e:
        print(f"❌ Outputs directory structure test failed: {e}")
        return False


def main():
    """运行所有重构功能测试"""
    print("🚀 AI Research Assistant System Refactoring Tests")
    print("=" * 90)
    print("Testing implementation of enhanced astrophysics research workflow")
    
    tests = [
        ("Enhanced Logger System", test_enhanced_logger),
        ("PlannerAgent Enhancements", test_planner_agent_enhancements),
        ("ADS Client Specialization", test_ads_client_specialization),
        ("Paper Processing Pipeline", test_paper_processing_pipeline),
        ("SynthesizerAgent Enhancements", test_synthesizer_agent_enhancements),
        ("WriterAgent Chinese Report", test_writer_agent_chinese_report),
        ("Outputs Directory Structure", test_outputs_directory_structure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # 生成测试结果总结
    print("\n🎉 SYSTEM REFACTORING TESTING RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL SYSTEM REFACTORING COMPLETED SUCCESSFULLY!")
        print("✅ 日志和输出优化: 增强日志系统实现")
        print("✅ PlannerAgent增强: ADS搜索建议和计划保存")
        print("✅ ADS检索专业化: 天体物理学限制和重试机制")
        print("✅ 论文排序去重: 即时排序和全局去重流程")
        print("✅ SynthesizerAgent优化: 中文精读笔记生成")
        print("✅ WriterAgent中文报告: 完整中文研究报告")
        print("✅ 输出目录结构: 规范化文件保存")
    else:
        print("\n⚠️  Some refactoring components need attention")
        print("🔧 Review failed components before deployment")
    
    print("\n💡 Enhanced Workflow Features:")
    print("   📊 优化日志输出: 关键进度信息 + 详细文件日志")
    print("   🎯 专业化检索: 天体物理学数据库限制")
    print("   ⚡ 即时排序: 每个子问题立即排序保存")
    print("   🔄 全局去重: 基于DOI和标题的高级去重")
    print("   📝 中文笔记: AI翻译的精读笔记")
    print("   📋 中文报告: 完整的学术研究报告")
    print("   💾 规范存储: 统一的outputs目录结构")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
