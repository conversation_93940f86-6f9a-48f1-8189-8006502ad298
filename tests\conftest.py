# tests/conftest.py
#
# pytest配置文件和共享fixtures

import pytest
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import config
from models import Paper, PaperAnalysis, ResearchQuery, WebSearchResult


@pytest.fixture
def temp_output_dir():
    """创建临时输出目录"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_config():
    """模拟配置"""
    with patch.object(config, 'ADS_API_TOKEN', 'test_ads_token'), \
         patch.object(config, 'OPENAI_API_KEY', 'test_openai_key'), \
         patch.object(config, 'LLM_PROVIDER', 'openai'), \
         patch.object(config, 'SEARCH_PROVIDER', 'tavily'), \
         patch.object(config, 'TAVILY_API_KEY', 'test_tavily_key'), \
         patch.object(config, 'MAX_PAPERS_TO_ANALYZE', 10), \
         patch.object(config, 'PAPER_BATCH_SIZE', 3):
        yield config


@pytest.fixture
def sample_paper():
    """创建示例论文对象"""
    return Paper(
        title="Test Paper: A Study of Something Important",
        authors=["Smith, J.", "Doe, A.", "Johnson, B."],
        abstract="This is a test abstract describing the research methodology and findings.",
        publication_date="2023-01-15",
        journal="Test Journal",
        doi="10.1000/test.doi",
        ads_bibcode="2023TestJ..1..123S",
        citation_count=42,
        source="ads"
    )


@pytest.fixture
def sample_paper_analysis():
    """创建示例论文分析"""
    return PaperAnalysis(
        short_summary="This paper presents a novel approach to solving test problems.",
        relevance_to_topic="Highly relevant to the research topic as it addresses key challenges.",
        research_subject="Test subjects and experimental conditions",
        methodology="Experimental design with control groups and statistical analysis",
        data_used="Synthetic test data and real-world observations",
        key_findings_or_results=[
            "Finding 1: Significant improvement in test metrics",
            "Finding 2: Novel correlation discovered",
            "Finding 3: Method outperforms baseline approaches"
        ]
    )


@pytest.fixture
def sample_research_query():
    """创建示例研究查询"""
    query = ResearchQuery(main_topic="Test Research Topic")
    query.sub_questions = [
        "What are the fundamental concepts?",
        "What are the current challenges?",
        "What are the future directions?"
    ]
    query.keywords = ["test", "research", "analysis", "methodology"]
    return query


@pytest.fixture
def sample_web_results():
    """创建示例网络搜索结果"""
    return [
        WebSearchResult(
            title="Test Article 1",
            url="https://example.com/article1",
            snippet="This is a test snippet about the research topic.",
            source="test"
        ),
        WebSearchResult(
            title="Test Article 2", 
            url="https://example.com/article2",
            snippet="Another test snippet with relevant information.",
            source="test"
        )
    ]


@pytest.fixture
def mock_llm_response():
    """模拟LLM响应"""
    def _mock_response(prompt, **kwargs):
        from models import LLMResponse, ResponseStatus
        
        # 根据提示词类型返回不同的模拟响应
        if "子问题" in prompt or "sub-question" in prompt.lower():
            response_text = '["问题1？", "问题2？", "问题3？"]'
        elif "JSON" in prompt:
            response_text = '''{
                "short_summary": "Test summary",
                "relevance_to_topic": "Test relevance",
                "research_subject": "Test subject",
                "methodology": "Test methodology",
                "data_used": "Test data",
                "key_findings_or_results": ["Finding 1", "Finding 2"]
            }'''
        else:
            response_text = "This is a test response from the LLM."
        
        return LLMResponse(
            prompt=prompt,
            response=response_text,
            model="test-model",
            provider="test",
            status=ResponseStatus.SUCCESS,
            response_time=0.1
        )
    
    return _mock_response


@pytest.fixture
def mock_search_response():
    """模拟搜索响应"""
    def _mock_response(query, **kwargs):
        from models import SearchResponse, ResponseStatus
        
        return SearchResponse(
            query=query,
            total_results=2,
            results=[
                {
                    "title": f"Test Result for {query}",
                    "url": "https://example.com/test",
                    "snippet": f"Test snippet for query: {query}",
                    "source": "test"
                }
            ],
            source="test",
            status=ResponseStatus.SUCCESS,
            search_time=0.1
        )
    
    return _mock_response


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 设置测试环境变量
    os.environ['LOG_LEVEL'] = 'ERROR'  # 减少测试时的日志输出
    os.environ['EXECUTION_MODE'] = 'deep_research'
    
    yield
    
    # 清理测试环境变量
    test_vars = ['LOG_LEVEL', 'EXECUTION_MODE']
    for var in test_vars:
        if var in os.environ:
            del os.environ[var]
