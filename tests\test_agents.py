# tests/test_agents.py
#
# 测试智能代理

import pytest
from unittest.mock import Mock, patch
from agents import PlannerAgent, SynthesizerAgent, WriterAgent
from models import Paper, PaperAnalysis, WebSearchResult


class TestPlannerAgent:
    """测试规划代理"""
    
    def test_planner_initialization(self):
        """测试规划代理初始化"""
        with patch('agents.planner_agent.LLMClient') as mock_llm:
            planner = PlannerAgent()
            assert planner.llm_client is not None
    
    def test_generate_research_plan_success(self, mock_llm_response):
        """测试成功生成研究计划"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response(
            '["什么是基本概念？", "当前挑战是什么？", "未来方向如何？"]'
        )
        
        planner = PlannerAgent(mock_llm_client)
        query = planner.generate_research_plan("测试主题")
        
        assert query.main_topic == "测试主题"
        assert len(query.sub_questions) >= 3
        assert len(query.keywords) > 0
        
        # 验证LLM被调用
        mock_llm_client.generate.assert_called_once()
    
    def test_generate_research_plan_llm_failure(self):
        """测试LLM失败时的备用计划"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = Mock(
            is_successful=Mock(return_value=False),
            error="Test error"
        )
        
        planner = PlannerAgent(mock_llm_client)
        query = planner.generate_research_plan("测试主题")
        
        # 应该返回备用计划
        assert query.main_topic == "测试主题"
        assert len(query.sub_questions) > 0  # 备用问题
        assert "基本概念" in query.sub_questions[0]
    
    def test_parse_sub_questions_json_format(self):
        """测试解析JSON格式的子问题"""
        planner = PlannerAgent(Mock())
        
        # 测试标准JSON格式
        response = '["问题1？", "问题2？", "问题3？"]'
        questions = planner._parse_sub_questions(response)
        
        assert len(questions) == 3
        assert questions[0] == "问题1？"
        assert questions[1] == "问题2？"
        assert questions[2] == "问题3？"
    
    def test_parse_sub_questions_numbered_format(self):
        """测试解析编号格式的子问题"""
        planner = PlannerAgent(Mock())
        
        response = """
        1. 什么是基本概念
        2. 当前挑战是什么
        3. 未来方向如何
        """
        questions = planner._parse_sub_questions(response)
        
        assert len(questions) == 3
        assert all(q.endswith('?') for q in questions)
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        planner = PlannerAgent(Mock())
        
        topic = "人工智能机器学习"
        sub_questions = ["什么是深度学习？", "神经网络如何工作？"]
        
        keywords = planner._extract_keywords(topic, sub_questions)
        
        assert len(keywords) > 0
        assert any("人工智能" in kw or "机器学习" in kw or "深度学习" in kw for kw in keywords)
    
    def test_validate_research_plan(self, sample_research_query):
        """测试研究计划验证"""
        planner = PlannerAgent(Mock())
        
        # 有效计划
        assert planner.validate_research_plan(sample_research_query) is True
        
        # 无效计划 - 缺少主题
        invalid_query = sample_research_query
        invalid_query.main_topic = ""
        assert planner.validate_research_plan(invalid_query) is False


class TestSynthesizerAgent:
    """测试综合代理"""
    
    def test_synthesizer_initialization(self):
        """测试综合代理初始化"""
        with patch('agents.synthesizer_agent.LLMClient') as mock_llm:
            synthesizer = SynthesizerAgent()
            assert synthesizer.llm_client is not None
    
    def test_synthesize_web_results_success(self, sample_web_results, mock_llm_response):
        """测试成功综合网络结果"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response("综合摘要内容")
        
        synthesizer = SynthesizerAgent(mock_llm_client)
        summary = synthesizer.synthesize_web_results("测试问题", sample_web_results)
        
        assert isinstance(summary, str)
        assert len(summary) > 0
        mock_llm_client.generate.assert_called_once()
    
    def test_synthesize_web_results_empty(self):
        """测试空网络结果的综合"""
        synthesizer = SynthesizerAgent(Mock())
        summary = synthesizer.synthesize_web_results("测试问题", [])
        
        assert "未找到" in summary
    
    def test_analyze_paper_success(self, sample_paper, mock_llm_response):
        """测试成功分析论文"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response(
            '{"short_summary": "测试摘要", "relevance_to_topic": "相关性", '
            '"research_subject": "研究对象", "methodology": "方法", '
            '"data_used": "数据", "key_findings_or_results": ["发现1", "发现2"]}'
        )
        
        synthesizer = SynthesizerAgent(mock_llm_client)
        analysis = synthesizer.analyze_paper(sample_paper, "测试主题")
        
        assert analysis is not None
        assert isinstance(analysis, PaperAnalysis)
        assert analysis.short_summary == "测试摘要"
        assert len(analysis.key_findings_or_results) == 2
    
    def test_analyze_paper_no_abstract(self):
        """测试分析没有摘要的论文"""
        paper = Paper(title="Test Paper")  # 没有摘要
        synthesizer = SynthesizerAgent(Mock())
        
        analysis = synthesizer.analyze_paper(paper, "测试主题")
        assert analysis is None
    
    def test_batch_analyze_papers(self, mock_llm_response):
        """测试批量分析论文"""
        papers = [
            Paper(title="Paper 1", abstract="Abstract 1"),
            Paper(title="Paper 2", abstract="Abstract 2"),
            Paper(title="Paper 3")  # 没有摘要
        ]
        
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response(
            '{"short_summary": "测试", "relevance_to_topic": "相关", '
            '"research_subject": "对象", "methodology": "方法", '
            '"data_used": "数据", "key_findings_or_results": ["发现"]}'
        )
        
        synthesizer = SynthesizerAgent(mock_llm_client)
        analyzed_papers = synthesizer.batch_analyze_papers(papers, "测试主题")
        
        assert len(analyzed_papers) == 3
        assert analyzed_papers[0].has_analysis()
        assert analyzed_papers[1].has_analysis()
        assert not analyzed_papers[2].has_analysis()  # 没有摘要的论文
    
    def test_parse_paper_analysis_valid_json(self):
        """测试解析有效的JSON分析"""
        synthesizer = SynthesizerAgent(Mock())
        
        json_response = '''
        {
            "short_summary": "测试摘要",
            "relevance_to_topic": "相关性描述",
            "research_subject": "研究对象",
            "methodology": "研究方法",
            "data_used": "使用的数据",
            "key_findings_or_results": ["发现1", "发现2"]
        }
        '''
        
        result = synthesizer._parse_paper_analysis(json_response)
        
        assert result is not None
        assert result["short_summary"] == "测试摘要"
        assert len(result["key_findings_or_results"]) == 2
    
    def test_parse_paper_analysis_invalid_json(self):
        """测试解析无效的JSON"""
        synthesizer = SynthesizerAgent(Mock())
        
        invalid_json = "这不是有效的JSON"
        result = synthesizer._parse_paper_analysis(invalid_json)
        
        assert result is None
    
    def test_create_timeline_summary(self, sample_paper, sample_paper_analysis):
        """测试创建时间线摘要"""
        sample_paper.analysis = sample_paper_analysis
        papers = [sample_paper]
        
        synthesizer = SynthesizerAgent(Mock())
        timeline = synthesizer.create_timeline_summary(papers)
        
        assert isinstance(timeline, str)
        assert "时间线" in timeline
        assert "2023年" in timeline
    
    def test_get_structured_paper_data(self, sample_paper, sample_paper_analysis):
        """测试获取结构化论文数据"""
        sample_paper.analysis = sample_paper_analysis
        papers = [sample_paper]
        
        synthesizer = SynthesizerAgent(Mock())
        structured_data = synthesizer.get_structured_paper_data(papers)
        
        assert isinstance(structured_data, str)
        # 应该是有效的JSON
        import json
        data = json.loads(structured_data)
        assert len(data) == 1
        assert data[0]["title"] == sample_paper.title


class TestWriterAgent:
    """测试撰写代理"""
    
    def test_writer_initialization(self):
        """测试撰写代理初始化"""
        with patch('agents.writer_agent.LLMClient') as mock_llm:
            writer = WriterAgent()
            assert writer.llm_client is not None
    
    def test_write_research_report_success(self, mock_llm_response):
        """测试成功撰写研究报告"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response("# 研究报告\n\n这是一份测试报告。")
        
        writer = WriterAgent(mock_llm_client)
        report = writer.write_research_report(
            topic="测试主题",
            web_summaries="网络摘要",
            structured_paper_analyses="论文分析",
            timeline="时间线"
        )
        
        assert isinstance(report, str)
        assert "研究报告" in report
        mock_llm_client.generate.assert_called_once()
    
    def test_write_innovation_proposal_success(self, mock_llm_response):
        """测试成功撰写创新方案"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response("# 创新方案\n\n这是创新方案内容。")
        
        writer = WriterAgent(mock_llm_client)
        proposal = writer.write_innovation_proposal("研究报告内容")
        
        assert isinstance(proposal, str)
        assert "创新方案" in proposal
    
    def test_write_feasibility_analysis_success(self, mock_llm_response):
        """测试成功撰写可行性分析"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = mock_llm_response("# 可行性分析\n\n这是可行性分析内容。")
        
        writer = WriterAgent(mock_llm_client)
        analysis = writer.write_feasibility_analysis("研究报告", "创新方案")
        
        assert isinstance(analysis, str)
        assert "可行性分析" in analysis
    
    def test_create_detailed_notes(self, sample_paper, sample_paper_analysis):
        """测试创建详细笔记"""
        sample_paper.analysis = sample_paper_analysis
        papers = [sample_paper]
        
        writer = WriterAgent(Mock())
        notes = writer.create_detailed_notes(papers, "测试主题")
        
        assert isinstance(notes, str)
        assert "详细研究笔记" in notes
        assert sample_paper.title in notes
        assert "统计信息" in notes
    
    def test_create_paper_note_section(self, sample_paper, sample_paper_analysis):
        """测试创建论文笔记部分"""
        sample_paper.analysis = sample_paper_analysis
        
        writer = WriterAgent(Mock())
        section = writer._create_paper_note_section(sample_paper, 1)
        
        assert isinstance(section, str)
        assert sample_paper.title in section
        assert "核心贡献" in section
        assert "研究方法" in section
        assert "关键发现" in section
    
    def test_llm_failure_fallbacks(self):
        """测试LLM失败时的备用方案"""
        mock_llm_client = Mock()
        mock_llm_client.generate.return_value = Mock(
            is_successful=Mock(return_value=False),
            error="Test error"
        )
        
        writer = WriterAgent(mock_llm_client)
        
        # 测试研究报告备用方案
        report = writer.write_research_report("主题", "摘要", "分析", "时间线")
        assert "研究报告" in report
        
        # 测试创新方案备用方案
        proposal = writer.write_innovation_proposal("报告内容")
        assert "创新研究方案" in proposal
        
        # 测试可行性分析备用方案
        feasibility = writer.write_feasibility_analysis("报告", "方案")
        assert "可行性分析" in feasibility
