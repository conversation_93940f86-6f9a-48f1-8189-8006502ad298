# tests/test_config.py
#
# 测试配置系统

import pytest
import os
from unittest.mock import patch
from config import Config


class TestConfig:
    """测试配置类"""
    
    def test_default_values(self):
        """测试默认配置值"""
        # 创建一个新的配置实例来测试默认值
        with patch.dict(os.environ, {}, clear=True):
            config = Config()
            
            assert config.EXECUTION_MODE == "deep_research"
            assert config.DEFAULT_LLM_PROVIDER == "openai"
            assert config.DEFAULT_LLM_TEMPERATURE == 0.7
            assert config.DEFAULT_LLM_MAX_TOKENS == 8000
            assert config.WEB_SEARCH_RESULTS_PER_QUERY == 5
            assert config.ADS_SEARCH_RESULTS_PER_QUERY == 10
            assert config.MAX_PAPERS_TO_ANALYZE == 50
            assert config.PAPER_BATCH_SIZE == 5
            assert config.REQUEST_TIMEOUT == 30
            assert config.MAX_RETRIES == 3
            assert config.REQUEST_DELAY == 1.0
            assert config.LOG_LEVEL == "INFO"
    
    def test_environment_variable_override(self):
        """测试环境变量覆盖"""
        test_env = {
            'EXECUTION_MODE': 'full_analysis',
            'DEFAULT_LLM_PROVIDER': 'anthropic',
            'DEFAULT_LLM_TEMPERATURE': '0.5',
            'MAX_PAPERS_TO_ANALYZE': '100',
            'PAPER_BATCH_SIZE': '10',
            'LOG_LEVEL': 'DEBUG'
        }

        with patch.dict(os.environ, test_env, clear=True):
            config = Config()

            assert config.EXECUTION_MODE == "full_analysis"
            assert config.DEFAULT_LLM_PROVIDER == "anthropic"
            assert config.DEFAULT_LLM_TEMPERATURE == 0.5
            assert config.MAX_PAPERS_TO_ANALYZE == 100
            assert config.PAPER_BATCH_SIZE == 10
            assert config.LOG_LEVEL == "DEBUG"
    
    def test_boolean_parsing(self):
        """测试布尔值解析"""
        test_env = {
            'VERBOSE_LOGGING': 'true',
            'SAVE_INTERMEDIATE_RESULTS': 'false',
            'ENABLE_CACHE': 'True',
            'INCLUDE_CITATIONS': 'FALSE'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            
            assert config.VERBOSE_LOGGING is True
            assert config.SAVE_INTERMEDIATE_RESULTS is False
            assert config.ENABLE_CACHE is True
            assert config.INCLUDE_CITATIONS is False
    
    def test_list_parsing(self):
        """测试列表解析"""
        test_env = {
            'SEARCH_FILTER_KEYWORDS': 'keyword1,keyword2,keyword3'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            
            assert config.SEARCH_FILTER_KEYWORDS == ['keyword1', 'keyword2', 'keyword3']
        
        # 测试空列表
        with patch.dict(os.environ, {'SEARCH_FILTER_KEYWORDS': ''}, clear=True):
            config = Config()
            assert config.SEARCH_FILTER_KEYWORDS == []
    
    def test_config_validation_success(self):
        """测试配置验证成功"""
        test_env = {
            'DEFAULT_LLM_PROVIDER': 'openai',
            'OPENAI_API_KEY': 'test_key',
            'ADS_API_TOKEN': 'test_token',
            'TAVILY_API_KEY': 'test_tavily_key',
            'SEARCH_PROVIDER': 'tavily'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            # 应该不抛出异常
            assert config.validate_config() is True
    
    def test_config_validation_missing_llm_key(self):
        """测试缺少LLM API密钥的验证"""
        test_env = {
            'DEFAULT_LLM_PROVIDER': 'openai',
            'ADS_API_TOKEN': 'test_token'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            
            with pytest.raises(ValueError) as exc_info:
                config.validate_config()
            
            assert "OPENAI_API_KEY is required" in str(exc_info.value)
    
    def test_config_validation_missing_ads_token(self):
        """测试缺少ADS API令牌的验证"""
        test_env = {
            'DEFAULT_LLM_PROVIDER': 'openai',
            'OPENAI_API_KEY': 'test_key'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            
            with pytest.raises(ValueError) as exc_info:
                config.validate_config()
            
            assert "ADS_API_TOKEN is required" in str(exc_info.value)
    
    def test_config_validation_invalid_ranges(self):
        """测试无效数值范围的验证"""
        test_env = {
            'DEFAULT_LLM_PROVIDER': 'openai',
            'OPENAI_API_KEY': 'test_key',
            'ADS_API_TOKEN': 'test_token',
            'PAPER_SIMILARITY_THRESHOLD': '1.5',  # 超出范围
            'MAX_CONCURRENT_REQUESTS': '0',  # 小于最小值
            'PAPER_BATCH_SIZE': '-1'  # 小于最小值
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            
            with pytest.raises(ValueError) as exc_info:
                config.validate_config()
            
            error_message = str(exc_info.value)
            assert "PAPER_SIMILARITY_THRESHOLD must be between 0 and 1" in error_message
            assert "MAX_CONCURRENT_REQUESTS must be at least 1" in error_message
            assert "PAPER_BATCH_SIZE must be at least 1" in error_message
    
    def test_get_config_summary(self):
        """测试配置摘要"""
        test_env = {
            'EXECUTION_MODE': 'full_analysis',
            'DEFAULT_LLM_PROVIDER': 'anthropic',
            'ANTHROPIC_API_KEY': 'test_key',
            'ADS_API_TOKEN': 'test_token',
            'SEARCH_PROVIDER': 'google',
            'GOOGLE_SEARCH_API_KEY': 'test_google_key',
            'GOOGLE_CSE_ID': 'test_cse_id'
        }

        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            summary = config.get_config_summary()

            assert summary['execution_mode'] == 'full_analysis'
            assert summary['llm_provider'] == 'anthropic'
            assert summary['search_provider'] == 'google'
            assert summary['api_keys_configured']['ads'] is True
            assert summary['api_keys_configured']['anthropic'] is True
            assert summary['api_keys_configured']['google'] is True
    
    def test_is_development_mode(self):
        """测试开发模式检测"""
        # 测试DEBUG日志级别
        with patch.dict(os.environ, {'LOG_LEVEL': 'DEBUG'}, clear=True):
            config = Config()
            assert config.is_development_mode() is True
        
        # 测试详细日志
        with patch.dict(os.environ, {'VERBOSE_LOGGING': 'true'}, clear=True):
            config = Config()
            assert config.is_development_mode() is True
        
        # 测试非开发模式
        with patch.dict(os.environ, {'LOG_LEVEL': 'INFO', 'VERBOSE_LOGGING': 'false'}, clear=True):
            config = Config()
            assert config.is_development_mode() is False
    
    def test_get_available_providers(self):
        """测试可用提供商检测"""
        test_env = {
            'OPENAI_API_KEY': 'test_openai_key',
            'TAVILY_API_KEY': 'test_tavily_key'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            providers = config.get_available_providers()
            
            assert providers['llm']['openai'] is True
            assert providers['llm']['anthropic'] is False
            assert providers['search']['tavily'] is True
            assert providers['search']['google'] is False
    
    def test_output_file_paths(self):
        """测试输出文件路径"""
        config = Config()
        
        assert config.RESEARCH_REPORT_FILENAME == "research_report.md"
        assert config.RESEARCH_DETAILS_FILENAME == "research_details.md"
        assert config.PROPOSAL_FILENAME == "proposal.md"
        assert config.FEASIBILITY_FILENAME == "feasibility_analysis.md"
        assert config.OUTPUT_DIR == "outputs"
    
    def test_advanced_config_options(self):
        """测试高级配置选项"""
        test_env = {
            'PAPER_SIMILARITY_THRESHOLD': '0.9',
            'MAX_CONCURRENT_REQUESTS': '5',
            'OUTPUT_LANGUAGE': 'en',
            'CACHE_DIR': 'custom_cache'
        }
        
        with patch.dict(os.environ, test_env, clear=True):
            config = Config()
            
            assert config.PAPER_SIMILARITY_THRESHOLD == 0.9
            assert config.MAX_CONCURRENT_REQUESTS == 5
            assert config.OUTPUT_LANGUAGE == 'en'
            assert config.CACHE_DIR == 'custom_cache'
