# tests/test_integration.py
#
# 集成测试

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from main import ResearchAssistant
from models import ResearchSession, Paper, WebSearchResult
from utils import ReportGenerator


class TestResearchAssistantIntegration:
    """测试研究助理集成功能"""
    
    @pytest.fixture
    def mock_clients(self):
        """模拟所有客户端"""
        with patch('main.ADSClient') as mock_ads, \
             patch('main.WebSearchClient') as mock_web, \
             patch('main.LLMClient') as mock_llm:
            
            # 配置ADS客户端模拟
            mock_ads_instance = Mock()
            mock_ads_instance.test_connection.return_value = True
            mock_ads_instance.search_papers.return_value = Mock(
                is_successful=Mock(return_value=True),
                results=[{
                    'title': 'Test Paper',
                    'authors': ['Test Author'],
                    'abstract': 'Test abstract',
                    'publication_date': '2023-01-01',
                    'journal': 'Test Journal',
                    'source': 'ads'
                }]
            )
            mock_ads_instance.build_search_query.return_value = "test query"
            mock_ads.return_value = mock_ads_instance
            
            # 配置Web搜索客户端模拟
            mock_web_instance = Mock()
            mock_web_instance.test_connection.return_value = True
            mock_web_instance.is_available.return_value = True
            mock_web_instance.search.return_value = Mock(
                is_successful=Mock(return_value=True),
                results=[{
                    'title': 'Test Web Result',
                    'url': 'https://test.com',
                    'snippet': 'Test snippet',
                    'source': 'test'
                }]
            )
            mock_web.return_value = mock_web_instance
            
            # 配置LLM客户端模拟
            mock_llm_instance = Mock()
            mock_llm_instance.test_connection.return_value = True
            mock_llm_instance.generate.return_value = Mock(
                is_successful=Mock(return_value=True),
                response='["问题1？", "问题2？", "问题3？"]'
            )
            mock_llm.return_value = mock_llm_instance
            
            yield {
                'ads': mock_ads_instance,
                'web': mock_web_instance,
                'llm': mock_llm_instance
            }
    
    @pytest.fixture
    def temp_output_dir(self):
        """临时输出目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    def test_research_assistant_initialization(self, mock_clients):
        """测试研究助理初始化"""
        with patch('main.config') as mock_config:
            mock_config.validate_config.return_value = True
            
            assistant = ResearchAssistant()
            
            assert assistant.ads_client is not None
            assert assistant.web_client is not None
            assert assistant.llm_client is not None
            assert assistant.planner is not None
            assert assistant.synthesizer is not None
            assert assistant.writer is not None
            assert assistant.report_generator is not None
    
    def test_health_check_integration(self, mock_clients):
        """测试健康检查集成"""
        with patch('main.config') as mock_config, \
             patch('main.health_checker') as mock_health_checker:
            
            mock_config.validate_config.return_value = True
            mock_health_checker.get_health_summary.return_value = {
                'overall_status': 'healthy',
                'healthy_services': 4,
                'warning_services': 0,
                'unhealthy_services': 0,
                'services': [
                    {'service': 'Configuration', 'status': 'healthy', 'message': 'OK'},
                    {'service': 'LLM API', 'status': 'healthy', 'message': 'OK'},
                    {'service': 'ADS API', 'status': 'healthy', 'message': 'OK'},
                    {'service': 'Web Search API', 'status': 'healthy', 'message': 'OK'}
                ]
            }
            
            assistant = ResearchAssistant()
            # 如果没有抛出异常，说明健康检查通过
            assert True
    
    def test_information_collection_integration(self, mock_clients, temp_output_dir):
        """测试信息收集集成"""
        with patch('main.config') as mock_config:
            mock_config.validate_config.return_value = True
            mock_config.OUTPUT_DIR = temp_output_dir
            mock_config.MAX_PAPERS_TO_ANALYZE = 5
            
            # 模拟规划代理
            mock_query = Mock()
            mock_query.sub_questions = ["问题1？", "问题2？"]
            mock_query.keywords = ["关键词1", "关键词2"]
            
            with patch('main.PlannerAgent') as mock_planner_class:
                mock_planner = Mock()
                mock_planner.generate_research_plan.return_value = mock_query
                mock_planner_class.return_value = mock_planner
                
                assistant = ResearchAssistant()
                session = ResearchSession(query=mock_query)
                
                # 执行信息收集
                assistant._collect_information(session)
                
                # 验证结果
                assert len(session.papers) > 0
                assert len(session.web_results) > 0
    
    def test_paper_analysis_integration(self, mock_clients, temp_output_dir):
        """测试论文分析集成"""
        with patch('main.config') as mock_config:
            mock_config.validate_config.return_value = True
            mock_config.OUTPUT_DIR = temp_output_dir
            mock_config.PAPER_BATCH_SIZE = 2
            
            # 创建测试会话
            mock_query = Mock()
            mock_query.main_topic = "测试主题"
            session = ResearchSession(query=mock_query)
            
            # 添加测试论文
            papers = [
                Paper(title="Paper 1", abstract="Abstract 1"),
                Paper(title="Paper 2", abstract="Abstract 2"),
                Paper(title="Paper 3", abstract="Abstract 3")
            ]
            for paper in papers:
                session.add_paper(paper)
            
            # 模拟综合代理
            with patch('main.SynthesizerAgent') as mock_synthesizer_class:
                mock_synthesizer = Mock()
                mock_synthesizer.batch_analyze_papers.return_value = papers
                mock_synthesizer_class.return_value = mock_synthesizer
                
                assistant = ResearchAssistant()
                assistant._analyze_papers(session)
                
                # 验证分析被调用
                assert mock_synthesizer.batch_analyze_papers.called
    
    def test_report_generation_integration(self, mock_clients, temp_output_dir):
        """测试报告生成集成"""
        with patch('main.config') as mock_config:
            mock_config.validate_config.return_value = True
            mock_config.OUTPUT_DIR = temp_output_dir
            mock_config.RESEARCH_REPORT_FILENAME = "test_report.md"
            
            # 创建测试会话
            mock_query = Mock()
            mock_query.main_topic = "测试主题"
            session = ResearchSession(query=mock_query)
            
            # 模拟撰写代理和综合代理
            with patch('main.WriterAgent') as mock_writer_class, \
                 patch('main.SynthesizerAgent') as mock_synthesizer_class:
                
                mock_writer = Mock()
                mock_writer.write_research_report.return_value = "# 测试报告\n\n报告内容"
                mock_writer_class.return_value = mock_writer
                
                mock_synthesizer = Mock()
                mock_synthesizer.get_structured_paper_data.return_value = "[]"
                mock_synthesizer.create_timeline_summary.return_value = "时间线"
                mock_synthesizer_class.return_value = mock_synthesizer
                
                assistant = ResearchAssistant()
                assistant._generate_research_report(session)
                
                # 验证报告文件被创建
                report_path = Path(temp_output_dir) / "test_report.md"
                assert report_path.exists()
                
                # 验证报告内容
                content = report_path.read_text(encoding='utf-8')
                assert "测试报告" in content
    
    def test_full_research_workflow(self, mock_clients, temp_output_dir):
        """测试完整研究工作流程"""
        with patch('main.config') as mock_config:
            mock_config.validate_config.return_value = True
            mock_config.OUTPUT_DIR = temp_output_dir
            mock_config.EXECUTION_MODE = "deep_research"
            mock_config.MAX_PAPERS_TO_ANALYZE = 3
            mock_config.PAPER_BATCH_SIZE = 2
            
            # 模拟所有代理
            with patch('main.PlannerAgent') as mock_planner_class, \
                 patch('main.SynthesizerAgent') as mock_synthesizer_class, \
                 patch('main.WriterAgent') as mock_writer_class:
                
                # 配置规划代理
                mock_query = Mock()
                mock_query.main_topic = "测试主题"
                mock_query.sub_questions = ["问题1？"]
                mock_query.keywords = ["关键词1"]
                
                mock_planner = Mock()
                mock_planner.generate_research_plan.return_value = mock_query
                mock_planner_class.return_value = mock_planner
                
                # 配置综合代理
                mock_synthesizer = Mock()
                mock_synthesizer.synthesize_web_results.return_value = "网络摘要"
                mock_synthesizer.batch_analyze_papers.return_value = [
                    Paper(title="Test Paper", abstract="Test abstract")
                ]
                mock_synthesizer.get_structured_paper_data.return_value = "[]"
                mock_synthesizer.create_timeline_summary.return_value = "时间线"
                mock_synthesizer_class.return_value = mock_synthesizer
                
                # 配置撰写代理
                mock_writer = Mock()
                mock_writer.write_research_report.return_value = "# 研究报告\n\n内容"
                mock_writer_class.return_value = mock_writer
                
                # 执行完整工作流程
                assistant = ResearchAssistant()
                session = assistant.run_research("测试主题")
                
                # 验证会话状态
                assert session.status == "completed"
                assert session.query.main_topic == "测试主题"
                
                # 验证各个步骤都被调用
                assert mock_planner.generate_research_plan.called
                assert mock_synthesizer.batch_analyze_papers.called
                assert mock_writer.write_research_report.called


class TestReportGeneratorIntegration:
    """测试报告生成器集成"""
    
    def test_report_generator_file_operations(self, temp_output_dir):
        """测试报告生成器文件操作"""
        generator = ReportGenerator(temp_output_dir)
        
        # 测试保存研究报告
        report_content = "# 测试报告\n\n这是测试内容。"
        report_path = generator.save_research_report(report_content, "test_report.md")
        
        assert Path(report_path).exists()
        assert "测试报告" in Path(report_path).read_text(encoding='utf-8')
        
        # 测试保存详细笔记
        notes_content = "# 详细笔记\n\n笔记内容。"
        notes_path = generator.save_research_details(notes_content, "test_notes.md")
        
        assert Path(notes_path).exists()
        assert "详细笔记" in Path(notes_path).read_text(encoding='utf-8')
        
        # 测试追加内容
        additional_content = "\n\n## 追加内容\n\n更多笔记。"
        generator.append_to_research_details(additional_content, "test_notes.md")
        
        updated_content = Path(notes_path).read_text(encoding='utf-8')
        assert "追加内容" in updated_content
        
        # 测试获取输出文件列表
        output_files = generator.get_output_files()
        assert len(output_files) >= 2
        assert any("test_report.md" in f for f in output_files)
        assert any("test_notes.md" in f for f in output_files)
