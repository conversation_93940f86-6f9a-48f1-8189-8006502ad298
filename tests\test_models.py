# tests/test_models.py
#
# 测试数据模型

import pytest
from datetime import datetime
from models import Paper, PaperAnalysis, ResearchQuery, WebSearchResult, ResearchSession


class TestPaper:
    """测试Paper模型"""
    
    def test_paper_creation(self, sample_paper):
        """测试论文对象创建"""
        assert sample_paper.title == "Test Paper: A Study of Something Important"
        assert len(sample_paper.authors) == 3
        assert sample_paper.authors[0] == "Smith, J<PERSON>"
        assert sample_paper.journal == "Test Journal"
        assert sample_paper.source == "ads"
    
    def test_paper_date_parsing(self):
        """测试日期解析"""
        # 测试不同日期格式
        paper1 = Paper(title="Test", publication_date="2023-01-15")
        assert paper1.publication_date.year == 2023
        assert paper1.publication_date.month == 1
        assert paper1.publication_date.day == 15
        
        paper2 = Paper(title="Test", publication_date="2023-01")
        assert paper2.publication_date.year == 2023
        assert paper2.publication_date.month == 1
        
        paper3 = Paper(title="Test", publication_date="2023")
        assert paper3.publication_date.year == 2023
    
    def test_paper_year_extraction(self, sample_paper):
        """测试年份提取"""
        assert sample_paper.get_year() == 2023
        
        paper_no_date = Paper(title="Test")
        assert paper_no_date.get_year() is None
    
    def test_paper_short_citation(self, sample_paper):
        """测试简短引用格式"""
        citation = sample_paper.get_short_citation()
        assert "Smith" in citation
        assert "2023" in citation
        
        # 测试单作者
        single_author_paper = Paper(title="Test", authors=["Johnson, A."], publication_date="2022-01-01")
        citation = single_author_paper.get_short_citation()
        assert "Johnson (2022)" == citation
        
        # 测试无作者
        no_author_paper = Paper(title="Test", publication_date="2021-01-01")
        citation = no_author_paper.get_short_citation()
        assert "Unknown (2021)" == citation
    
    def test_paper_analysis_setting(self, sample_paper, sample_paper_analysis):
        """测试设置论文分析"""
        assert not sample_paper.has_analysis()
        
        sample_paper.analysis = sample_paper_analysis
        assert sample_paper.has_analysis()
        
        # 测试通过字典设置分析
        paper2 = Paper(title="Test 2")
        analysis_dict = sample_paper_analysis.to_dict()
        paper2.set_analysis(analysis_dict)
        assert paper2.has_analysis()
    
    def test_paper_timeline_entry(self, sample_paper, sample_paper_analysis):
        """测试时间线条目生成"""
        # 没有分析时应返回None
        assert sample_paper.get_timeline_entry() is None
        
        # 有分析时应返回时间线条目
        sample_paper.analysis = sample_paper_analysis
        timeline_entry = sample_paper.get_timeline_entry()
        
        assert timeline_entry is not None
        assert "date" in timeline_entry
        assert "title" in timeline_entry
        assert "summary" in timeline_entry
        assert "citation" in timeline_entry
    
    def test_paper_serialization(self, sample_paper):
        """测试论文序列化"""
        # 测试转换为字典
        paper_dict = sample_paper.to_dict()
        assert isinstance(paper_dict, dict)
        assert paper_dict["title"] == sample_paper.title
        
        # 测试转换为JSON
        paper_json = sample_paper.to_json()
        assert isinstance(paper_json, str)
        assert sample_paper.title in paper_json


class TestPaperAnalysis:
    """测试PaperAnalysis模型"""
    
    def test_analysis_creation(self, sample_paper_analysis):
        """测试分析对象创建"""
        assert sample_paper_analysis.short_summary.startswith("This paper presents")
        assert len(sample_paper_analysis.key_findings_or_results) == 3
        assert sample_paper_analysis.methodology == "Experimental design with control groups and statistical analysis"
    
    def test_analysis_serialization(self, sample_paper_analysis):
        """测试分析序列化"""
        # 测试转换为字典
        analysis_dict = sample_paper_analysis.to_dict()
        assert isinstance(analysis_dict, dict)
        assert "short_summary" in analysis_dict
        assert "key_findings_or_results" in analysis_dict
        
        # 测试转换为JSON
        analysis_json = sample_paper_analysis.to_json()
        assert isinstance(analysis_json, str)
        assert "short_summary" in analysis_json


class TestResearchQuery:
    """测试ResearchQuery模型"""
    
    def test_query_creation(self, sample_research_query):
        """测试查询对象创建"""
        assert sample_research_query.main_topic == "Test Research Topic"
        assert len(sample_research_query.sub_questions) == 3
        assert len(sample_research_query.keywords) == 4
    
    def test_query_modification(self):
        """测试查询修改"""
        query = ResearchQuery(main_topic="Test Topic")
        
        # 测试添加子问题
        query.add_sub_question("New question?")
        assert len(query.sub_questions) == 1
        assert query.sub_questions[0] == "New question?"
        
        # 测试重复添加（应该不重复）
        query.add_sub_question("New question?")
        assert len(query.sub_questions) == 1
        
        # 测试添加关键词
        query.add_keyword("test")
        assert len(query.keywords) == 1
        assert query.keywords[0] == "test"
        
        # 测试重复添加关键词
        query.add_keyword("test")
        assert len(query.keywords) == 1


class TestWebSearchResult:
    """测试WebSearchResult模型"""
    
    def test_web_result_creation(self, sample_web_results):
        """测试网络搜索结果创建"""
        result = sample_web_results[0]
        assert result.title == "Test Article 1"
        assert result.url == "https://example.com/article1"
        assert result.snippet.startswith("This is a test snippet")
        assert result.source == "test"
    
    def test_web_result_serialization(self, sample_web_results):
        """测试网络搜索结果序列化"""
        result = sample_web_results[0]
        result_dict = result.to_dict()
        
        assert isinstance(result_dict, dict)
        assert result_dict["title"] == result.title
        assert result_dict["url"] == result.url


class TestResearchSession:
    """测试ResearchSession模型"""
    
    def test_session_creation(self, sample_research_query):
        """测试研究会话创建"""
        session = ResearchSession(query=sample_research_query)
        
        assert session.query.main_topic == "Test Research Topic"
        assert session.status == "initialized"
        assert len(session.papers) == 0
        assert len(session.web_results) == 0
    
    def test_session_paper_management(self, sample_research_query, sample_paper):
        """测试会话论文管理"""
        session = ResearchSession(query=sample_research_query)
        
        # 添加论文
        session.add_paper(sample_paper)
        assert len(session.papers) == 1
        
        # 测试获取已分析论文（当前没有分析）
        analyzed = session.get_analyzed_papers()
        assert len(analyzed) == 0
        
        # 添加分析后再测试
        from tests.conftest import sample_paper_analysis
        sample_paper.analysis = sample_paper_analysis()
        analyzed = session.get_analyzed_papers()
        assert len(analyzed) == 1
    
    def test_session_web_result_management(self, sample_research_query, sample_web_results):
        """测试会话网络结果管理"""
        session = ResearchSession(query=sample_research_query)
        
        for result in sample_web_results:
            session.add_web_result(result)
        
        assert len(session.web_results) == 2
    
    def test_session_status_management(self, sample_research_query):
        """测试会话状态管理"""
        session = ResearchSession(query=sample_research_query)
        
        assert session.status == "initialized"
        assert session.completed_at is None
        
        session.update_status("processing")
        assert session.status == "processing"
        
        session.update_status("completed")
        assert session.status == "completed"
        assert session.completed_at is not None
    
    def test_session_statistics(self, sample_research_query, sample_paper, sample_web_results):
        """测试会话统计"""
        session = ResearchSession(query=sample_research_query)
        
        # 添加数据
        session.add_paper(sample_paper)
        for result in sample_web_results:
            session.add_web_result(result)
        
        stats = session.get_summary_stats()
        assert stats["total_papers"] == 1
        assert stats["analyzed_papers"] == 0  # 没有分析
        assert stats["web_results"] == 2
        assert stats["sub_questions"] == 3
    
    def test_session_timeline_data(self, sample_research_query, sample_paper, sample_paper_analysis):
        """测试会话时间线数据"""
        session = ResearchSession(query=sample_research_query)
        
        # 添加有分析的论文
        sample_paper.analysis = sample_paper_analysis
        session.add_paper(sample_paper)
        
        timeline_data = session.get_timeline_data()
        assert len(timeline_data) == 1
        assert "date" in timeline_data[0]
        assert "title" in timeline_data[0]
