# utils/citation_manager.py
#
# 引用管理系统 - 负责学术引用的生成、格式化和管理

import re
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
from models.paper import Paper


class CitationFormatter:
    """引用格式化器 - 支持多种学术引用格式"""
    
    @staticmethod
    def format_apa(paper: Paper) -> Dict[str, str]:
        """
        APA格式引用
        
        Returns:
            Dict包含 'inline' 和 'full' 两种格式
        """
        # 处理作者
        if not paper.authors:
            author_part = "Unknown Author"
            inline_author = "Unknown"
        elif len(paper.authors) == 1:
            author_name = paper.authors[0]
            # 处理 "Last, First" 格式
            if ',' in author_name:
                last, first = author_name.split(',', 1)
                author_part = f"{last.strip()}, {first.strip()}"
                inline_author = last.strip()
            else:
                author_part = author_name
                inline_author = author_name.split()[-1]  # 取最后一个词作为姓氏
        elif len(paper.authors) == 2:
            authors = []
            inline_author = paper.authors[0].split(',')[0].strip() if ',' in paper.authors[0] else paper.authors[0].split()[-1]
            for author in paper.authors:
                if ',' in author:
                    last, first = author.split(',', 1)
                    authors.append(f"{last.strip()}, {first.strip()}")
                else:
                    authors.append(author)
            author_part = f"{authors[0]} & {authors[1]}"
        else:
            # 3个或更多作者
            first_author = paper.authors[0]
            if ',' in first_author:
                last, first = first_author.split(',', 1)
                author_part = f"{last.strip()}, {first.strip()}, et al."
                inline_author = f"{last.strip()} et al."
            else:
                author_part = f"{first_author}, et al."
                inline_author = f"{first_author.split()[-1]} et al."
        
        # 处理年份
        year = paper.get_year() or "n.d."
        
        # 处理标题
        title = paper.title or "Untitled"
        
        # 处理期刊
        journal = paper.journal or "Unknown Journal"
        
        # 内联引用格式
        inline = f"({inline_author}, {year})"
        
        # 完整引用格式
        full_parts = [f"{author_part} ({year})."]
        full_parts.append(f"{title}.")
        
        if paper.journal:
            if paper.volume:
                if paper.issue:
                    full_parts.append(f"*{journal}*, {paper.volume}({paper.issue})")
                else:
                    full_parts.append(f"*{journal}*, {paper.volume}")
            else:
                full_parts.append(f"*{journal}*")
        
        if paper.pages:
            full_parts.append(f"{paper.pages}.")
        
        if paper.doi:
            full_parts.append(f"https://doi.org/{paper.doi}")
        
        full = " ".join(full_parts)
        
        return {
            'inline': inline,
            'full': full
        }
    
    @staticmethod
    def format_ieee(paper: Paper) -> Dict[str, str]:
        """IEEE格式引用"""
        # 处理作者
        if not paper.authors:
            author_part = "Unknown Author"
        elif len(paper.authors) == 1:
            author_part = paper.authors[0]
        elif len(paper.authors) <= 6:
            author_part = ", ".join(paper.authors[:-1]) + f", and {paper.authors[-1]}"
        else:
            author_part = f"{paper.authors[0]}, et al."
        
        # 处理标题和期刊
        title = f'"{paper.title}"' if paper.title else '"Untitled"'
        journal = f"*{paper.journal}*" if paper.journal else "*Unknown Journal*"
        
        # 处理卷期页码
        vol_info = ""
        if paper.volume:
            vol_info = f"vol. {paper.volume}"
            if paper.issue:
                vol_info += f", no. {paper.issue}"
            if paper.pages:
                vol_info += f", pp. {paper.pages}"
        
        # 处理年份和月份
        year = paper.get_year() or "Unknown"
        
        # 组装完整引用
        full_parts = [f"{author_part},"]
        full_parts.append(f"{title},")
        full_parts.append(f"{journal},")
        if vol_info:
            full_parts.append(f"{vol_info},")
        full_parts.append(f"{year}.")
        
        if paper.doi:
            full_parts.append(f"doi: {paper.doi}")
        
        full = " ".join(full_parts)
        
        return {
            'inline': '[{}]',  # 占位符，将由CitationManager填入数字
            'full': full
        }
    
    @staticmethod
    def format_nature(paper: Paper) -> Dict[str, str]:
        """Nature格式引用"""
        # 处理作者
        if not paper.authors:
            author_part = "Unknown Author"
        elif len(paper.authors) <= 5:
            author_part = " & ".join([author.split(',')[0].strip() if ',' in author else author.split()[-1] 
                                    for author in paper.authors])
        else:
            first_author = paper.authors[0]
            if ',' in first_author:
                author_part = f"{first_author.split(',')[0].strip()} et al."
            else:
                author_part = f"{first_author.split()[-1]} et al."
        
        # 处理期刊和卷期
        journal = paper.journal or "Unknown Journal"
        vol_info = ""
        if paper.volume:
            vol_info = f"{paper.volume}"
            if paper.pages:
                vol_info += f", {paper.pages}"
        
        # 处理年份
        year = paper.get_year() or "Unknown"
        
        # 组装引用
        full_parts = [f"{author_part}."]
        if paper.title:
            full_parts.append(f"{paper.title}.")
        full_parts.append(f"*{journal}*")
        if vol_info:
            full_parts.append(f"{vol_info}")
        full_parts.append(f"({year}).")
        
        if paper.doi:
            full_parts.append(f"https://doi.org/{paper.doi}")
        
        full = " ".join(full_parts)
        
        return {
            'inline': '[{}]',  # 占位符，将由CitationManager填入数字
            'full': full
        }


class CitationManager:
    """引用管理器 - 负责引用的生成、格式化和管理"""
    
    def __init__(self, citation_style: str = "APA"):
        """
        初始化引用管理器
        
        Args:
            citation_style: 引用格式 (APA, IEEE, Nature)
        """
        self.citation_style = citation_style.upper()
        self.cited_papers: List[Paper] = []  # 被引用的论文列表
        self.citation_map: Dict[str, int] = {}  # 论文ID到引用编号的映射
        self.citation_counter = 0
        
        # 格式化器映射
        self.formatters = {
            'APA': CitationFormatter.format_apa,
            'IEEE': CitationFormatter.format_ieee,
            'NATURE': CitationFormatter.format_nature
        }
        
        if self.citation_style not in self.formatters:
            raise ValueError(f"Unsupported citation style: {citation_style}")
    
    def add_citation(self, paper: Paper) -> str:
        """
        添加引用并返回内联引用标记
        
        Args:
            paper: 要引用的论文对象
            
        Returns:
            str: 内联引用标记
        """
        paper_id = self._get_paper_id(paper)
        
        # 如果已经引用过，返回现有的引用标记
        if paper_id in self.citation_map:
            citation_num = self.citation_map[paper_id]
        else:
            # 新的引用
            self.citation_counter += 1
            citation_num = self.citation_counter
            self.citation_map[paper_id] = citation_num
            self.cited_papers.append(paper)
            paper.is_cited = True
            paper.citation_count_in_report += 1
        
        # 生成内联引用
        formatter = self.formatters[self.citation_style]
        citation_format = formatter(paper)
        
        if self.citation_style == 'APA':
            return citation_format['inline']
        else:
            # IEEE和Nature使用数字引用
            return citation_format['inline'].format(citation_num)
    
    def generate_bibliography(self) -> str:
        """
        生成完整的参考文献列表
        
        Returns:
            str: 格式化的参考文献列表
        """
        if not self.cited_papers:
            return "## References\n\nNo references cited.\n"
        
        bibliography_lines = ["## References\n"]
        
        # 根据引用格式排序
        if self.citation_style == 'APA':
            # APA按作者姓氏字母顺序排序
            sorted_papers = sorted(self.cited_papers, key=lambda p: self._get_sort_key(p))
        else:
            # IEEE和Nature按引用顺序排序
            sorted_papers = sorted(self.cited_papers, key=lambda p: self.citation_map[self._get_paper_id(p)])
        
        formatter = self.formatters[self.citation_style]
        
        for i, paper in enumerate(sorted_papers, 1):
            citation_format = formatter(paper)
            
            if self.citation_style == 'APA':
                bibliography_lines.append(f"{citation_format['full']}\n")
            else:
                # IEEE和Nature格式
                citation_num = self.citation_map[self._get_paper_id(paper)]
                bibliography_lines.append(f"[{citation_num}] {citation_format['full']}\n")
        
        return "\n".join(bibliography_lines)
    
    def get_citation_statistics(self) -> Dict[str, Any]:
        """
        获取引用统计信息
        
        Returns:
            Dict: 引用统计信息
        """
        return {
            'total_citations': len(self.cited_papers),
            'citation_style': self.citation_style,
            'most_cited_paper': max(self.cited_papers, key=lambda p: p.citation_count_in_report) if self.cited_papers else None,
            'citation_years': [p.get_year() for p in self.cited_papers if p.get_year()],
            'citation_journals': list(set([p.journal for p in self.cited_papers if p.journal]))
        }
    
    def _get_paper_id(self, paper: Paper) -> str:
        """生成论文的唯一标识符"""
        # 使用标题和第一作者作为唯一标识
        title = paper.title or "untitled"
        first_author = paper.authors[0] if paper.authors else "unknown"
        return f"{first_author}_{title}".lower().replace(" ", "_")
    
    def _get_sort_key(self, paper: Paper) -> str:
        """获取用于排序的键值（主要用于APA格式）"""
        if not paper.authors:
            return "zzz_unknown"
        
        first_author = paper.authors[0]
        if ',' in first_author:
            # "Last, First" 格式
            last_name = first_author.split(',')[0].strip()
        else:
            # "First Last" 格式
            last_name = first_author.split()[-1]
        
        return last_name.lower()
    
    def reset(self):
        """重置引用管理器"""
        self.cited_papers.clear()
        self.citation_map.clear()
        self.citation_counter = 0
