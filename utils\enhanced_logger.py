# utils/enhanced_logger.py
#
# 增强的日志管理器 - 优化控制台输出，详细日志重定向到文件

import logging
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)


class ConsoleProgressHandler(logging.Handler):
    """控制台进度处理器 - 只显示关键进度信息"""
    
    def __init__(self):
        super().__init__()
        self.setLevel(logging.INFO)
        
        # 设置简洁的格式
        formatter = logging.Formatter('%(message)s')
        self.setFormatter(formatter)
    
    def emit(self, record):
        """只处理标记为进度的日志"""
        if hasattr(record, 'progress') and record.progress:
            try:
                msg = self.format(record)
                # 根据日志级别添加颜色
                if record.levelno >= logging.ERROR:
                    msg = f"{Fore.RED}❌ {msg}{Style.RESET_ALL}"
                elif record.levelno >= logging.WARNING:
                    msg = f"{Fore.YELLOW}⚠️  {msg}{Style.RESET_ALL}"
                elif hasattr(record, 'success') and record.success:
                    msg = f"{Fore.GREEN}✅ {msg}{Style.RESET_ALL}"
                elif hasattr(record, 'step') and record.step:
                    msg = f"{Fore.CYAN}🔄 {msg}{Style.RESET_ALL}"
                else:
                    msg = f"{Fore.BLUE}📋 {msg}{Style.RESET_ALL}"
                
                print(msg)
                sys.stdout.flush()
            except Exception:
                self.handleError(record)


class EnhancedLogger:
    """增强的日志管理器"""
    
    def __init__(self, name: str, log_dir: str = "outputs/logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"research_assistant_{timestamp}.log"
        
        # 设置日志器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 添加文件处理器（详细日志）
        self._setup_file_handler()
        
        # 添加控制台处理器（仅关键进度）
        self._setup_console_handler()
        
        # 记录日志文件位置
        self.progress(f"详细日志保存到: {self.log_file}")
    
    def _setup_file_handler(self):
        """设置文件处理器"""
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 详细的文件格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
    
    def _setup_console_handler(self):
        """设置控制台处理器"""
        console_handler = ConsoleProgressHandler()
        self.logger.addHandler(console_handler)
    
    def step(self, message: str):
        """显示执行步骤"""
        record = self.logger.makeRecord(
            self.name, logging.INFO, "", 0, message, (), None
        )
        record.progress = True
        record.step = True
        self.logger.handle(record)
        
        # 同时记录到文件
        self.logger.info(f"STEP: {message}")
    
    def progress(self, message: str):
        """显示进度信息"""
        record = self.logger.makeRecord(
            self.name, logging.INFO, "", 0, message, (), None
        )
        record.progress = True
        self.logger.handle(record)
        
        # 同时记录到文件
        self.logger.info(f"PROGRESS: {message}")
    
    def success(self, message: str):
        """显示成功信息"""
        record = self.logger.makeRecord(
            self.name, logging.INFO, "", 0, message, (), None
        )
        record.progress = True
        record.success = True
        self.logger.handle(record)
        
        # 同时记录到文件
        self.logger.info(f"SUCCESS: {message}")
    
    def warning(self, message: str):
        """显示警告信息"""
        record = self.logger.makeRecord(
            self.name, logging.WARNING, "", 0, message, (), None
        )
        record.progress = True
        self.logger.handle(record)
        
        # 同时记录到文件
        self.logger.warning(message)
    
    def error(self, message: str):
        """显示错误信息"""
        record = self.logger.makeRecord(
            self.name, logging.ERROR, "", 0, message, (), None
        )
        record.progress = True
        self.logger.handle(record)
        
        # 同时记录到文件
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试信息（仅文件）"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """记录信息（仅文件）"""
        self.logger.info(message)
    
    def config_summary(self, config_info: Dict[str, Any]):
        """显示配置摘要"""
        self.progress("系统配置摘要:")
        for key, value in config_info.items():
            self.progress(f"  - {key}: {value}")
    
    def file_info(self, operation: str, file_path: str, size: Optional[int] = None):
        """显示文件操作信息"""
        if size:
            size_mb = size / (1024 * 1024)
            self.success(f"{operation}: {file_path} ({size_mb:.2f} MB)")
        else:
            self.success(f"{operation}: {file_path}")
    
    def phase_start(self, phase_name: str, description: str = ""):
        """开始新阶段"""
        separator = "=" * 60
        self.progress(separator)
        if description:
            self.step(f"{phase_name}: {description}")
        else:
            self.step(phase_name)
        self.progress(separator)
    
    def phase_end(self, phase_name: str, duration: Optional[float] = None):
        """结束阶段"""
        if duration:
            self.success(f"{phase_name} 完成 (耗时: {duration:.1f}秒)")
        else:
            self.success(f"{phase_name} 完成")
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return str(self.log_file)


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, logger: EnhancedLogger, total_steps: int, task_name: str):
        self.logger = logger
        self.total_steps = total_steps
        self.task_name = task_name
        self.current_step = 0
        self.start_time = datetime.now()
    
    def update(self, step_increment: int = 1, message: str = ""):
        """更新进度"""
        self.current_step += step_increment
        progress_percent = (self.current_step / self.total_steps) * 100
        
        elapsed_time = (datetime.now() - self.start_time).total_seconds()
        
        if message:
            self.logger.progress(
                f"{self.task_name}: {message} ({self.current_step}/{self.total_steps}, {progress_percent:.1f}%, {elapsed_time:.1f}s)"
            )
        else:
            self.logger.progress(
                f"{self.task_name}: {self.current_step}/{self.total_steps} ({progress_percent:.1f}%)"
            )
    
    def complete(self):
        """完成进度跟踪"""
        total_time = (datetime.now() - self.start_time).total_seconds()
        self.logger.success(f"{self.task_name} 完成 (总耗时: {total_time:.1f}秒)")


def create_enhanced_logger(name: str) -> EnhancedLogger:
    """创建增强日志器的便捷函数"""
    return EnhancedLogger(name)


def suppress_verbose_loggers():
    """抑制第三方库的冗余日志"""
    # 抑制HTTP请求日志
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    # 抑制其他冗余日志
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("anthropic").setLevel(logging.WARNING)
    
    # 设置根日志器级别
    logging.getLogger().setLevel(logging.WARNING)


# 在模块导入时自动抑制冗余日志
suppress_verbose_loggers()
