# utils/exceptions.py
#
# 自定义异常类和错误处理工具

import logging
import traceback
from typing import Optional, Dict, Any
from functools import wraps


class ResearchAssistantError(Exception):
    """研究助理基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class ConfigurationError(ResearchAssistantError):
    """配置错误"""
    pass


class APIConnectionError(ResearchAssistantError):
    """API连接错误"""
    pass


class LLMError(ResearchAssistantError):
    """LLM相关错误"""
    pass


class SearchError(ResearchAssistantError):
    """搜索相关错误"""
    pass


class PaperAnalysisError(ResearchAssistantError):
    """论文分析错误"""
    pass


class ReportGenerationError(ResearchAssistantError):
    """报告生成错误"""
    pass


class DataValidationError(ResearchAssistantError):
    """数据验证错误"""
    pass


def handle_exceptions(logger: Optional[logging.Logger] = None, 
                     reraise: bool = True,
                     default_return: Any = None):
    """
    异常处理装饰器
    
    Args:
        logger: 日志器实例
        reraise: 是否重新抛出异常
        default_return: 异常时的默认返回值
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ResearchAssistantError as e:
                # 自定义异常，记录详细信息
                if logger:
                    logger.error(f"Custom error in {func.__name__}: {e.message}", 
                               extra={"error_details": e.details})
                if reraise:
                    raise
                return default_return
            except Exception as e:
                # 其他异常，记录堆栈信息
                if logger:
                    logger.error(f"Unexpected error in {func.__name__}: {str(e)}", 
                               exc_info=True)
                if reraise:
                    raise ResearchAssistantError(
                        f"Unexpected error in {func.__name__}: {str(e)}",
                        error_code="UNEXPECTED_ERROR",
                        details={"original_exception": str(e)}
                    )
                return default_return
        return wrapper
    return decorator


def safe_execute(func, *args, logger: Optional[logging.Logger] = None, **kwargs):
    """
    安全执行函数，捕获并记录异常
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        logger: 日志器
        **kwargs: 函数关键字参数
        
    Returns:
        tuple: (success: bool, result: Any, error: Optional[Exception])
    """
    try:
        result = func(*args, **kwargs)
        return True, result, None
    except Exception as e:
        if logger:
            logger.error(f"Error executing {func.__name__}: {str(e)}", exc_info=True)
        return False, None, e


class ErrorCollector:
    """错误收集器，用于收集和管理批处理中的错误"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.logger = logging.getLogger(__name__)
    
    def add_error(self, error: Exception, context: str = ""):
        """添加错误"""
        error_info = {
            "error": error,
            "context": context,
            "timestamp": logging.Formatter().formatTime(logging.LogRecord(
                name="", level=0, pathname="", lineno=0, msg="", args=(), exc_info=None
            ))
        }
        self.errors.append(error_info)
        self.logger.error(f"Error in {context}: {str(error)}")
    
    def add_warning(self, message: str, context: str = ""):
        """添加警告"""
        warning_info = {
            "message": message,
            "context": context,
            "timestamp": logging.Formatter().formatTime(logging.LogRecord(
                name="", level=0, pathname="", lineno=0, msg="", args=(), exc_info=None
            ))
        }
        self.warnings.append(warning_info)
        self.logger.warning(f"Warning in {context}: {message}")
    
    def has_errors(self) -> bool:
        """检查是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """检查是否有警告"""
        return len(self.warnings) > 0
    
    def get_error_count(self) -> int:
        """获取错误数量"""
        return len(self.errors)
    
    def get_warning_count(self) -> int:
        """获取警告数量"""
        return len(self.warnings)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        return {
            "error_count": self.get_error_count(),
            "warning_count": self.get_warning_count(),
            "errors": [
                {
                    "message": str(err["error"]),
                    "context": err["context"],
                    "timestamp": err["timestamp"]
                }
                for err in self.errors
            ],
            "warnings": [
                {
                    "message": warn["message"],
                    "context": warn["context"],
                    "timestamp": warn["timestamp"]
                }
                for warn in self.warnings
            ]
        }
    
    def clear(self):
        """清除所有错误和警告"""
        self.errors.clear()
        self.warnings.clear()


class RetryHandler:
    """重试处理器"""
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
        """
        初始化重试处理器
        
        Args:
            max_retries: 最大重试次数
            delay: 初始延迟时间（秒）
            backoff_factor: 退避因子
        """
        self.max_retries = max_retries
        self.delay = delay
        self.backoff_factor = backoff_factor
        self.logger = logging.getLogger(__name__)
    
    def execute_with_retry(self, func, *args, **kwargs):
        """
        带重试的执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            最后一次执行的异常
        """
        import time
        
        last_exception = None
        current_delay = self.delay
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    self.logger.warning(
                        f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. "
                        f"Retrying in {current_delay} seconds..."
                    )
                    time.sleep(current_delay)
                    current_delay *= self.backoff_factor
                else:
                    self.logger.error(
                        f"All {self.max_retries + 1} attempts failed for {func.__name__}: {str(e)}"
                    )
        
        raise last_exception


def log_function_call(logger: logging.Logger):
    """
    函数调用日志装饰器
    
    Args:
        logger: 日志器实例
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
            try:
                result = func(*args, **kwargs)
                logger.debug(f"{func.__name__} completed successfully")
                return result
            except Exception as e:
                logger.error(f"{func.__name__} failed with error: {str(e)}")
                raise
        return wrapper
    return decorator


def validate_input(validation_func, error_message: str = "Invalid input"):
    """
    输入验证装饰器
    
    Args:
        validation_func: 验证函数，接受函数参数并返回bool
        error_message: 验证失败时的错误消息
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not validation_func(*args, **kwargs):
                raise DataValidationError(error_message)
            return func(*args, **kwargs)
        return wrapper
    return decorator
