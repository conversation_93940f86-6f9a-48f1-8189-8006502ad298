# utils/logger.py
#
# 日志配置工具

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from config import config


def setup_logging(log_level: Optional[str] = None, log_file: Optional[str] = None) -> None:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别，默认使用配置中的级别
        log_file: 日志文件路径，默认使用配置中的路径
    """
    log_level = log_level or config.LOG_LEVEL
    log_file = log_file or config.LOG_FILE
    
    # 创建日志目录
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(exist_ok=True)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('openai').setLevel(logging.WARNING)
    logging.getLogger('anthropic').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)


class ProgressLogger:
    """进度日志器"""
    
    def __init__(self, total_items: int, task_name: str = "Processing"):
        """
        初始化进度日志器
        
        Args:
            total_items: 总项目数
            task_name: 任务名称
        """
        self.total_items = total_items
        self.task_name = task_name
        self.current_item = 0
        self.start_time = datetime.now()
        self.logger = logging.getLogger(__name__)
    
    def update(self, increment: int = 1, message: str = "") -> None:
        """
        更新进度
        
        Args:
            increment: 增量
            message: 附加消息
        """
        self.current_item += increment
        percentage = (self.current_item / self.total_items) * 100
        
        elapsed_time = datetime.now() - self.start_time
        
        if self.current_item > 0:
            avg_time_per_item = elapsed_time.total_seconds() / self.current_item
            remaining_items = self.total_items - self.current_item
            estimated_remaining = avg_time_per_item * remaining_items
            
            progress_msg = (
                f"{self.task_name}: {self.current_item}/{self.total_items} "
                f"({percentage:.1f}%) - 预计剩余: {estimated_remaining:.0f}秒"
            )
        else:
            progress_msg = f"{self.task_name}: {self.current_item}/{self.total_items} ({percentage:.1f}%)"
        
        if message:
            progress_msg += f" - {message}"
        
        self.logger.info(progress_msg)
    
    def complete(self) -> None:
        """标记完成"""
        total_time = datetime.now() - self.start_time
        self.logger.info(
            f"{self.task_name} 完成! 总耗时: {total_time.total_seconds():.1f}秒, "
            f"平均每项: {total_time.total_seconds()/self.total_items:.1f}秒"
        )
