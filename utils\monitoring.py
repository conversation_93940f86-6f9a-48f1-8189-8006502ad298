# utils/monitoring.py
#
# 系统监控和健康检查工具

import time
import logging
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from config import config
from clients import ADSClient, WebSearchClient, LLMClient


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    service: str
    status: str  # "healthy", "unhealthy", "warning"
    message: str
    response_time: Optional[float] = None
    details: Optional[Dict[str, Any]] = None


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.start_time = datetime.now()
        self.metrics = {
            "requests_made": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "papers_analyzed": 0,
            "reports_generated": 0
        }
    
    def record_request(self, success: bool = True):
        """记录请求"""
        self.metrics["requests_made"] += 1
        if success:
            self.metrics["successful_requests"] += 1
        else:
            self.metrics["failed_requests"] += 1
    
    def record_paper_analysis(self):
        """记录论文分析"""
        self.metrics["papers_analyzed"] += 1
    
    def record_report_generation(self):
        """记录报告生成"""
        self.metrics["reports_generated"] += 1
    
    def get_uptime(self) -> timedelta:
        """获取运行时间"""
        return datetime.now() - self.start_time
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "uptime_seconds": self.get_uptime().total_seconds(),
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "disk_percent": disk.percent,
                "disk_free_gb": disk.free / (1024**3),
                "requests_made": self.metrics["requests_made"],
                "successful_requests": self.metrics["successful_requests"],
                "failed_requests": self.metrics["failed_requests"],
                "success_rate": (
                    self.metrics["successful_requests"] / self.metrics["requests_made"] * 100
                    if self.metrics["requests_made"] > 0 else 0
                ),
                "papers_analyzed": self.metrics["papers_analyzed"],
                "reports_generated": self.metrics["reports_generated"]
            }
        except Exception as e:
            self.logger.error(f"Error getting system metrics: {e}")
            return {"error": str(e)}
    
    def check_resource_usage(self) -> List[str]:
        """检查资源使用情况并返回警告"""
        warnings = []
        
        try:
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                warnings.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            # 检查内存使用率
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                warnings.append(f"High memory usage: {memory.percent:.1f}%")
            
            # 检查磁盘使用率
            disk = psutil.disk_usage('/')
            if disk.percent > 90:
                warnings.append(f"High disk usage: {disk.percent:.1f}%")
            
            # 检查请求失败率
            if self.metrics["requests_made"] > 10:
                failure_rate = self.metrics["failed_requests"] / self.metrics["requests_made"] * 100
                if failure_rate > 20:
                    warnings.append(f"High request failure rate: {failure_rate:.1f}%")
        
        except Exception as e:
            warnings.append(f"Error checking resources: {e}")
        
        return warnings


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def check_all_services(self) -> List[HealthCheckResult]:
        """检查所有服务的健康状态"""
        results = []
        
        # 检查配置
        results.append(self._check_configuration())
        
        # 检查ADS API
        results.append(self._check_ads_api())
        
        # 检查网络搜索API
        results.append(self._check_web_search_api())
        
        # 检查LLM API
        results.append(self._check_llm_api())
        
        # 检查文件系统
        results.append(self._check_file_system())
        
        return results
    
    def _check_configuration(self) -> HealthCheckResult:
        """检查配置"""
        try:
            config.validate_config()
            return HealthCheckResult(
                service="Configuration",
                status="healthy",
                message="Configuration is valid"
            )
        except Exception as e:
            return HealthCheckResult(
                service="Configuration",
                status="unhealthy",
                message=f"Configuration error: {str(e)}"
            )
    
    def _check_ads_api(self) -> HealthCheckResult:
        """检查ADS API"""
        if not config.ADS_API_TOKEN:
            return HealthCheckResult(
                service="ADS API",
                status="unhealthy",
                message="ADS API token not configured"
            )
        
        try:
            start_time = time.time()
            client = ADSClient()
            success = client.test_connection()
            response_time = time.time() - start_time
            
            if success:
                return HealthCheckResult(
                    service="ADS API",
                    status="healthy",
                    message="ADS API is accessible",
                    response_time=response_time
                )
            else:
                return HealthCheckResult(
                    service="ADS API",
                    status="unhealthy",
                    message="ADS API connection failed",
                    response_time=response_time
                )
        except Exception as e:
            return HealthCheckResult(
                service="ADS API",
                status="unhealthy",
                message=f"ADS API error: {str(e)}"
            )
    
    def _check_web_search_api(self) -> HealthCheckResult:
        """检查网络搜索API"""
        try:
            start_time = time.time()
            client = WebSearchClient()
            
            if not client.is_available():
                return HealthCheckResult(
                    service="Web Search API",
                    status="warning",
                    message="No web search API configured"
                )
            
            success = client.test_connection()
            response_time = time.time() - start_time
            
            if success:
                return HealthCheckResult(
                    service="Web Search API",
                    status="healthy",
                    message="Web search API is accessible",
                    response_time=response_time
                )
            else:
                return HealthCheckResult(
                    service="Web Search API",
                    status="unhealthy",
                    message="Web search API connection failed",
                    response_time=response_time
                )
        except Exception as e:
            return HealthCheckResult(
                service="Web Search API",
                status="unhealthy",
                message=f"Web search API error: {str(e)}"
            )
    
    def _check_llm_api(self) -> HealthCheckResult:
        """检查LLM API"""
        try:
            start_time = time.time()
            client = LLMClient()
            success = client.test_connection()
            response_time = time.time() - start_time
            
            if success:
                return HealthCheckResult(
                    service="LLM API",
                    status="healthy",
                    message=f"LLM API ({config.LLM_PROVIDER}) is accessible",
                    response_time=response_time
                )
            else:
                return HealthCheckResult(
                    service="LLM API",
                    status="unhealthy",
                    message=f"LLM API ({config.LLM_PROVIDER}) connection failed",
                    response_time=response_time
                )
        except Exception as e:
            return HealthCheckResult(
                service="LLM API",
                status="unhealthy",
                message=f"LLM API error: {str(e)}"
            )
    
    def _check_file_system(self) -> HealthCheckResult:
        """检查文件系统"""
        try:
            import os
            from pathlib import Path
            
            # 检查输出目录
            output_dir = Path(config.OUTPUT_DIR)
            if not output_dir.exists():
                output_dir.mkdir(exist_ok=True)
            
            # 检查写入权限
            test_file = output_dir / "health_check_test.tmp"
            test_file.write_text("test")
            test_file.unlink()
            
            # 检查磁盘空间
            disk_usage = psutil.disk_usage(str(output_dir))
            free_gb = disk_usage.free / (1024**3)
            
            if free_gb < 1:  # 少于1GB
                return HealthCheckResult(
                    service="File System",
                    status="warning",
                    message=f"Low disk space: {free_gb:.1f}GB available",
                    details={"free_space_gb": free_gb}
                )
            
            return HealthCheckResult(
                service="File System",
                status="healthy",
                message=f"File system accessible, {free_gb:.1f}GB available",
                details={"free_space_gb": free_gb}
            )
        
        except Exception as e:
            return HealthCheckResult(
                service="File System",
                status="unhealthy",
                message=f"File system error: {str(e)}"
            )
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        results = self.check_all_services()
        
        healthy_count = sum(1 for r in results if r.status == "healthy")
        warning_count = sum(1 for r in results if r.status == "warning")
        unhealthy_count = sum(1 for r in results if r.status == "unhealthy")
        
        overall_status = "healthy"
        if unhealthy_count > 0:
            overall_status = "unhealthy"
        elif warning_count > 0:
            overall_status = "warning"
        
        return {
            "overall_status": overall_status,
            "healthy_services": healthy_count,
            "warning_services": warning_count,
            "unhealthy_services": unhealthy_count,
            "total_services": len(results),
            "services": [
                {
                    "service": r.service,
                    "status": r.status,
                    "message": r.message,
                    "response_time": r.response_time,
                    "details": r.details
                }
                for r in results
            ],
            "timestamp": datetime.now().isoformat()
        }


# 全局监控实例
system_monitor = SystemMonitor()
health_checker = HealthChecker()
