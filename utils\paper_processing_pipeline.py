# utils/paper_processing_pipeline.py
#
# 论文处理流水线 - 排序和去重流程管理

import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from models import Paper
from agents.paper_ranking_agent import PaperRankingAgent
from utils.enhanced_logger import EnhancedLogger


class PaperProcessingPipeline:
    """论文处理流水线 - 管理排序和去重流程"""
    
    def __init__(self, ranking_agent: PaperRankingAgent):
        """
        初始化论文处理流水线
        
        Args:
            ranking_agent: 论文排序代理
        """
        self.ranking_agent = ranking_agent
        self.logger = EnhancedLogger("PaperProcessingPipeline")
        self.outputs_dir = Path("outputs")
        self.outputs_dir.mkdir(exist_ok=True)
    
    def process_subquestion_papers(self, papers: List[Paper], sub_question: str, 
                                 sub_question_index: int) -> List[Paper]:
        """
        处理单个子问题的论文 - 立即排序
        
        Args:
            papers: 论文列表
            sub_question: 子问题文本
            sub_question_index: 子问题索引
            
        Returns:
            List[Paper]: 排序后的论文列表
        """
        if not papers:
            self.logger.warning(f"No papers to process for sub-question {sub_question_index + 1}")
            return []
        
        try:
            self.logger.progress(f"排序子问题 {sub_question_index + 1} 的 {len(papers)} 篇论文...")
            
            # 使用PaperRankingAgent进行排序
            ranked_papers = self.ranking_agent.rank_papers(papers, sub_question)
            
            # 保存排序结果
            self._save_ranked_papers(ranked_papers, sub_question_index, sub_question)
            
            self.logger.success(f"子问题 {sub_question_index + 1} 论文排序完成: {len(ranked_papers)} 篇")
            
            return ranked_papers
            
        except Exception as e:
            self.logger.error(f"Error processing papers for sub-question {sub_question_index + 1}: {e}")
            return papers  # 返回原始列表作为备选
    
    def merge_and_deduplicate_papers(self, all_ranked_papers: List[Tuple[int, List[Paper]]], 
                                   research_topic: str) -> List[Paper]:
        """
        合并所有子问题的论文并执行全局去重
        
        Args:
            all_ranked_papers: 所有子问题的排序论文列表 [(sub_question_index, papers), ...]
            research_topic: 研究主题
            
        Returns:
            List[Paper]: 去重合并后的最终论文列表
        """
        try:
            self.logger.step("执行全局论文去重合并")
            
            # 收集所有论文
            all_papers = []
            for sub_question_index, papers in all_ranked_papers:
                for paper in papers:
                    # 记录论文来源的子问题
                    paper_dict = paper.to_dict()
                    paper_dict['source_subquestion'] = sub_question_index + 1
                    all_papers.append(paper_dict)
            
            self.logger.progress(f"收集到 {len(all_papers)} 篇论文，开始去重...")
            
            # 执行去重
            deduplicated_papers = self._deduplicate_papers_advanced(all_papers)
            
            # 转换回Paper对象
            final_papers = []
            for paper_dict in deduplicated_papers:
                try:
                    paper = Paper.from_dict(paper_dict)
                    final_papers.append(paper)
                except Exception as e:
                    self.logger.warning(f"Error converting paper dict to Paper object: {e}")
                    continue
            
            # 保存最终结果
            self._save_final_papers(final_papers, research_topic)
            
            self.logger.success(f"全局去重完成: {len(final_papers)} 篇最终论文")
            
            return final_papers
            
        except Exception as e:
            self.logger.error(f"Error in merge and deduplication: {e}")
            # 返回所有论文的简单合并作为备选
            all_papers_simple = []
            for _, papers in all_ranked_papers:
                all_papers_simple.extend(papers)
            return all_papers_simple
    
    def _save_ranked_papers(self, papers: List[Paper], sub_question_index: int, sub_question: str):
        """
        保存子问题的排序结果
        
        Args:
            papers: 排序后的论文列表
            sub_question_index: 子问题索引
            sub_question: 子问题文本
        """
        try:
            filename = f"ranked_papers_{sub_question_index + 1}.json"
            file_path = self.outputs_dir / filename
            
            # 准备保存的数据
            ranked_data = {
                "sub_question_index": sub_question_index + 1,
                "sub_question": sub_question,
                "total_papers": len(papers),
                "ranked_at": datetime.now().isoformat(),
                "papers": []
            }
            
            # 添加论文数据（包含排序分数）
            for i, paper in enumerate(papers):
                paper_dict = paper.to_dict()
                paper_dict['rank'] = i + 1
                # 如果有排序分数，包含进去
                if hasattr(paper, 'ranking_score'):
                    paper_dict['ranking_score'] = paper.ranking_score
                ranked_data["papers"].append(paper_dict)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(ranked_data, f, indent=2, ensure_ascii=False)
            
            self.logger.file_info("保存排序结果", str(file_path))
            
        except Exception as e:
            self.logger.error(f"Error saving ranked papers: {e}")
    
    def _save_final_papers(self, papers: List[Paper], research_topic: str):
        """
        保存最终的去重合并结果
        
        Args:
            papers: 最终论文列表
            research_topic: 研究主题
        """
        try:
            filename = "final_ranked_papers.json"
            file_path = self.outputs_dir / filename
            
            # 准备保存的数据
            final_data = {
                "research_topic": research_topic,
                "total_papers": len(papers),
                "processed_at": datetime.now().isoformat(),
                "papers": []
            }
            
            # 添加论文数据
            for i, paper in enumerate(papers):
                paper_dict = paper.to_dict()
                paper_dict['final_rank'] = i + 1
                # 保留原始排序信息
                if hasattr(paper, 'ranking_score'):
                    paper_dict['ranking_score'] = paper.ranking_score
                final_data["papers"].append(paper_dict)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, indent=2, ensure_ascii=False)
            
            self.logger.file_info("保存最终论文列表", str(file_path))
            
        except Exception as e:
            self.logger.error(f"Error saving final papers: {e}")
    
    def _deduplicate_papers_advanced(self, papers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        高级论文去重 - 基于DOI和标题
        
        Args:
            papers: 论文字典列表
            
        Returns:
            List[Dict]: 去重后的论文列表
        """
        seen_dois = set()
        seen_titles = set()
        deduplicated = []
        
        # 按排序分数排序（如果有的话）
        papers_sorted = sorted(papers, key=lambda x: x.get('ranking_score', 0), reverse=True)
        
        for paper in papers_sorted:
            is_duplicate = False
            
            # 检查DOI重复
            doi = paper.get('doi', '').strip()
            if doi and doi in seen_dois:
                is_duplicate = True
            
            # 检查标题重复
            title = paper.get('title', '').strip().lower()
            if title:
                # 标准化标题
                normalized_title = self._normalize_title_for_dedup(title)
                if normalized_title in seen_titles:
                    is_duplicate = True
                
                # 检查相似标题
                for seen_title in seen_titles:
                    if self._titles_are_similar(normalized_title, seen_title):
                        is_duplicate = True
                        break
            
            if not is_duplicate:
                deduplicated.append(paper)
                if doi:
                    seen_dois.add(doi)
                if title:
                    seen_titles.add(self._normalize_title_for_dedup(title))
        
        return deduplicated
    
    def _normalize_title_for_dedup(self, title: str) -> str:
        """标准化标题用于去重"""
        import re
        
        # 转换为小写并移除多余空格
        normalized = re.sub(r'\s+', ' ', title.lower().strip())
        
        # 移除标点符号
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        # 移除常见前缀
        prefixes = ['a ', 'an ', 'the ']
        for prefix in prefixes:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):]
                break
        
        return normalized
    
    def _titles_are_similar(self, title1: str, title2: str, threshold: float = 0.85) -> bool:
        """检查两个标题是否相似"""
        words1 = set(title1.split())
        words2 = set(title2.split())
        
        if not words1 or not words2:
            return False
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            stats = {
                "ranked_files": [],
                "final_file": None,
                "total_subquestions": 0,
                "total_final_papers": 0
            }
            
            # 统计排序文件
            for file_path in self.outputs_dir.glob("ranked_papers_*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        stats["ranked_files"].append({
                            "file": file_path.name,
                            "sub_question_index": data.get("sub_question_index", 0),
                            "paper_count": data.get("total_papers", 0)
                        })
                        stats["total_subquestions"] += 1
                except Exception:
                    continue
            
            # 统计最终文件
            final_file = self.outputs_dir / "final_ranked_papers.json"
            if final_file.exists():
                try:
                    with open(final_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        stats["final_file"] = {
                            "file": final_file.name,
                            "paper_count": data.get("total_papers", 0)
                        }
                        stats["total_final_papers"] = data.get("total_papers", 0)
                except Exception:
                    pass
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting processing stats: {e}")
            return {"error": str(e)}
