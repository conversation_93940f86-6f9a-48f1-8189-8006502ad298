# utils/report_generator.py
#
# 报告生成工具 - 负责将内容格式化并写入Markdown文件

import os
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path

from config import config
from models import Paper, ResearchSession


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, output_dir: Optional[str] = None):
        """
        初始化报告生成器
        
        Args:
            output_dir: 输出目录，默认使用配置中的目录
        """
        self.output_dir = Path(output_dir or config.OUTPUT_DIR)
        self.logger = logging.getLogger(__name__)
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
    
    def save_research_report(self, content: str, filename: Optional[str] = None) -> str:
        """
        保存研究报告
        
        Args:
            content: 报告内容
            filename: 文件名，默认使用配置中的文件名
            
        Returns:
            str: 保存的文件路径
        """
        filename = filename or config.RESEARCH_REPORT_FILENAME
        filepath = self.output_dir / filename
        
        try:
            # 添加生成时间戳
            timestamped_content = self._add_timestamp_header(content, "研究报告")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(timestamped_content)
            
            self.logger.info(f"Research report saved to: {filepath}")
            return str(filepath)
        
        except Exception as e:
            self.logger.error(f"Error saving research report: {e}")
            raise
    
    def save_research_details(self, content: str, filename: Optional[str] = None) -> str:
        """
        保存研究详细笔记
        
        Args:
            content: 笔记内容
            filename: 文件名，默认使用配置中的文件名
            
        Returns:
            str: 保存的文件路径
        """
        filename = filename or config.RESEARCH_DETAILS_FILENAME
        filepath = self.output_dir / filename
        
        try:
            # 添加生成时间戳
            timestamped_content = self._add_timestamp_header(content, "详细研究笔记")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(timestamped_content)
            
            self.logger.info(f"Research details saved to: {filepath}")
            return str(filepath)
        
        except Exception as e:
            self.logger.error(f"Error saving research details: {e}")
            raise
    
    def save_innovation_proposal(self, content: str, filename: Optional[str] = None) -> str:
        """
        保存创新方案
        
        Args:
            content: 方案内容
            filename: 文件名，默认使用配置中的文件名
            
        Returns:
            str: 保存的文件路径
        """
        filename = filename or config.PROPOSAL_FILENAME
        filepath = self.output_dir / filename
        
        try:
            # 添加生成时间戳
            timestamped_content = self._add_timestamp_header(content, "创新研究方案")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(timestamped_content)
            
            self.logger.info(f"Innovation proposal saved to: {filepath}")
            return str(filepath)
        
        except Exception as e:
            self.logger.error(f"Error saving innovation proposal: {e}")
            raise
    
    def save_feasibility_analysis(self, content: str, filename: Optional[str] = None) -> str:
        """
        保存可行性分析
        
        Args:
            content: 分析内容
            filename: 文件名，默认使用配置中的文件名
            
        Returns:
            str: 保存的文件路径
        """
        filename = filename or config.FEASIBILITY_FILENAME
        filepath = self.output_dir / filename
        
        try:
            # 添加生成时间戳
            timestamped_content = self._add_timestamp_header(content, "可行性分析")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(timestamped_content)
            
            self.logger.info(f"Feasibility analysis saved to: {filepath}")
            return str(filepath)
        
        except Exception as e:
            self.logger.error(f"Error saving feasibility analysis: {e}")
            raise
    
    def append_to_research_details(self, content: str, filename: Optional[str] = None) -> str:
        """
        追加内容到研究详细笔记
        
        Args:
            content: 要追加的内容
            filename: 文件名，默认使用配置中的文件名
            
        Returns:
            str: 文件路径
        """
        filename = filename or config.RESEARCH_DETAILS_FILENAME
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(content)
                f.write('\n\n')  # 添加分隔符
            
            self.logger.info(f"Content appended to research details: {filepath}")
            return str(filepath)
        
        except Exception as e:
            self.logger.error(f"Error appending to research details: {e}")
            raise
    
    def create_batch_paper_notes(self, papers: List[Paper], batch_number: int) -> str:
        """
        为一批论文创建笔记内容
        
        Args:
            papers: 论文列表
            batch_number: 批次号
            
        Returns:
            str: 笔记内容
        """
        notes_sections = []
        
        # 批次标题
        notes_sections.append(f"## 批次 {batch_number} 论文分析\n")
        notes_sections.append(f"*处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
        
        # 为每篇论文创建笔记
        for i, paper in enumerate(papers, 1):
            if paper.has_analysis():
                section = self._create_paper_summary(paper, i)
                notes_sections.append(section)
                notes_sections.append("---\n")
        
        return "\n".join(notes_sections)
    
    def create_session_summary(self, session: ResearchSession) -> Dict[str, Any]:
        """
        创建研究会话摘要
        
        Args:
            session: 研究会话对象
            
        Returns:
            Dict[str, Any]: 会话摘要信息
        """
        stats = session.get_summary_stats()
        timeline_data = session.get_timeline_data()
        
        summary = {
            "topic": session.query.main_topic,
            "execution_mode": config.EXECUTION_MODE,
            "statistics": stats,
            "timeline_entries": len(timeline_data),
            "processing_time": None,
            "status": session.status
        }
        
        if session.completed_at and session.started_at:
            duration = (session.completed_at - session.started_at).total_seconds()
            summary["processing_time"] = f"{duration:.1f} seconds"
        
        return summary
    
    def save_session_summary(self, session: ResearchSession, filename: str = "session_summary.json") -> str:
        """
        保存会话摘要为JSON文件
        
        Args:
            session: 研究会话对象
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        import json
        
        filepath = self.output_dir / filename
        summary = self.create_session_summary(session)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Session summary saved to: {filepath}")
            return str(filepath)
        
        except Exception as e:
            self.logger.error(f"Error saving session summary: {e}")
            raise
    
    def _add_timestamp_header(self, content: str, document_type: str) -> str:
        """
        为文档添加时间戳头部
        
        Args:
            content: 原始内容
            document_type: 文档类型
            
        Returns:
            str: 添加时间戳后的内容
        """
        timestamp = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        execution_mode = config.EXECUTION_MODE
        
        header = f"""---
文档类型: {document_type}
生成时间: {timestamp}
执行模式: {execution_mode}
生成工具: AI驱动的科研助理
---

"""
        
        return header + content
    
    def _create_paper_summary(self, paper: Paper, index: int) -> str:
        """
        创建单篇论文的简要摘要
        
        Args:
            paper: 论文对象
            index: 序号
            
        Returns:
            str: 论文摘要
        """
        sections = []
        
        # 基本信息
        sections.append(f"### {index}. {paper.title}")
        sections.append(f"**作者**: {', '.join(paper.authors[:2])}{'等' if len(paper.authors) > 2 else ''}")
        
        if paper.publication_date:
            sections.append(f"**发表时间**: {paper.publication_date.strftime('%Y年')}")
        
        if paper.journal:
            sections.append(f"**期刊**: {paper.journal}")
        
        # AI分析结果
        if paper.analysis:
            sections.append(f"**核心贡献**: {paper.analysis.short_summary}")
            sections.append(f"**研究方法**: {paper.analysis.methodology}")
        
        sections.append("")
        
        return "\n".join(sections)
    
    def get_output_files(self) -> List[str]:
        """
        获取输出目录中的所有文件
        
        Returns:
            List[str]: 文件路径列表
        """
        try:
            files = []
            for file_path in self.output_dir.iterdir():
                if file_path.is_file():
                    files.append(str(file_path))
            return sorted(files)
        
        except Exception as e:
            self.logger.error(f"Error listing output files: {e}")
            return []
    
    def clean_output_directory(self) -> None:
        """清理输出目录"""
        try:
            for file_path in self.output_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
            self.logger.info("Output directory cleaned")
        
        except Exception as e:
            self.logger.error(f"Error cleaning output directory: {e}")
            raise
