# utils/research_plan_storage.py
#
# 研究计划持久化存储管理器

import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path


class ResearchPlanStorage:
    """研究计划持久化存储管理器"""
    
    def __init__(self, storage_file: str = "saved_research_plans.json"):
        """
        初始化存储管理器
        
        Args:
            storage_file: 存储文件路径
        """
        self.storage_file = Path(storage_file)
        self.logger = logging.getLogger(__name__)
        
        # 确保存储文件存在
        self._ensure_storage_file()
    
    def _ensure_storage_file(self):
        """确保存储文件存在"""
        if not self.storage_file.exists():
            self._create_empty_storage()
            self.logger.info(f"Created new research plan storage file: {self.storage_file}")
    
    def _create_empty_storage(self):
        """创建空的存储文件"""
        empty_data = {
            "version": "1.0",
            "created_at": datetime.now().isoformat(),
            "research_plans": []
        }
        
        with open(self.storage_file, 'w', encoding='utf-8') as f:
            json.dump(empty_data, f, indent=2, ensure_ascii=False)
    
    def save_research_plan(self, main_topic: str, sub_questions: List[str], 
                          general_keywords: List[str], 
                          sub_question_keywords: Dict[str, List[str]]) -> str:
        """
        保存研究计划
        
        Args:
            main_topic: 研究主题
            sub_questions: 子问题列表
            general_keywords: 通用关键词
            sub_question_keywords: 子问题特定关键词映射
            
        Returns:
            str: 计划ID
        """
        try:
            # 生成计划ID
            plan_id = self._generate_plan_id(main_topic)
            
            # 创建研究计划数据
            research_plan = {
                "plan_id": plan_id,
                "main_topic": main_topic,
                "sub_questions": sub_questions,
                "general_keywords": general_keywords,
                "sub_question_keywords": sub_question_keywords,
                "created_at": datetime.now().isoformat(),
                "last_used": datetime.now().isoformat(),
                "usage_count": 1
            }
            
            # 加载现有数据
            data = self._load_storage_data()
            
            # 检查是否已存在相同主题的计划
            existing_plan = self._find_existing_plan(data, main_topic)
            if existing_plan:
                # 更新现有计划
                existing_plan.update({
                    "sub_questions": sub_questions,
                    "general_keywords": general_keywords,
                    "sub_question_keywords": sub_question_keywords,
                    "last_used": datetime.now().isoformat(),
                    "usage_count": existing_plan.get("usage_count", 0) + 1
                })
                self.logger.info(f"Updated existing research plan: {main_topic}")
            else:
                # 添加新计划
                data["research_plans"].append(research_plan)
                self.logger.info(f"Saved new research plan: {main_topic}")
            
            # 保存数据
            self._save_storage_data(data)
            
            return plan_id
            
        except Exception as e:
            self.logger.error(f"Error saving research plan: {e}")
            raise
    
    def load_research_plans(self) -> List[Dict[str, Any]]:
        """
        加载所有研究计划
        
        Returns:
            List[Dict]: 研究计划列表
        """
        try:
            data = self._load_storage_data()
            plans = data.get("research_plans", [])
            
            # 按最后使用时间排序
            plans.sort(key=lambda x: x.get("last_used", ""), reverse=True)
            
            return plans
            
        except Exception as e:
            self.logger.error(f"Error loading research plans: {e}")
            return []
    
    def get_research_plan(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定研究计划
        
        Args:
            plan_id: 计划ID
            
        Returns:
            Optional[Dict]: 研究计划数据
        """
        try:
            data = self._load_storage_data()
            plans = data.get("research_plans", [])
            
            for plan in plans:
                if plan.get("plan_id") == plan_id:
                    # 更新最后使用时间和使用次数
                    plan["last_used"] = datetime.now().isoformat()
                    plan["usage_count"] = plan.get("usage_count", 0) + 1
                    self._save_storage_data(data)
                    
                    return plan
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting research plan {plan_id}: {e}")
            return None
    
    def delete_research_plan(self, plan_id: str) -> bool:
        """
        删除研究计划
        
        Args:
            plan_id: 计划ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            data = self._load_storage_data()
            plans = data.get("research_plans", [])
            
            original_count = len(plans)
            data["research_plans"] = [p for p in plans if p.get("plan_id") != plan_id]
            
            if len(data["research_plans"]) < original_count:
                self._save_storage_data(data)
                self.logger.info(f"Deleted research plan: {plan_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error deleting research plan {plan_id}: {e}")
            return False
    
    def get_plan_summary(self, plan: Dict[str, Any]) -> str:
        """
        获取计划摘要信息
        
        Args:
            plan: 研究计划数据
            
        Returns:
            str: 计划摘要
        """
        main_topic = plan.get("main_topic", "Unknown Topic")
        sub_questions_count = len(plan.get("sub_questions", []))
        keywords_count = len(plan.get("general_keywords", []))
        last_used = plan.get("last_used", "")
        usage_count = plan.get("usage_count", 0)
        
        # 格式化最后使用时间
        try:
            last_used_dt = datetime.fromisoformat(last_used.replace('Z', '+00:00'))
            last_used_str = last_used_dt.strftime("%Y-%m-%d %H:%M")
        except:
            last_used_str = "Unknown"
        
        return f"{main_topic} ({sub_questions_count}个子问题, {keywords_count}个关键词) - 使用{usage_count}次, 最后使用: {last_used_str}"
    
    def _generate_plan_id(self, main_topic: str) -> str:
        """生成计划ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        topic_hash = abs(hash(main_topic)) % 10000
        return f"plan_{timestamp}_{topic_hash:04d}"
    
    def _find_existing_plan(self, data: Dict, main_topic: str) -> Optional[Dict]:
        """查找现有计划"""
        plans = data.get("research_plans", [])
        for plan in plans:
            if plan.get("main_topic") == main_topic:
                return plan
        return None
    
    def _load_storage_data(self) -> Dict[str, Any]:
        """加载存储数据"""
        try:
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            self.logger.warning(f"Error loading storage data: {e}, creating new file")
            self._create_empty_storage()
            return self._load_storage_data()
    
    def _save_storage_data(self, data: Dict[str, Any]):
        """保存存储数据"""
        with open(self.storage_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            data = self._load_storage_data()
            plans = data.get("research_plans", [])
            
            total_plans = len(plans)
            total_usage = sum(plan.get("usage_count", 0) for plan in plans)
            
            if plans:
                most_used = max(plans, key=lambda x: x.get("usage_count", 0))
                most_recent = max(plans, key=lambda x: x.get("last_used", ""))
            else:
                most_used = None
                most_recent = None
            
            return {
                "total_plans": total_plans,
                "total_usage": total_usage,
                "most_used_plan": most_used.get("main_topic") if most_used else None,
                "most_recent_plan": most_recent.get("main_topic") if most_recent else None,
                "storage_file_size": self.storage_file.stat().st_size if self.storage_file.exists() else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {e}")
            return {"error": str(e)}
