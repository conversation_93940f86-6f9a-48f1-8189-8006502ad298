#!/usr/bin/env python3
# verify_agents_health.py
#
# 验证SynthesizerAgent和WriterAgent的健康状态

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_synthesizer_agent_health():
    """测试SynthesizerAgent健康状态"""
    print("🧪 Testing SynthesizerAgent Health")
    print("=" * 60)
    
    try:
        from agents.synthesizer_agent import SynthesizerAgent
        from config import config
        
        print("📋 Creating SynthesizerAgent:")
        synthesizer = SynthesizerAgent()
        
        # 检查初始化
        if hasattr(synthesizer, 'llm_client') and synthesizer.llm_client:
            model = getattr(synthesizer.llm_client, 'model', None)
            expected_model = config.SYNTHESIZER_AGENT_MODEL
            
            print(f"   ✅ Agent created successfully")
            print(f"   ✅ LLM Client: {type(synthesizer.llm_client).__name__}")
            print(f"   ✅ Expected Model: {expected_model}")
            print(f"   ✅ Actual Model: {model}")
            
            if model == expected_model:
                print(f"   ✅ Model configuration CORRECT")
            else:
                print(f"   ❌ Model configuration INCORRECT")
                return False
        else:
            print(f"   ❌ LLM client not properly initialized")
            return False
        
        # 检查关键方法
        key_methods = [
            'synthesize_web_results',
            'analyze_paper',
            'batch_analyze_papers',
            'create_timeline_summary'
        ]
        
        print(f"\n📋 Checking key methods:")
        for method_name in key_methods:
            if hasattr(synthesizer, method_name):
                print(f"   ✅ Method exists: {method_name}")
            else:
                print(f"   ❌ Method missing: {method_name}")
                return False
        
        # 检查配置使用
        print(f"\n📋 Checking configuration usage:")
        try:
            agent_config = config.get_agent_config("synthesizer")
            print(f"   ✅ Agent config accessible")
            print(f"   ✅ Provider: {agent_config.get('provider')}")
            print(f"   ✅ Temperature: {agent_config.get('temperature')}")
            print(f"   ✅ Max Tokens: {agent_config.get('max_tokens')}")
        except Exception as e:
            print(f"   ❌ Configuration error: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SynthesizerAgent health test failed: {e}")
        return False


def test_writer_agent_health():
    """测试WriterAgent健康状态"""
    print("\n🧪 Testing WriterAgent Health")
    print("=" * 60)
    
    try:
        from agents.writer_agent import WriterAgent
        from config import config
        
        print("📋 Creating WriterAgent:")
        writer = WriterAgent()
        
        # 检查初始化
        if hasattr(writer, 'llm_client') and writer.llm_client:
            model = getattr(writer.llm_client, 'model', None)
            expected_model = config.WRITER_AGENT_MODEL
            
            print(f"   ✅ Agent created successfully")
            print(f"   ✅ LLM Client: {type(writer.llm_client).__name__}")
            print(f"   ✅ Expected Model: {expected_model}")
            print(f"   ✅ Actual Model: {model}")
            
            if model == expected_model:
                print(f"   ✅ Model configuration CORRECT")
            else:
                print(f"   ❌ Model configuration INCORRECT")
                return False
        else:
            print(f"   ❌ LLM client not properly initialized")
            return False
        
        # 检查关键方法
        key_methods = [
            'write_research_report',
            'write_innovation_proposal',
            'write_feasibility_analysis',
            'create_detailed_notes'
        ]
        
        print(f"\n📋 Checking key methods:")
        for method_name in key_methods:
            if hasattr(writer, method_name):
                print(f"   ✅ Method exists: {method_name}")
            else:
                print(f"   ❌ Method missing: {method_name}")
                return False
        
        # 检查配置使用
        print(f"\n📋 Checking configuration usage:")
        try:
            agent_config = config.get_agent_config("writer")
            print(f"   ✅ Agent config accessible")
            print(f"   ✅ Provider: {agent_config.get('provider')}")
            print(f"   ✅ Temperature: {agent_config.get('temperature')}")
            print(f"   ✅ Max Tokens: {agent_config.get('max_tokens')}")
        except Exception as e:
            print(f"   ❌ Configuration error: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ WriterAgent health test failed: {e}")
        return False


def test_agents_independence():
    """测试Agent独立性"""
    print("\n🧪 Testing Agents Independence")
    print("=" * 60)
    
    try:
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        from config import config
        
        print("📋 Creating independent agent instances:")
        
        synthesizer = SynthesizerAgent()
        writer = WriterAgent()
        
        synthesizer_model = getattr(synthesizer.llm_client, 'model', None)
        writer_model = getattr(writer.llm_client, 'model', None)
        
        expected_synthesizer = config.SYNTHESIZER_AGENT_MODEL
        expected_writer = config.WRITER_AGENT_MODEL
        
        print(f"   SynthesizerAgent model: {synthesizer_model}")
        print(f"   WriterAgent model: {writer_model}")
        print(f"   Expected synthesizer: {expected_synthesizer}")
        print(f"   Expected writer: {expected_writer}")
        
        # 验证独立性
        independence_correct = (
            synthesizer_model == expected_synthesizer and
            writer_model == expected_writer
        )
        
        if independence_correct:
            print(f"   ✅ Agent independence VERIFIED")
            
            # 检查是否使用不同模型
            if synthesizer_model != writer_model:
                print(f"   ✅ Agents use DIFFERENT models (optimization working)")
            else:
                print(f"   ⚠️  Agents use SAME model (may be intentional)")
            
            return True
        else:
            print(f"   ❌ Agent independence FAILED")
            return False
        
    except Exception as e:
        print(f"❌ Agent independence test failed: {e}")
        return False


def test_no_unpacking_issues():
    """测试无解包问题"""
    print("\n🧪 Testing No Unpacking Issues")
    print("=" * 60)
    
    try:
        # 检查SynthesizerAgent和WriterAgent的源码中是否有危险的解包模式
        agents_to_check = [
            ("SynthesizerAgent", "agents/synthesizer_agent.py"),
            ("WriterAgent", "agents/writer_agent.py")
        ]
        
        dangerous_patterns = [
            r'action,.*=.*result',
            r'confirm.*=.*result',
            r'.*,.*,.*=.*show.*confirmation',
            r'.*,.*,.*=.*dialog'
        ]
        
        all_safe = True
        
        for agent_name, file_path in agents_to_check:
            print(f"\n📋 Checking {agent_name} for dangerous unpacking patterns:")
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                found_issues = []
                
                for i, line in enumerate(lines, 1):
                    line_stripped = line.strip()
                    
                    for pattern in dangerous_patterns:
                        import re
                        if re.search(pattern, line_stripped, re.IGNORECASE):
                            found_issues.append({
                                "line": i,
                                "content": line_stripped,
                                "pattern": pattern
                            })
                
                if found_issues:
                    print(f"   ❌ Found {len(found_issues)} potential issues:")
                    for issue in found_issues:
                        print(f"     Line {issue['line']}: {issue['content']}")
                    all_safe = False
                else:
                    print(f"   ✅ No dangerous unpacking patterns found")
            else:
                print(f"   ⚠️  File not found: {file_path}")
                all_safe = False
        
        return all_safe
        
    except Exception as e:
        print(f"❌ Unpacking issues test failed: {e}")
        return False


def test_error_handling_patterns():
    """测试错误处理模式"""
    print("\n🧪 Testing Error Handling Patterns")
    print("=" * 60)
    
    try:
        from agents.synthesizer_agent import SynthesizerAgent
        from agents.writer_agent import WriterAgent
        
        agents = [
            ("SynthesizerAgent", SynthesizerAgent()),
            ("WriterAgent", WriterAgent())
        ]
        
        all_good = True
        
        for agent_name, agent in agents:
            print(f"\n📋 Checking {agent_name} error handling:")
            
            # 检查是否有logger
            if hasattr(agent, 'logger'):
                print(f"   ✅ Has logger: {agent.logger.name}")
            else:
                print(f"   ❌ No logger found")
                all_good = False
            
            # 检查是否有LLM客户端
            if hasattr(agent, 'llm_client'):
                print(f"   ✅ Has LLM client")
            else:
                print(f"   ❌ No LLM client")
                all_good = False
            
            # 检查关键方法是否存在
            if agent_name == "SynthesizerAgent":
                key_method = "analyze_paper"
            else:  # WriterAgent
                key_method = "write_research_report"
            
            if hasattr(agent, key_method):
                print(f"   ✅ Has key method: {key_method}")
            else:
                print(f"   ❌ Missing key method: {key_method}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error handling patterns test failed: {e}")
        return False


def main():
    """运行所有健康检查"""
    print("🚀 Agents Health Verification")
    print("=" * 80)
    print("Verifying that SynthesizerAgent and WriterAgent are free from")
    print("'too many values to unpack' issues and working correctly")
    
    tests = [
        ("SynthesizerAgent Health", test_synthesizer_agent_health),
        ("WriterAgent Health", test_writer_agent_health),
        ("Agents Independence", test_agents_independence),
        ("No Unpacking Issues", test_no_unpacking_issues),
        ("Error Handling Patterns", test_error_handling_patterns),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎉 Health Verification Results")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL AGENTS HEALTHY!")
        print("✅ SynthesizerAgent and WriterAgent are working correctly")
        print("✅ No 'too many values to unpack' issues found")
        print("✅ All agents use their designated models")
        print("✅ Configuration and initialization working properly")
    else:
        print("\n⚠️  Some health checks failed")
        print("🔧 Review the failed tests above")
    
    print("\n💡 Key Verification Points:")
    print("1. ✅ No confirmation dialogs in SynthesizerAgent/WriterAgent")
    print("2. ✅ No dangerous tuple unpacking patterns")
    print("3. ✅ Proper agent-specific LLM client initialization")
    print("4. ✅ Correct model configuration usage")
    print("5. ✅ Standard error handling patterns")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
