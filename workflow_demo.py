#!/usr/bin/env python3
# workflow_demo.py
#
# AI研究助理系统工作流程演示

import os
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from agents import PlannerAgent, SynthesizerAgent, WriterAgent
from models import Paper, PaperAnalysis, ResearchSession


class WorkflowDemo:
    """工作流程演示类"""
    
    def __init__(self):
        self.demo_topic = "Deep Learning Applications in Astrophysics"
        
    def demonstrate_complete_workflow(self):
        """演示完整工作流程"""
        print("🚀 AI研究助理系统工作流程演示")
        print("=" * 80)
        print(f"📋 演示主题: {self.demo_topic}")
        print(f"⚙️  当前配置:")
        print(f"   - 最大论文数: {config.MAX_PAPERS_TO_ANALYZE}")
        print(f"   - 批量分析大小: {config.PAPERS_PER_ANALYSIS_BATCH}")
        print(f"   - 传统批处理大小: {config.PAPER_BATCH_SIZE}")
        print(f"   - SynthesizerAgent TOKEN限制: {config.SYNTHESIZER_MAX_TOKENS}")
        print()
        
        # 阶段1: 研究规划
        self.demo_planning_phase()
        
        # 阶段2: 信息采集
        self.demo_information_collection()
        
        # 阶段3: 论文分析 (重点演示SynthesizerAgent)
        self.demo_paper_analysis()
        
        # 阶段4: 报告生成
        self.demo_report_generation()
        
        # 总结
        self.demo_summary()
    
    def demo_planning_phase(self):
        """演示规划阶段"""
        print("📋 阶段1: 研究规划 (PlannerAgent)")
        print("-" * 50)
        
        try:
            # 创建模拟的PlannerAgent
            class MockPlannerAgent:
                def generate_research_plan(self, topic):
                    # 模拟AI生成的研究计划
                    from models import ResearchQuery
                    
                    query = ResearchQuery(
                        main_topic=topic,
                        sub_questions=[
                            "What are the fundamental applications of deep learning in astrophysics?",
                            "How do convolutional neural networks help in galaxy classification?",
                            "What role does deep learning play in exoplanet detection?",
                            "How are recurrent neural networks used in time-series analysis of stellar data?",
                            "What are the recent breakthroughs in using deep learning for gravitational wave detection?",
                            "How does deep learning assist in cosmic ray identification and classification?",
                            "What are the challenges and limitations of applying deep learning to astrophysical data?"
                        ]
                    )
                    
                    # 添加关键词
                    keywords = [
                        "deep learning astrophysics", "neural networks astronomy", "CNN galaxy classification",
                        "exoplanet detection ML", "RNN stellar analysis", "gravitational wave deep learning",
                        "cosmic ray identification", "astronomical data mining", "machine learning cosmology",
                        "AI stellar classification", "deep learning telescope data", "neural network sky survey"
                    ]
                    
                    for keyword in keywords:
                        query.add_keyword(keyword)
                    
                    # 添加子问题关键词映射
                    query.sub_question_keywords = {
                        "sub_question_1": ["deep learning applications", "astrophysics fundamentals", "AI astronomy"],
                        "sub_question_2": ["CNN", "galaxy classification", "image recognition"],
                        "sub_question_3": ["exoplanet detection", "transit photometry", "machine learning"],
                        "sub_question_4": ["RNN", "time series", "stellar variability"],
                        "sub_question_5": ["gravitational waves", "LIGO", "deep learning detection"],
                        "sub_question_6": ["cosmic rays", "particle identification", "classification"],
                        "sub_question_7": ["challenges", "limitations", "data quality"]
                    }
                    
                    return query
            
            planner = MockPlannerAgent()
            
            print("🤖 调用PlannerAgent.generate_research_plan()...")
            start_time = time.time()
            
            research_query = planner.generate_research_plan(self.demo_topic)
            
            end_time = time.time()
            
            print(f"✅ 规划完成 (耗时: {end_time - start_time:.1f}秒)")
            print(f"📊 生成结果:")
            print(f"   - 子问题数量: {len(research_query.sub_questions)}")
            print(f"   - 关键词数量: {len(research_query.keywords)}")
            print(f"   - 子问题关键词映射: {len(research_query.sub_question_keywords)}组")
            
            print(f"\n📝 生成的子问题 (前3个):")
            for i, question in enumerate(research_query.sub_questions[:3], 1):
                print(f"   {i}. {question}")
            
            print(f"\n🔑 生成的关键词 (前6个):")
            for i, keyword in enumerate(research_query.keywords[:6], 1):
                print(f"   {i}. {keyword}")
            
            self.research_query = research_query
            
        except Exception as e:
            print(f"❌ 规划阶段演示失败: {e}")
        
        print()
    
    def demo_information_collection(self):
        """演示信息采集阶段"""
        print("🔍 阶段2: 双轨信息采集")
        print("-" * 50)
        
        print("📡 轨道1: 网络搜索")
        print(f"   - 对{len(self.research_query.sub_questions)}个子问题执行网络搜索")
        print(f"   - 每个子问题获取5个搜索结果")
        print(f"   - 预估获得{len(self.research_query.sub_questions) * 5}个网络结果")
        
        print("\n📚 轨道2: 学术搜索 (ADS)")
        print("   - 增强检索策略:")
        print(f"     * 每个子问题检索100篇论文")
        print(f"     * 按引用数排序选择前30篇")
        print(f"     * 按时间排序选择最近30篇")
        print(f"     * 去重处理")
        print(f"   - 预估获得{config.MAX_PAPERS_TO_ANALYZE}篇论文")
        
        # 创建模拟论文数据
        self.demo_papers = []
        for i in range(config.MAX_PAPERS_TO_ANALYZE):
            paper = Paper(
                title=f"Deep Learning Application in Astrophysics - Paper {i+1}",
                authors=[f"Author {i+1}A", f"Author {i+1}B"],
                abstract=f"This paper presents a novel application of deep learning techniques to astrophysical data analysis. We demonstrate the effectiveness of our approach on large-scale astronomical datasets. The method shows significant improvements over traditional approaches. Paper {i+1} focuses on specific aspects of the problem.",
                publication_date=datetime(2023, 1 + (i % 12), 1),
                journal=f"Astrophysical Journal {i % 5 + 1}",
                citation_count=100 - i,
                source="ads"
            )
            self.demo_papers.append(paper)
        
        print(f"\n✅ 信息采集完成")
        print(f"   - 网络结果: {len(self.research_query.sub_questions) * 5}个")
        print(f"   - 学术论文: {len(self.demo_papers)}篇")
        print()
    
    def demo_paper_analysis(self):
        """演示论文分析阶段 (重点展示SynthesizerAgent)"""
        print("🔬 阶段3: 论文深度分析 (SynthesizerAgent)")
        print("-" * 50)
        
        print("📊 批量分析配置:")
        print(f"   - 论文总数: {len(self.demo_papers)}")
        print(f"   - 批量大小: {config.PAPERS_PER_ANALYSIS_BATCH}篇/批")
        print(f"   - TOKEN限制: {config.SYNTHESIZER_MAX_TOKENS:,}")
        
        # 计算批次信息
        batch_size = config.PAPERS_PER_ANALYSIS_BATCH
        total_batches = (len(self.demo_papers) + batch_size - 1) // batch_size
        
        print(f"\n🔄 批处理计算:")
        print(f"   - 总批次数: {total_batches}")
        print(f"   - API调用次数: {total_batches} (vs 传统方法的{len(self.demo_papers)}次)")
        print(f"   - 效率提升: {(1 - total_batches/len(self.demo_papers))*100:.1f}%")
        print(f"   - 预估处理时间: {total_batches * 0.5:.1f}分钟")
        
        print(f"\n🚀 开始批量分析演示...")
        
        # 模拟批量分析过程
        analyzed_papers = []
        successful_analyses = 0
        
        for i in range(0, len(self.demo_papers), batch_size):
            batch = self.demo_papers[i:i + batch_size]
            batch_number = i // batch_size + 1
            
            print(f"   📦 处理第{batch_number}批: {len(batch)}篇论文")
            
            # 模拟AI分析
            time.sleep(0.1)  # 模拟处理时间
            
            # 为每篇论文创建模拟分析结果
            for j, paper in enumerate(batch):
                try:
                    # 模拟成功分析 (95%成功率)
                    if (i + j) % 20 != 0:  # 5%失败率
                        analysis = PaperAnalysis(
                            short_summary=f"This paper presents innovative deep learning approaches for astrophysical data analysis, focusing on {paper.title.split('-')[-1].strip()}.",
                            relevance_to_topic="Highly relevant to deep learning applications in astrophysics",
                            research_subject="Astronomical data analysis using neural networks",
                            methodology="Deep learning with convolutional and recurrent neural networks",
                            data_used="Large-scale astronomical survey data and simulated datasets",
                            key_findings_or_results=[
                                f"Achieved {90 + (i+j) % 10}% accuracy in classification tasks",
                                "Demonstrated significant improvement over traditional methods",
                                "Identified novel patterns in astronomical data"
                            ]
                        )
                        paper.analysis = analysis
                        successful_analyses += 1
                        
                except Exception as e:
                    print(f"     ⚠️  论文{i+j+1}分析失败: {e}")
            
            analyzed_papers.extend(batch)
            
            # 显示进度
            progress = (batch_number / total_batches) * 100
            print(f"     ✅ 批次{batch_number}完成 (总进度: {progress:.1f}%)")
        
        print(f"\n📈 分析结果统计:")
        print(f"   - 成功分析: {successful_analyses}/{len(self.demo_papers)} ({successful_analyses/len(self.demo_papers)*100:.1f}%)")
        print(f"   - 失败数量: {len(self.demo_papers) - successful_analyses}")
        print(f"   - 实际API调用: {total_batches}次")
        print(f"   - 成本节约: {(1 - total_batches/len(self.demo_papers))*100:.1f}%")
        
        self.analyzed_papers = analyzed_papers
        print()
    
    def demo_report_generation(self):
        """演示报告生成阶段"""
        print("📝 阶段4: 报告生成 (WriterAgent)")
        print("-" * 50)
        
        print("🤖 调用WriterAgent生成研究报告...")
        print(f"   - 输入数据: {len(self.analyzed_papers)}篇已分析论文")
        print(f"   - TOKEN限制: {config.WRITER_MAX_TOKENS:,}")
        print(f"   - 预估报告长度: 15,000-25,000字")
        
        # 模拟报告生成
        time.sleep(0.2)
        
        print("✅ 报告生成完成")
        print("📄 输出文件:")
        print("   - research_report.md (主要研究报告)")
        print("   - research_details.md (详细分析笔记)")
        
        if config.EXECUTION_MODE == "full_analysis":
            print("   - proposal.md (创新方案建议)")
            print("   - feasibility_analysis.md (可行性分析)")
        
        print()
    
    def demo_summary(self):
        """演示总结"""
        print("🎉 工作流程演示完成")
        print("=" * 80)
        
        print("📊 整体性能统计:")
        print(f"   - 处理论文数量: {config.MAX_PAPERS_TO_ANALYZE}篇")
        print(f"   - 生成子问题: {len(self.research_query.sub_questions)}个")
        print(f"   - 生成关键词: {len(self.research_query.keywords)}个")
        print(f"   - API调用优化: 减少{(1 - (config.MAX_PAPERS_TO_ANALYZE // config.PAPERS_PER_ANALYSIS_BATCH)/config.MAX_PAPERS_TO_ANALYZE)*100:.1f}%")
        
        print(f"\n🏆 关键优化成果:")
        print(f"   - SynthesizerAgent批量分析: 8篇/批次")
        print(f"   - TOKEN限制大幅提升: 32K tokens")
        print(f"   - 容错机制完善: 多层容错保障")
        print(f"   - 处理能力提升: 支持{config.MAX_PAPERS_TO_ANALYZE}篇论文")
        
        print(f"\n🔧 系统配置:")
        print(f"   - 执行模式: {getattr(config, 'EXECUTION_MODE', 'deep_research')}")
        print(f"   - LLM提供商: {config.DEFAULT_LLM_PROVIDER}")
        print(f"   - 批量分析大小: {config.PAPERS_PER_ANALYSIS_BATCH}")
        print(f"   - 最大论文数: {config.MAX_PAPERS_TO_ANALYZE}")


def main():
    """运行工作流程演示"""
    demo = WorkflowDemo()
    demo.demonstrate_complete_workflow()


if __name__ == "__main__":
    main()
